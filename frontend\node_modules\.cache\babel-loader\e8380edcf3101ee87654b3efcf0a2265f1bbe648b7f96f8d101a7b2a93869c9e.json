{"ast": null, "code": "import * as React from 'react';\nimport classNames from 'classnames';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default (className => /*#__PURE__*/React.forwardRef((p, ref) => /*#__PURE__*/_jsx(\"div\", {\n  ...p,\n  ref: ref,\n  className: classNames(p.className, className)\n})));", "map": {"version": 3, "names": ["React", "classNames", "jsx", "_jsx", "className", "forwardRef", "p", "ref"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/divWithClassName.js"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default (className => /*#__PURE__*/React.forwardRef((p, ref) => /*#__PURE__*/_jsx(\"div\", {\n  ...p,\n  ref: ref,\n  className: classNames(p.className, className)\n})));"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,gBAAgBC,SAAS,IAAI,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK,aAAaJ,IAAI,CAAC,KAAK,EAAE;EAC9F,GAAGG,CAAC;EACJC,GAAG,EAAEA,GAAG;EACRH,SAAS,EAAEH,UAAU,CAACK,CAAC,CAACF,SAAS,EAAEA,SAAS;AAC9C,CAAC,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}