{"ast": null, "code": "const _excluded = [\"as\", \"active\", \"eventKey\"];\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport NavContext from './NavContext';\nimport SelectableContext, { makeEventKey } from './SelectableContext';\nimport Button from './Button';\nimport { dataAttr } from './DataKey';\nimport TabContext from './TabContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useNavItem(_ref2) {\n  let {\n    key,\n    onClick,\n    active,\n    id,\n    role,\n    disabled\n  } = _ref2;\n  const parentOnSelect = useContext(SelectableContext);\n  const navContext = useContext(NavContext);\n  const tabContext = useContext(TabContext);\n  let isActive = active;\n  const props = {\n    role\n  };\n  if (navContext) {\n    if (!role && navContext.role === 'tablist') props.role = 'tab';\n    const contextControllerId = navContext.getControllerId(key != null ? key : null);\n    const contextControlledId = navContext.getControlledId(key != null ? key : null);\n\n    // @ts-ignore\n    props[dataAttr('event-key')] = key;\n    props.id = contextControllerId || id;\n    isActive = active == null && key != null ? navContext.activeKey === key : active;\n\n    /**\n     * Simplified scenario for `mountOnEnter`.\n     *\n     * While it would make sense to keep 'aria-controls' for tabs that have been mounted at least\n     * once, it would also complicate the code quite a bit, for very little gain.\n     * The following implementation is probably good enough.\n     *\n     * @see https://github.com/react-restart/ui/pull/40#issuecomment-1009971561\n     */\n    if (isActive || !(tabContext != null && tabContext.unmountOnExit) && !(tabContext != null && tabContext.mountOnEnter)) props['aria-controls'] = contextControlledId;\n  }\n  if (props.role === 'tab') {\n    props['aria-selected'] = isActive;\n    if (!isActive) {\n      props.tabIndex = -1;\n    }\n    if (disabled) {\n      props.tabIndex = -1;\n      props['aria-disabled'] = true;\n    }\n  }\n  props.onClick = useEventCallback(e => {\n    if (disabled) return;\n    onClick == null ? void 0 : onClick(e);\n    if (key == null) {\n      return;\n    }\n    if (parentOnSelect && !e.isPropagationStopped()) {\n      parentOnSelect(key, e);\n    }\n  });\n  return [props, {\n    isActive\n  }];\n}\nconst NavItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      as: Component = Button,\n      active,\n      eventKey\n    } = _ref,\n    options = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [props, meta] = useNavItem(Object.assign({\n    key: makeEventKey(eventKey, options.href),\n    active\n  }, options));\n\n  // @ts-ignore\n  props[dataAttr('active')] = meta.isActive;\n  return /*#__PURE__*/_jsx(Component, Object.assign({}, options, props, {\n    ref: ref\n  }));\n});\nNavItem.displayName = 'NavItem';\nexport default NavItem;", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "source", "excluded", "target", "sourceKeys", "Object", "keys", "key", "i", "length", "indexOf", "React", "useContext", "useEventCallback", "NavContext", "SelectableContext", "makeEventKey", "<PERSON><PERSON>", "dataAttr", "TabContext", "jsx", "_jsx", "useNavItem", "_ref2", "onClick", "active", "id", "role", "disabled", "parentOnSelect", "navContext", "tabContext", "isActive", "props", "contextControllerId", "getControllerId", "contextControlledId", "getControlledId", "active<PERSON><PERSON>", "unmountOnExit", "mountOnEnter", "tabIndex", "e", "isPropagationStopped", "NavItem", "forwardRef", "_ref", "ref", "as", "Component", "eventKey", "options", "meta", "assign", "href", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/ui/esm/NavItem.js"], "sourcesContent": ["const _excluded = [\"as\", \"active\", \"eventKey\"];\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport NavContext from './NavContext';\nimport SelectableContext, { makeEventKey } from './SelectableContext';\nimport Button from './Button';\nimport { dataAttr } from './DataKey';\nimport TabContext from './TabContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useNavItem({\n  key,\n  onClick,\n  active,\n  id,\n  role,\n  disabled\n}) {\n  const parentOnSelect = useContext(SelectableContext);\n  const navContext = useContext(NavContext);\n  const tabContext = useContext(TabContext);\n  let isActive = active;\n  const props = {\n    role\n  };\n  if (navContext) {\n    if (!role && navContext.role === 'tablist') props.role = 'tab';\n    const contextControllerId = navContext.getControllerId(key != null ? key : null);\n    const contextControlledId = navContext.getControlledId(key != null ? key : null);\n\n    // @ts-ignore\n    props[dataAttr('event-key')] = key;\n    props.id = contextControllerId || id;\n    isActive = active == null && key != null ? navContext.activeKey === key : active;\n\n    /**\n     * Simplified scenario for `mountOnEnter`.\n     *\n     * While it would make sense to keep 'aria-controls' for tabs that have been mounted at least\n     * once, it would also complicate the code quite a bit, for very little gain.\n     * The following implementation is probably good enough.\n     *\n     * @see https://github.com/react-restart/ui/pull/40#issuecomment-1009971561\n     */\n    if (isActive || !(tabContext != null && tabContext.unmountOnExit) && !(tabContext != null && tabContext.mountOnEnter)) props['aria-controls'] = contextControlledId;\n  }\n  if (props.role === 'tab') {\n    props['aria-selected'] = isActive;\n    if (!isActive) {\n      props.tabIndex = -1;\n    }\n    if (disabled) {\n      props.tabIndex = -1;\n      props['aria-disabled'] = true;\n    }\n  }\n  props.onClick = useEventCallback(e => {\n    if (disabled) return;\n    onClick == null ? void 0 : onClick(e);\n    if (key == null) {\n      return;\n    }\n    if (parentOnSelect && !e.isPropagationStopped()) {\n      parentOnSelect(key, e);\n    }\n  });\n  return [props, {\n    isActive\n  }];\n}\nconst NavItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      as: Component = Button,\n      active,\n      eventKey\n    } = _ref,\n    options = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [props, meta] = useNavItem(Object.assign({\n    key: makeEventKey(eventKey, options.href),\n    active\n  }, options));\n\n  // @ts-ignore\n  props[dataAttr('active')] = meta.isActive;\n  return /*#__PURE__*/_jsx(Component, Object.assign({}, options, props, {\n    ref: ref\n  }));\n});\nNavItem.displayName = 'NavItem';\nexport default NavItem;"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC;AAC9C,SAASC,6BAA6BA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC;EAAE,IAAIM,GAAG,EAAEC,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IAAED,GAAG,GAAGH,UAAU,CAACI,CAAC,CAAC;IAAE,IAAIN,QAAQ,CAACQ,OAAO,CAACH,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUJ,MAAM,CAACI,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC;EAAE;EAAE,OAAOJ,MAAM;AAAE;AAClT,OAAO,KAAKQ,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,iBAAiB,IAAIC,YAAY,QAAQ,qBAAqB;AACrE,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,UAAUA,CAAAC,KAAA,EAOvB;EAAA,IAPwB;IACzBhB,GAAG;IACHiB,OAAO;IACPC,MAAM;IACNC,EAAE;IACFC,IAAI;IACJC;EACF,CAAC,GAAAL,KAAA;EACC,MAAMM,cAAc,GAAGjB,UAAU,CAACG,iBAAiB,CAAC;EACpD,MAAMe,UAAU,GAAGlB,UAAU,CAACE,UAAU,CAAC;EACzC,MAAMiB,UAAU,GAAGnB,UAAU,CAACO,UAAU,CAAC;EACzC,IAAIa,QAAQ,GAAGP,MAAM;EACrB,MAAMQ,KAAK,GAAG;IACZN;EACF,CAAC;EACD,IAAIG,UAAU,EAAE;IACd,IAAI,CAACH,IAAI,IAAIG,UAAU,CAACH,IAAI,KAAK,SAAS,EAAEM,KAAK,CAACN,IAAI,GAAG,KAAK;IAC9D,MAAMO,mBAAmB,GAAGJ,UAAU,CAACK,eAAe,CAAC5B,GAAG,IAAI,IAAI,GAAGA,GAAG,GAAG,IAAI,CAAC;IAChF,MAAM6B,mBAAmB,GAAGN,UAAU,CAACO,eAAe,CAAC9B,GAAG,IAAI,IAAI,GAAGA,GAAG,GAAG,IAAI,CAAC;;IAEhF;IACA0B,KAAK,CAACf,QAAQ,CAAC,WAAW,CAAC,CAAC,GAAGX,GAAG;IAClC0B,KAAK,CAACP,EAAE,GAAGQ,mBAAmB,IAAIR,EAAE;IACpCM,QAAQ,GAAGP,MAAM,IAAI,IAAI,IAAIlB,GAAG,IAAI,IAAI,GAAGuB,UAAU,CAACQ,SAAS,KAAK/B,GAAG,GAAGkB,MAAM;;IAEhF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIO,QAAQ,IAAI,EAAED,UAAU,IAAI,IAAI,IAAIA,UAAU,CAACQ,aAAa,CAAC,IAAI,EAAER,UAAU,IAAI,IAAI,IAAIA,UAAU,CAACS,YAAY,CAAC,EAAEP,KAAK,CAAC,eAAe,CAAC,GAAGG,mBAAmB;EACrK;EACA,IAAIH,KAAK,CAACN,IAAI,KAAK,KAAK,EAAE;IACxBM,KAAK,CAAC,eAAe,CAAC,GAAGD,QAAQ;IACjC,IAAI,CAACA,QAAQ,EAAE;MACbC,KAAK,CAACQ,QAAQ,GAAG,CAAC,CAAC;IACrB;IACA,IAAIb,QAAQ,EAAE;MACZK,KAAK,CAACQ,QAAQ,GAAG,CAAC,CAAC;MACnBR,KAAK,CAAC,eAAe,CAAC,GAAG,IAAI;IAC/B;EACF;EACAA,KAAK,CAACT,OAAO,GAAGX,gBAAgB,CAAC6B,CAAC,IAAI;IACpC,IAAId,QAAQ,EAAE;IACdJ,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACkB,CAAC,CAAC;IACrC,IAAInC,GAAG,IAAI,IAAI,EAAE;MACf;IACF;IACA,IAAIsB,cAAc,IAAI,CAACa,CAAC,CAACC,oBAAoB,EAAE,EAAE;MAC/Cd,cAAc,CAACtB,GAAG,EAAEmC,CAAC,CAAC;IACxB;EACF,CAAC,CAAC;EACF,OAAO,CAACT,KAAK,EAAE;IACbD;EACF,CAAC,CAAC;AACJ;AACA,MAAMY,OAAO,GAAG,aAAajC,KAAK,CAACkC,UAAU,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;EAC3D,IAAI;MACAC,EAAE,EAAEC,SAAS,GAAGhC,MAAM;MACtBQ,MAAM;MACNyB;IACF,CAAC,GAAGJ,IAAI;IACRK,OAAO,GAAGnD,6BAA6B,CAAC8C,IAAI,EAAE/C,SAAS,CAAC;EAC1D,MAAM,CAACkC,KAAK,EAAEmB,IAAI,CAAC,GAAG9B,UAAU,CAACjB,MAAM,CAACgD,MAAM,CAAC;IAC7C9C,GAAG,EAAES,YAAY,CAACkC,QAAQ,EAAEC,OAAO,CAACG,IAAI,CAAC;IACzC7B;EACF,CAAC,EAAE0B,OAAO,CAAC,CAAC;;EAEZ;EACAlB,KAAK,CAACf,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAGkC,IAAI,CAACpB,QAAQ;EACzC,OAAO,aAAaX,IAAI,CAAC4B,SAAS,EAAE5C,MAAM,CAACgD,MAAM,CAAC,CAAC,CAAC,EAAEF,OAAO,EAAElB,KAAK,EAAE;IACpEc,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFH,OAAO,CAACW,WAAW,GAAG,SAAS;AAC/B,eAAeX,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}