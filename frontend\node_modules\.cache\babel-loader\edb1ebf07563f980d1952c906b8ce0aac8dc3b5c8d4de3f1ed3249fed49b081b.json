{"ast": null, "code": "var rHyphen = /-(.)/g;\nexport default function camelize(string) {\n  return string.replace(rHyphen, function (_, chr) {\n    return chr.toUpperCase();\n  });\n}", "map": {"version": 3, "names": ["rHyphen", "camelize", "string", "replace", "_", "chr", "toUpperCase"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/dom-helpers/esm/camelize.js"], "sourcesContent": ["var rHyphen = /-(.)/g;\nexport default function camelize(string) {\n  return string.replace(rHyphen, function (_, chr) {\n    return chr.toUpperCase();\n  });\n}"], "mappings": "AAAA,IAAIA,OAAO,GAAG,OAAO;AACrB,eAAe,SAASC,QAAQA,CAACC,MAAM,EAAE;EACvC,OAAOA,MAAM,CAACC,OAAO,CAACH,OAAO,EAAE,UAAUI,CAAC,EAAEC,GAAG,EAAE;IAC/C,OAAOA,GAAG,CAACC,WAAW,EAAE;EAC1B,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}