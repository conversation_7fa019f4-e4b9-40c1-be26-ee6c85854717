{"ast": null, "code": "const _excluded = [\"enabled\", \"placement\", \"strategy\", \"modifiers\"];\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport { dequal } from 'dequal';\nimport useSafeState from '@restart/hooks/useSafeState';\nimport { createPopper } from './popper';\nconst disabledApplyStylesModifier = {\n  name: 'applyStyles',\n  enabled: false,\n  phase: 'afterWrite',\n  fn: () => undefined\n};\n\n// until docjs supports type exports...\n\nconst ariaDescribedByModifier = {\n  name: 'ariaDescribedBy',\n  enabled: true,\n  phase: 'afterWrite',\n  effect: _ref2 => {\n    let {\n      state\n    } = _ref2;\n    return () => {\n      const {\n        reference,\n        popper\n      } = state.elements;\n      if ('removeAttribute' in reference) {\n        const ids = (reference.getAttribute('aria-describedby') || '').split(',').filter(id => id.trim() !== popper.id);\n        if (!ids.length) reference.removeAttribute('aria-describedby');else reference.setAttribute('aria-describedby', ids.join(','));\n      }\n    };\n  },\n  fn: _ref3 => {\n    let {\n      state\n    } = _ref3;\n    var _popper$getAttribute;\n    const {\n      popper,\n      reference\n    } = state.elements;\n    const role = (_popper$getAttribute = popper.getAttribute('role')) == null ? void 0 : _popper$getAttribute.toLowerCase();\n    if (popper.id && role === 'tooltip' && 'setAttribute' in reference) {\n      const ids = reference.getAttribute('aria-describedby');\n      if (ids && ids.split(',').indexOf(popper.id) !== -1) {\n        return;\n      }\n      reference.setAttribute('aria-describedby', ids ? `${ids},${popper.id}` : popper.id);\n    }\n  }\n};\nconst EMPTY_MODIFIERS = [];\n/**\n * Position an element relative some reference element using Popper.js\n *\n * @param referenceElement\n * @param popperElement\n * @param {object}      options\n * @param {object=}     options.modifiers Popper.js modifiers\n * @param {boolean=}    options.enabled toggle the popper functionality on/off\n * @param {string=}     options.placement The popper element placement relative to the reference element\n * @param {string=}     options.strategy the positioning strategy\n * @param {function=}   options.onCreate called when the popper is created\n * @param {function=}   options.onUpdate called when the popper is updated\n *\n * @returns {UsePopperState} The popper state\n */\nfunction usePopper(referenceElement, popperElement) {\n  let _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  let {\n      enabled = true,\n      placement = 'bottom',\n      strategy = 'absolute',\n      modifiers = EMPTY_MODIFIERS\n    } = _ref,\n    config = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const prevModifiers = useRef(modifiers);\n  const popperInstanceRef = useRef();\n  const update = useCallback(() => {\n    var _popperInstanceRef$cu;\n    (_popperInstanceRef$cu = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu.update();\n  }, []);\n  const forceUpdate = useCallback(() => {\n    var _popperInstanceRef$cu2;\n    (_popperInstanceRef$cu2 = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu2.forceUpdate();\n  }, []);\n  const [popperState, setState] = useSafeState(useState({\n    placement,\n    update,\n    forceUpdate,\n    attributes: {},\n    styles: {\n      popper: {},\n      arrow: {}\n    }\n  }));\n  const updateModifier = useMemo(() => ({\n    name: 'updateStateModifier',\n    enabled: true,\n    phase: 'write',\n    requires: ['computeStyles'],\n    fn: _ref4 => {\n      let {\n        state\n      } = _ref4;\n      const styles = {};\n      const attributes = {};\n      Object.keys(state.elements).forEach(element => {\n        styles[element] = state.styles[element];\n        attributes[element] = state.attributes[element];\n      });\n      setState({\n        state,\n        styles,\n        attributes,\n        update,\n        forceUpdate,\n        placement: state.placement\n      });\n    }\n  }), [update, forceUpdate, setState]);\n  const nextModifiers = useMemo(() => {\n    if (!dequal(prevModifiers.current, modifiers)) {\n      prevModifiers.current = modifiers;\n    }\n    return prevModifiers.current;\n  }, [modifiers]);\n  useEffect(() => {\n    if (!popperInstanceRef.current || !enabled) return;\n    popperInstanceRef.current.setOptions({\n      placement,\n      strategy,\n      modifiers: [...nextModifiers, updateModifier, disabledApplyStylesModifier]\n    });\n  }, [strategy, placement, updateModifier, enabled, nextModifiers]);\n  useEffect(() => {\n    if (!enabled || referenceElement == null || popperElement == null) {\n      return undefined;\n    }\n    popperInstanceRef.current = createPopper(referenceElement, popperElement, Object.assign({}, config, {\n      placement,\n      strategy,\n      modifiers: [...nextModifiers, ariaDescribedByModifier, updateModifier]\n    }));\n    return () => {\n      if (popperInstanceRef.current != null) {\n        popperInstanceRef.current.destroy();\n        popperInstanceRef.current = undefined;\n        setState(s => Object.assign({}, s, {\n          attributes: {},\n          styles: {\n            popper: {}\n          }\n        }));\n      }\n    };\n    // This is only run once to _create_ the popper\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [enabled, referenceElement, popperElement]);\n  return popperState;\n}\nexport default usePopper;", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "source", "excluded", "target", "sourceKeys", "Object", "keys", "key", "i", "length", "indexOf", "useCallback", "useEffect", "useMemo", "useRef", "useState", "dequal", "useSafeState", "createPopper", "disabledApplyStylesModifier", "name", "enabled", "phase", "fn", "undefined", "ariaDescribedByModifier", "effect", "_ref2", "state", "reference", "popper", "elements", "ids", "getAttribute", "split", "filter", "id", "trim", "removeAttribute", "setAttribute", "join", "_ref3", "_popper$getAttribute", "role", "toLowerCase", "EMPTY_MODIFIERS", "usePopper", "referenceElement", "popper<PERSON>lement", "_ref", "arguments", "placement", "strategy", "modifiers", "config", "prevModifiers", "popperInstanceRef", "update", "_popperInstanceRef$cu", "current", "forceUpdate", "_popperInstanceRef$cu2", "popperState", "setState", "attributes", "styles", "arrow", "updateModifier", "requires", "_ref4", "for<PERSON>ach", "element", "nextModifiers", "setOptions", "assign", "destroy", "s"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/ui/esm/usePopper.js"], "sourcesContent": ["const _excluded = [\"enabled\", \"placement\", \"strategy\", \"modifiers\"];\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport { dequal } from 'dequal';\nimport useSafeState from '@restart/hooks/useSafeState';\nimport { createPopper } from './popper';\nconst disabledApplyStylesModifier = {\n  name: 'applyStyles',\n  enabled: false,\n  phase: 'afterWrite',\n  fn: () => undefined\n};\n\n// until docjs supports type exports...\n\nconst ariaDescribedByModifier = {\n  name: 'ariaDescribedBy',\n  enabled: true,\n  phase: 'afterWrite',\n  effect: ({\n    state\n  }) => () => {\n    const {\n      reference,\n      popper\n    } = state.elements;\n    if ('removeAttribute' in reference) {\n      const ids = (reference.getAttribute('aria-describedby') || '').split(',').filter(id => id.trim() !== popper.id);\n      if (!ids.length) reference.removeAttribute('aria-describedby');else reference.setAttribute('aria-describedby', ids.join(','));\n    }\n  },\n  fn: ({\n    state\n  }) => {\n    var _popper$getAttribute;\n    const {\n      popper,\n      reference\n    } = state.elements;\n    const role = (_popper$getAttribute = popper.getAttribute('role')) == null ? void 0 : _popper$getAttribute.toLowerCase();\n    if (popper.id && role === 'tooltip' && 'setAttribute' in reference) {\n      const ids = reference.getAttribute('aria-describedby');\n      if (ids && ids.split(',').indexOf(popper.id) !== -1) {\n        return;\n      }\n      reference.setAttribute('aria-describedby', ids ? `${ids},${popper.id}` : popper.id);\n    }\n  }\n};\nconst EMPTY_MODIFIERS = [];\n/**\n * Position an element relative some reference element using Popper.js\n *\n * @param referenceElement\n * @param popperElement\n * @param {object}      options\n * @param {object=}     options.modifiers Popper.js modifiers\n * @param {boolean=}    options.enabled toggle the popper functionality on/off\n * @param {string=}     options.placement The popper element placement relative to the reference element\n * @param {string=}     options.strategy the positioning strategy\n * @param {function=}   options.onCreate called when the popper is created\n * @param {function=}   options.onUpdate called when the popper is updated\n *\n * @returns {UsePopperState} The popper state\n */\nfunction usePopper(referenceElement, popperElement, _ref = {}) {\n  let {\n      enabled = true,\n      placement = 'bottom',\n      strategy = 'absolute',\n      modifiers = EMPTY_MODIFIERS\n    } = _ref,\n    config = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const prevModifiers = useRef(modifiers);\n  const popperInstanceRef = useRef();\n  const update = useCallback(() => {\n    var _popperInstanceRef$cu;\n    (_popperInstanceRef$cu = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu.update();\n  }, []);\n  const forceUpdate = useCallback(() => {\n    var _popperInstanceRef$cu2;\n    (_popperInstanceRef$cu2 = popperInstanceRef.current) == null ? void 0 : _popperInstanceRef$cu2.forceUpdate();\n  }, []);\n  const [popperState, setState] = useSafeState(useState({\n    placement,\n    update,\n    forceUpdate,\n    attributes: {},\n    styles: {\n      popper: {},\n      arrow: {}\n    }\n  }));\n  const updateModifier = useMemo(() => ({\n    name: 'updateStateModifier',\n    enabled: true,\n    phase: 'write',\n    requires: ['computeStyles'],\n    fn: ({\n      state\n    }) => {\n      const styles = {};\n      const attributes = {};\n      Object.keys(state.elements).forEach(element => {\n        styles[element] = state.styles[element];\n        attributes[element] = state.attributes[element];\n      });\n      setState({\n        state,\n        styles,\n        attributes,\n        update,\n        forceUpdate,\n        placement: state.placement\n      });\n    }\n  }), [update, forceUpdate, setState]);\n  const nextModifiers = useMemo(() => {\n    if (!dequal(prevModifiers.current, modifiers)) {\n      prevModifiers.current = modifiers;\n    }\n    return prevModifiers.current;\n  }, [modifiers]);\n  useEffect(() => {\n    if (!popperInstanceRef.current || !enabled) return;\n    popperInstanceRef.current.setOptions({\n      placement,\n      strategy,\n      modifiers: [...nextModifiers, updateModifier, disabledApplyStylesModifier]\n    });\n  }, [strategy, placement, updateModifier, enabled, nextModifiers]);\n  useEffect(() => {\n    if (!enabled || referenceElement == null || popperElement == null) {\n      return undefined;\n    }\n    popperInstanceRef.current = createPopper(referenceElement, popperElement, Object.assign({}, config, {\n      placement,\n      strategy,\n      modifiers: [...nextModifiers, ariaDescribedByModifier, updateModifier]\n    }));\n    return () => {\n      if (popperInstanceRef.current != null) {\n        popperInstanceRef.current.destroy();\n        popperInstanceRef.current = undefined;\n        setState(s => Object.assign({}, s, {\n          attributes: {},\n          styles: {\n            popper: {}\n          }\n        }));\n      }\n    };\n    // This is only run once to _create_ the popper\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [enabled, referenceElement, popperElement]);\n  return popperState;\n}\nexport default usePopper;"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,CAAC;AACnE,SAASC,6BAA6BA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC;EAAE,IAAIM,GAAG,EAAEC,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IAAED,GAAG,GAAGH,UAAU,CAACI,CAAC,CAAC;IAAE,IAAIN,QAAQ,CAACQ,OAAO,CAACH,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUJ,MAAM,CAACI,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC;EAAE;EAAE,OAAOJ,MAAM;AAAE;AAClT,SAASQ,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACzE,SAASC,MAAM,QAAQ,QAAQ;AAC/B,OAAOC,YAAY,MAAM,6BAA6B;AACtD,SAASC,YAAY,QAAQ,UAAU;AACvC,MAAMC,2BAA2B,GAAG;EAClCC,IAAI,EAAE,aAAa;EACnBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,YAAY;EACnBC,EAAE,EAAEA,CAAA,KAAMC;AACZ,CAAC;;AAED;;AAEA,MAAMC,uBAAuB,GAAG;EAC9BL,IAAI,EAAE,iBAAiB;EACvBC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,YAAY;EACnBI,MAAM,EAAEC,KAAA;IAAA,IAAC;MACPC;IACF,CAAC,GAAAD,KAAA;IAAA,OAAK,MAAM;MACV,MAAM;QACJE,SAAS;QACTC;MACF,CAAC,GAAGF,KAAK,CAACG,QAAQ;MAClB,IAAI,iBAAiB,IAAIF,SAAS,EAAE;QAClC,MAAMG,GAAG,GAAG,CAACH,SAAS,CAACI,YAAY,CAAC,kBAAkB,CAAC,IAAI,EAAE,EAAEC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACC,IAAI,EAAE,KAAKP,MAAM,CAACM,EAAE,CAAC;QAC/G,IAAI,CAACJ,GAAG,CAACvB,MAAM,EAAEoB,SAAS,CAACS,eAAe,CAAC,kBAAkB,CAAC,CAAC,KAAKT,SAAS,CAACU,YAAY,CAAC,kBAAkB,EAAEP,GAAG,CAACQ,IAAI,CAAC,GAAG,CAAC,CAAC;MAC/H;IACF,CAAC;EAAA;EACDjB,EAAE,EAAEkB,KAAA,IAEE;IAAA,IAFD;MACHb;IACF,CAAC,GAAAa,KAAA;IACC,IAAIC,oBAAoB;IACxB,MAAM;MACJZ,MAAM;MACND;IACF,CAAC,GAAGD,KAAK,CAACG,QAAQ;IAClB,MAAMY,IAAI,GAAG,CAACD,oBAAoB,GAAGZ,MAAM,CAACG,YAAY,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGS,oBAAoB,CAACE,WAAW,EAAE;IACvH,IAAId,MAAM,CAACM,EAAE,IAAIO,IAAI,KAAK,SAAS,IAAI,cAAc,IAAId,SAAS,EAAE;MAClE,MAAMG,GAAG,GAAGH,SAAS,CAACI,YAAY,CAAC,kBAAkB,CAAC;MACtD,IAAID,GAAG,IAAIA,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC,CAACxB,OAAO,CAACoB,MAAM,CAACM,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QACnD;MACF;MACAP,SAAS,CAACU,YAAY,CAAC,kBAAkB,EAAEP,GAAG,GAAI,GAAEA,GAAI,IAAGF,MAAM,CAACM,EAAG,EAAC,GAAGN,MAAM,CAACM,EAAE,CAAC;IACrF;EACF;AACF,CAAC;AACD,MAAMS,eAAe,GAAG,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,gBAAgB,EAAEC,aAAa,EAAa;EAAA,IAAXC,IAAI,GAAAC,SAAA,CAAAzC,MAAA,QAAAyC,SAAA,QAAA1B,SAAA,GAAA0B,SAAA,MAAG,CAAC,CAAC;EAC3D,IAAI;MACA7B,OAAO,GAAG,IAAI;MACd8B,SAAS,GAAG,QAAQ;MACpBC,QAAQ,GAAG,UAAU;MACrBC,SAAS,GAAGR;IACd,CAAC,GAAGI,IAAI;IACRK,MAAM,GAAGtD,6BAA6B,CAACiD,IAAI,EAAElD,SAAS,CAAC;EACzD,MAAMwD,aAAa,GAAGzC,MAAM,CAACuC,SAAS,CAAC;EACvC,MAAMG,iBAAiB,GAAG1C,MAAM,EAAE;EAClC,MAAM2C,MAAM,GAAG9C,WAAW,CAAC,MAAM;IAC/B,IAAI+C,qBAAqB;IACzB,CAACA,qBAAqB,GAAGF,iBAAiB,CAACG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,qBAAqB,CAACD,MAAM,EAAE;EACvG,CAAC,EAAE,EAAE,CAAC;EACN,MAAMG,WAAW,GAAGjD,WAAW,CAAC,MAAM;IACpC,IAAIkD,sBAAsB;IAC1B,CAACA,sBAAsB,GAAGL,iBAAiB,CAACG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,sBAAsB,CAACD,WAAW,EAAE;EAC9G,CAAC,EAAE,EAAE,CAAC;EACN,MAAM,CAACE,WAAW,EAAEC,QAAQ,CAAC,GAAG9C,YAAY,CAACF,QAAQ,CAAC;IACpDoC,SAAS;IACTM,MAAM;IACNG,WAAW;IACXI,UAAU,EAAE,CAAC,CAAC;IACdC,MAAM,EAAE;MACNnC,MAAM,EAAE,CAAC,CAAC;MACVoC,KAAK,EAAE,CAAC;IACV;EACF,CAAC,CAAC,CAAC;EACH,MAAMC,cAAc,GAAGtD,OAAO,CAAC,OAAO;IACpCO,IAAI,EAAE,qBAAqB;IAC3BC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE,OAAO;IACd8C,QAAQ,EAAE,CAAC,eAAe,CAAC;IAC3B7C,EAAE,EAAE8C,KAAA,IAEE;MAAA,IAFD;QACHzC;MACF,CAAC,GAAAyC,KAAA;MACC,MAAMJ,MAAM,GAAG,CAAC,CAAC;MACjB,MAAMD,UAAU,GAAG,CAAC,CAAC;MACrB3D,MAAM,CAACC,IAAI,CAACsB,KAAK,CAACG,QAAQ,CAAC,CAACuC,OAAO,CAACC,OAAO,IAAI;QAC7CN,MAAM,CAACM,OAAO,CAAC,GAAG3C,KAAK,CAACqC,MAAM,CAACM,OAAO,CAAC;QACvCP,UAAU,CAACO,OAAO,CAAC,GAAG3C,KAAK,CAACoC,UAAU,CAACO,OAAO,CAAC;MACjD,CAAC,CAAC;MACFR,QAAQ,CAAC;QACPnC,KAAK;QACLqC,MAAM;QACND,UAAU;QACVP,MAAM;QACNG,WAAW;QACXT,SAAS,EAAEvB,KAAK,CAACuB;MACnB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,EAAE,CAACM,MAAM,EAAEG,WAAW,EAAEG,QAAQ,CAAC,CAAC;EACpC,MAAMS,aAAa,GAAG3D,OAAO,CAAC,MAAM;IAClC,IAAI,CAACG,MAAM,CAACuC,aAAa,CAACI,OAAO,EAAEN,SAAS,CAAC,EAAE;MAC7CE,aAAa,CAACI,OAAO,GAAGN,SAAS;IACnC;IACA,OAAOE,aAAa,CAACI,OAAO;EAC9B,CAAC,EAAE,CAACN,SAAS,CAAC,CAAC;EACfzC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4C,iBAAiB,CAACG,OAAO,IAAI,CAACtC,OAAO,EAAE;IAC5CmC,iBAAiB,CAACG,OAAO,CAACc,UAAU,CAAC;MACnCtB,SAAS;MACTC,QAAQ;MACRC,SAAS,EAAE,CAAC,GAAGmB,aAAa,EAAEL,cAAc,EAAEhD,2BAA2B;IAC3E,CAAC,CAAC;EACJ,CAAC,EAAE,CAACiC,QAAQ,EAAED,SAAS,EAAEgB,cAAc,EAAE9C,OAAO,EAAEmD,aAAa,CAAC,CAAC;EACjE5D,SAAS,CAAC,MAAM;IACd,IAAI,CAACS,OAAO,IAAI0B,gBAAgB,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,EAAE;MACjE,OAAOxB,SAAS;IAClB;IACAgC,iBAAiB,CAACG,OAAO,GAAGzC,YAAY,CAAC6B,gBAAgB,EAAEC,aAAa,EAAE3C,MAAM,CAACqE,MAAM,CAAC,CAAC,CAAC,EAAEpB,MAAM,EAAE;MAClGH,SAAS;MACTC,QAAQ;MACRC,SAAS,EAAE,CAAC,GAAGmB,aAAa,EAAE/C,uBAAuB,EAAE0C,cAAc;IACvE,CAAC,CAAC,CAAC;IACH,OAAO,MAAM;MACX,IAAIX,iBAAiB,CAACG,OAAO,IAAI,IAAI,EAAE;QACrCH,iBAAiB,CAACG,OAAO,CAACgB,OAAO,EAAE;QACnCnB,iBAAiB,CAACG,OAAO,GAAGnC,SAAS;QACrCuC,QAAQ,CAACa,CAAC,IAAIvE,MAAM,CAACqE,MAAM,CAAC,CAAC,CAAC,EAAEE,CAAC,EAAE;UACjCZ,UAAU,EAAE,CAAC,CAAC;UACdC,MAAM,EAAE;YACNnC,MAAM,EAAE,CAAC;UACX;QACF,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IACD;IACA;EACF,CAAC,EAAE,CAACT,OAAO,EAAE0B,gBAAgB,EAAEC,aAAa,CAAC,CAAC;EAC9C,OAAOc,WAAW;AACpB;AACA,eAAehB,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}