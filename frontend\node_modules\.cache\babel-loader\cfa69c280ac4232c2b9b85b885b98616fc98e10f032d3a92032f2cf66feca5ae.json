{"ast": null, "code": "import createWithBsPrefix from './createWithBsPrefix';\nexport default createWithBsPrefix('carousel-caption');", "map": {"version": 3, "names": ["createWithBsPrefix"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/CarouselCaption.js"], "sourcesContent": ["import createWithBsPrefix from './createWithBsPrefix';\nexport default createWithBsPrefix('carousel-caption');"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,sBAAsB;AACrD,eAAeA,kBAAkB,CAAC,kBAAkB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}