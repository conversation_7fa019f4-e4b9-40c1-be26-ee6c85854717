{"ast": null, "code": "import createWithBsPrefix from './createWithBsPrefix';\nexport default createWithBsPrefix('modal-footer');", "map": {"version": 3, "names": ["createWithBsPrefix"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/ModalFooter.js"], "sourcesContent": ["import createWithBsPrefix from './createWithBsPrefix';\nexport default createWithBsPrefix('modal-footer');"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,sBAAsB;AACrD,eAAeA,kBAAkB,CAAC,cAAc,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}