{"ast": null, "code": "import css from 'dom-helpers/css';\nimport { dataAttr } from './DataKey';\nimport getBodyScrollbarWidth from './getScrollbarWidth';\nexport const OPEN_DATA_ATTRIBUTE = dataAttr('modal-open');\n\n/**\n * Manages a stack of Modals as well as ensuring\n * body scrolling is is disabled and padding accounted for\n */\nclass ModalManager {\n  constructor() {\n    let {\n      ownerDocument,\n      handleContainerOverflow = true,\n      isRTL = false\n    } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.handleContainerOverflow = handleContainerOverflow;\n    this.isRTL = isRTL;\n    this.modals = [];\n    this.ownerDocument = ownerDocument;\n  }\n  getScrollbarWidth() {\n    return getBodyScrollbarWidth(this.ownerDocument);\n  }\n  getElement() {\n    return (this.ownerDocument || document).body;\n  }\n  setModalAttributes(_modal) {\n    // For overriding\n  }\n  removeModalAttributes(_modal) {\n    // For overriding\n  }\n  setContainerStyle(containerState) {\n    const style = {\n      overflow: 'hidden'\n    };\n\n    // we are only interested in the actual `style` here\n    // because we will override it\n    const paddingProp = this.isRTL ? 'paddingLeft' : 'paddingRight';\n    const container = this.getElement();\n    containerState.style = {\n      overflow: container.style.overflow,\n      [paddingProp]: container.style[paddingProp]\n    };\n    if (containerState.scrollBarWidth) {\n      // use computed style, here to get the real padding\n      // to add our scrollbar width\n      style[paddingProp] = `${parseInt(css(container, paddingProp) || '0', 10) + containerState.scrollBarWidth}px`;\n    }\n    container.setAttribute(OPEN_DATA_ATTRIBUTE, '');\n    css(container, style);\n  }\n  reset() {\n    [...this.modals].forEach(m => this.remove(m));\n  }\n  removeContainerStyle(containerState) {\n    const container = this.getElement();\n    container.removeAttribute(OPEN_DATA_ATTRIBUTE);\n    Object.assign(container.style, containerState.style);\n  }\n  add(modal) {\n    let modalIdx = this.modals.indexOf(modal);\n    if (modalIdx !== -1) {\n      return modalIdx;\n    }\n    modalIdx = this.modals.length;\n    this.modals.push(modal);\n    this.setModalAttributes(modal);\n    if (modalIdx !== 0) {\n      return modalIdx;\n    }\n    this.state = {\n      scrollBarWidth: this.getScrollbarWidth(),\n      style: {}\n    };\n    if (this.handleContainerOverflow) {\n      this.setContainerStyle(this.state);\n    }\n    return modalIdx;\n  }\n  remove(modal) {\n    const modalIdx = this.modals.indexOf(modal);\n    if (modalIdx === -1) {\n      return;\n    }\n    this.modals.splice(modalIdx, 1);\n\n    // if that was the last modal in a container,\n    // clean up the container\n    if (!this.modals.length && this.handleContainerOverflow) {\n      this.removeContainerStyle(this.state);\n    }\n    this.removeModalAttributes(modal);\n  }\n  isTopModal(modal) {\n    return !!this.modals.length && this.modals[this.modals.length - 1] === modal;\n  }\n}\nexport default ModalManager;", "map": {"version": 3, "names": ["css", "dataAttr", "getBodyScrollbarWidth", "OPEN_DATA_ATTRIBUTE", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "ownerDocument", "handleContainerOverflow", "isRTL", "arguments", "length", "undefined", "modals", "getScrollbarWidth", "getElement", "document", "body", "setModalAttributes", "_modal", "removeModalAttributes", "setContainerStyle", "containerState", "style", "overflow", "paddingProp", "container", "scrollBarWidth", "parseInt", "setAttribute", "reset", "for<PERSON>ach", "m", "remove", "removeContainerStyle", "removeAttribute", "Object", "assign", "add", "modal", "modalIdx", "indexOf", "push", "state", "splice", "isTopModal"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/ui/esm/ModalManager.js"], "sourcesContent": ["import css from 'dom-helpers/css';\nimport { dataAttr } from './DataKey';\nimport getBodyScrollbarWidth from './getScrollbarWidth';\nexport const OPEN_DATA_ATTRIBUTE = dataAttr('modal-open');\n\n/**\n * Manages a stack of Modals as well as ensuring\n * body scrolling is is disabled and padding accounted for\n */\nclass ModalManager {\n  constructor({\n    ownerDocument,\n    handleContainerOverflow = true,\n    isRTL = false\n  } = {}) {\n    this.handleContainerOverflow = handleContainerOverflow;\n    this.isRTL = isRTL;\n    this.modals = [];\n    this.ownerDocument = ownerDocument;\n  }\n  getScrollbarWidth() {\n    return getBodyScrollbarWidth(this.ownerDocument);\n  }\n  getElement() {\n    return (this.ownerDocument || document).body;\n  }\n  setModalAttributes(_modal) {\n    // For overriding\n  }\n  removeModalAttributes(_modal) {\n    // For overriding\n  }\n  setContainerStyle(containerState) {\n    const style = {\n      overflow: 'hidden'\n    };\n\n    // we are only interested in the actual `style` here\n    // because we will override it\n    const paddingProp = this.isRTL ? 'paddingLeft' : 'paddingRight';\n    const container = this.getElement();\n    containerState.style = {\n      overflow: container.style.overflow,\n      [paddingProp]: container.style[paddingProp]\n    };\n    if (containerState.scrollBarWidth) {\n      // use computed style, here to get the real padding\n      // to add our scrollbar width\n      style[paddingProp] = `${parseInt(css(container, paddingProp) || '0', 10) + containerState.scrollBarWidth}px`;\n    }\n    container.setAttribute(OPEN_DATA_ATTRIBUTE, '');\n    css(container, style);\n  }\n  reset() {\n    [...this.modals].forEach(m => this.remove(m));\n  }\n  removeContainerStyle(containerState) {\n    const container = this.getElement();\n    container.removeAttribute(OPEN_DATA_ATTRIBUTE);\n    Object.assign(container.style, containerState.style);\n  }\n  add(modal) {\n    let modalIdx = this.modals.indexOf(modal);\n    if (modalIdx !== -1) {\n      return modalIdx;\n    }\n    modalIdx = this.modals.length;\n    this.modals.push(modal);\n    this.setModalAttributes(modal);\n    if (modalIdx !== 0) {\n      return modalIdx;\n    }\n    this.state = {\n      scrollBarWidth: this.getScrollbarWidth(),\n      style: {}\n    };\n    if (this.handleContainerOverflow) {\n      this.setContainerStyle(this.state);\n    }\n    return modalIdx;\n  }\n  remove(modal) {\n    const modalIdx = this.modals.indexOf(modal);\n    if (modalIdx === -1) {\n      return;\n    }\n    this.modals.splice(modalIdx, 1);\n\n    // if that was the last modal in a container,\n    // clean up the container\n    if (!this.modals.length && this.handleContainerOverflow) {\n      this.removeContainerStyle(this.state);\n    }\n    this.removeModalAttributes(modal);\n  }\n  isTopModal(modal) {\n    return !!this.modals.length && this.modals[this.modals.length - 1] === modal;\n  }\n}\nexport default ModalManager;"], "mappings": "AAAA,OAAOA,GAAG,MAAM,iBAAiB;AACjC,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAOC,qBAAqB,MAAM,qBAAqB;AACvD,OAAO,MAAMC,mBAAmB,GAAGF,QAAQ,CAAC,YAAY,CAAC;;AAEzD;AACA;AACA;AACA;AACA,MAAMG,YAAY,CAAC;EACjBC,WAAWA,CAAA,EAIH;IAAA,IAJI;MACVC,aAAa;MACbC,uBAAuB,GAAG,IAAI;MAC9BC,KAAK,GAAG;IACV,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACJ,IAAI,CAACF,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACI,MAAM,GAAG,EAAE;IAChB,IAAI,CAACN,aAAa,GAAGA,aAAa;EACpC;EACAO,iBAAiBA,CAAA,EAAG;IAClB,OAAOX,qBAAqB,CAAC,IAAI,CAACI,aAAa,CAAC;EAClD;EACAQ,UAAUA,CAAA,EAAG;IACX,OAAO,CAAC,IAAI,CAACR,aAAa,IAAIS,QAAQ,EAAEC,IAAI;EAC9C;EACAC,kBAAkBA,CAACC,MAAM,EAAE;IACzB;EAAA;EAEFC,qBAAqBA,CAACD,MAAM,EAAE;IAC5B;EAAA;EAEFE,iBAAiBA,CAACC,cAAc,EAAE;IAChC,MAAMC,KAAK,GAAG;MACZC,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA;IACA,MAAMC,WAAW,GAAG,IAAI,CAAChB,KAAK,GAAG,aAAa,GAAG,cAAc;IAC/D,MAAMiB,SAAS,GAAG,IAAI,CAACX,UAAU,EAAE;IACnCO,cAAc,CAACC,KAAK,GAAG;MACrBC,QAAQ,EAAEE,SAAS,CAACH,KAAK,CAACC,QAAQ;MAClC,CAACC,WAAW,GAAGC,SAAS,CAACH,KAAK,CAACE,WAAW;IAC5C,CAAC;IACD,IAAIH,cAAc,CAACK,cAAc,EAAE;MACjC;MACA;MACAJ,KAAK,CAACE,WAAW,CAAC,GAAI,GAAEG,QAAQ,CAAC3B,GAAG,CAACyB,SAAS,EAAED,WAAW,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC,GAAGH,cAAc,CAACK,cAAe,IAAG;IAC9G;IACAD,SAAS,CAACG,YAAY,CAACzB,mBAAmB,EAAE,EAAE,CAAC;IAC/CH,GAAG,CAACyB,SAAS,EAAEH,KAAK,CAAC;EACvB;EACAO,KAAKA,CAAA,EAAG;IACN,CAAC,GAAG,IAAI,CAACjB,MAAM,CAAC,CAACkB,OAAO,CAACC,CAAC,IAAI,IAAI,CAACC,MAAM,CAACD,CAAC,CAAC,CAAC;EAC/C;EACAE,oBAAoBA,CAACZ,cAAc,EAAE;IACnC,MAAMI,SAAS,GAAG,IAAI,CAACX,UAAU,EAAE;IACnCW,SAAS,CAACS,eAAe,CAAC/B,mBAAmB,CAAC;IAC9CgC,MAAM,CAACC,MAAM,CAACX,SAAS,CAACH,KAAK,EAAED,cAAc,CAACC,KAAK,CAAC;EACtD;EACAe,GAAGA,CAACC,KAAK,EAAE;IACT,IAAIC,QAAQ,GAAG,IAAI,CAAC3B,MAAM,CAAC4B,OAAO,CAACF,KAAK,CAAC;IACzC,IAAIC,QAAQ,KAAK,CAAC,CAAC,EAAE;MACnB,OAAOA,QAAQ;IACjB;IACAA,QAAQ,GAAG,IAAI,CAAC3B,MAAM,CAACF,MAAM;IAC7B,IAAI,CAACE,MAAM,CAAC6B,IAAI,CAACH,KAAK,CAAC;IACvB,IAAI,CAACrB,kBAAkB,CAACqB,KAAK,CAAC;IAC9B,IAAIC,QAAQ,KAAK,CAAC,EAAE;MAClB,OAAOA,QAAQ;IACjB;IACA,IAAI,CAACG,KAAK,GAAG;MACXhB,cAAc,EAAE,IAAI,CAACb,iBAAiB,EAAE;MACxCS,KAAK,EAAE,CAAC;IACV,CAAC;IACD,IAAI,IAAI,CAACf,uBAAuB,EAAE;MAChC,IAAI,CAACa,iBAAiB,CAAC,IAAI,CAACsB,KAAK,CAAC;IACpC;IACA,OAAOH,QAAQ;EACjB;EACAP,MAAMA,CAACM,KAAK,EAAE;IACZ,MAAMC,QAAQ,GAAG,IAAI,CAAC3B,MAAM,CAAC4B,OAAO,CAACF,KAAK,CAAC;IAC3C,IAAIC,QAAQ,KAAK,CAAC,CAAC,EAAE;MACnB;IACF;IACA,IAAI,CAAC3B,MAAM,CAAC+B,MAAM,CAACJ,QAAQ,EAAE,CAAC,CAAC;;IAE/B;IACA;IACA,IAAI,CAAC,IAAI,CAAC3B,MAAM,CAACF,MAAM,IAAI,IAAI,CAACH,uBAAuB,EAAE;MACvD,IAAI,CAAC0B,oBAAoB,CAAC,IAAI,CAACS,KAAK,CAAC;IACvC;IACA,IAAI,CAACvB,qBAAqB,CAACmB,KAAK,CAAC;EACnC;EACAM,UAAUA,CAACN,KAAK,EAAE;IAChB,OAAO,CAAC,CAAC,IAAI,CAAC1B,MAAM,CAACF,MAAM,IAAI,IAAI,CAACE,MAAM,CAAC,IAAI,CAACA,MAAM,CAACF,MAAM,GAAG,CAAC,CAAC,KAAK4B,KAAK;EAC9E;AACF;AACA,eAAelC,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}