{"ast": null, "code": "import { useState } from 'react';\nimport useEffect from './useIsomorphicEffect';\nvar targetMap = new WeakMap();\nvar resizeObserver;\nfunction getResizeObserver() {\n  // eslint-disable-next-line no-return-assign\n  return resizeObserver = resizeObserver || new window.ResizeObserver(function (entries) {\n    entries.forEach(function (entry) {\n      var handler = targetMap.get(entry.target);\n      if (handler) handler(entry.contentRect);\n    });\n  });\n}\n/**\n * Efficiently observe size changes on an element. Depends on the `ResizeObserver` api,\n * and polyfills are needed in older browsers.\n *\n * ```ts\n * const [ref, attachRef] = useCallbackRef(null);\n *\n * const rect = useResizeObserver(ref);\n *\n * return (\n *  <div ref={attachRef}>\n *    {JSON.stringify(rect)}\n *  </div>\n * )\n * ```\n *\n * @param element The DOM element to observe\n */\n\nexport default function useResizeObserver(element) {\n  var _useState = useState(null),\n    rect = _useState[0],\n    setRect = _useState[1];\n  useEffect(function () {\n    if (!element) return;\n    getResizeObserver().observe(element);\n    setRect(element.getBoundingClientRect());\n    targetMap.set(element, function (rect) {\n      setRect(rect);\n    });\n    return function () {\n      targetMap.delete(element);\n    };\n  }, [element]);\n  return rect;\n}", "map": {"version": 3, "names": ["useState", "useEffect", "targetMap", "WeakMap", "resizeObserver", "getResizeObserver", "window", "ResizeObserver", "entries", "for<PERSON>ach", "entry", "handler", "get", "target", "contentRect", "useResizeObserver", "element", "_useState", "rect", "setRect", "observe", "getBoundingClientRect", "set", "delete"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useResizeObserver.js"], "sourcesContent": ["import { useState } from 'react';\nimport useEffect from './useIsomorphicEffect';\nvar targetMap = new WeakMap();\nvar resizeObserver;\n\nfunction getResizeObserver() {\n  // eslint-disable-next-line no-return-assign\n  return resizeObserver = resizeObserver || new window.ResizeObserver(function (entries) {\n    entries.forEach(function (entry) {\n      var handler = targetMap.get(entry.target);\n      if (handler) handler(entry.contentRect);\n    });\n  });\n}\n/**\n * Efficiently observe size changes on an element. Depends on the `ResizeObserver` api,\n * and polyfills are needed in older browsers.\n *\n * ```ts\n * const [ref, attachRef] = useCallbackRef(null);\n *\n * const rect = useResizeObserver(ref);\n *\n * return (\n *  <div ref={attachRef}>\n *    {JSON.stringify(rect)}\n *  </div>\n * )\n * ```\n *\n * @param element The DOM element to observe\n */\n\n\nexport default function useResizeObserver(element) {\n  var _useState = useState(null),\n      rect = _useState[0],\n      setRect = _useState[1];\n\n  useEffect(function () {\n    if (!element) return;\n    getResizeObserver().observe(element);\n    setRect(element.getBoundingClientRect());\n    targetMap.set(element, function (rect) {\n      setRect(rect);\n    });\n    return function () {\n      targetMap.delete(element);\n    };\n  }, [element]);\n  return rect;\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,IAAIC,SAAS,GAAG,IAAIC,OAAO,EAAE;AAC7B,IAAIC,cAAc;AAElB,SAASC,iBAAiBA,CAAA,EAAG;EAC3B;EACA,OAAOD,cAAc,GAAGA,cAAc,IAAI,IAAIE,MAAM,CAACC,cAAc,CAAC,UAAUC,OAAO,EAAE;IACrFA,OAAO,CAACC,OAAO,CAAC,UAAUC,KAAK,EAAE;MAC/B,IAAIC,OAAO,GAAGT,SAAS,CAACU,GAAG,CAACF,KAAK,CAACG,MAAM,CAAC;MACzC,IAAIF,OAAO,EAAEA,OAAO,CAACD,KAAK,CAACI,WAAW,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,eAAe,SAASC,iBAAiBA,CAACC,OAAO,EAAE;EACjD,IAAIC,SAAS,GAAGjB,QAAQ,CAAC,IAAI,CAAC;IAC1BkB,IAAI,GAAGD,SAAS,CAAC,CAAC,CAAC;IACnBE,OAAO,GAAGF,SAAS,CAAC,CAAC,CAAC;EAE1BhB,SAAS,CAAC,YAAY;IACpB,IAAI,CAACe,OAAO,EAAE;IACdX,iBAAiB,EAAE,CAACe,OAAO,CAACJ,OAAO,CAAC;IACpCG,OAAO,CAACH,OAAO,CAACK,qBAAqB,EAAE,CAAC;IACxCnB,SAAS,CAACoB,GAAG,CAACN,OAAO,EAAE,UAAUE,IAAI,EAAE;MACrCC,OAAO,CAACD,IAAI,CAAC;IACf,CAAC,CAAC;IACF,OAAO,YAAY;MACjBhB,SAAS,CAACqB,MAAM,CAACP,OAAO,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,OAAOE,IAAI;AACb"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}