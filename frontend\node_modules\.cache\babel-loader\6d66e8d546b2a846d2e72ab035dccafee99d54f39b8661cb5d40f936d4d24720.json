{"ast": null, "code": "import useEventListener from './useEventListener';\nimport { useCallback } from 'react';\n\n/**\n * Attaches an event handler outside directly to the `document`,\n * bypassing the react synthetic event system.\n *\n * ```ts\n * useGlobalListener('keydown', (event) => {\n *  console.log(event.key)\n * })\n * ```\n *\n * @param event The DOM event name\n * @param handler An event handler\n * @param capture Whether or not to listen during the capture event phase\n */\nexport default function useGlobalListener(event, handler, capture) {\n  if (capture === void 0) {\n    capture = false;\n  }\n  var documentTarget = useCallback(function () {\n    return document;\n  }, []);\n  return useEventListener(documentTarget, event, handler, capture);\n}", "map": {"version": 3, "names": ["useEventListener", "useCallback", "useGlobalListener", "event", "handler", "capture", "documentTarget", "document"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useGlobalListener.js"], "sourcesContent": ["import useEventListener from './useEventListener';\nimport { useCallback } from 'react';\n\n/**\n * Attaches an event handler outside directly to the `document`,\n * bypassing the react synthetic event system.\n *\n * ```ts\n * useGlobalListener('keydown', (event) => {\n *  console.log(event.key)\n * })\n * ```\n *\n * @param event The DOM event name\n * @param handler An event handler\n * @param capture Whether or not to listen during the capture event phase\n */\nexport default function useGlobalListener(event, handler, capture) {\n  if (capture === void 0) {\n    capture = false;\n  }\n\n  var documentTarget = useCallback(function () {\n    return document;\n  }, []);\n  return useEventListener(documentTarget, event, handler, capture);\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,WAAW,QAAQ,OAAO;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACjE,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,KAAK;EACjB;EAEA,IAAIC,cAAc,GAAGL,WAAW,CAAC,YAAY;IAC3C,OAAOM,QAAQ;EACjB,CAAC,EAAE,EAAE,CAAC;EACN,OAAOP,gBAAgB,CAACM,cAAc,EAAEH,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAAC;AAClE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}