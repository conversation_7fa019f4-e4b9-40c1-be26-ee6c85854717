{"ast": null, "code": "// reading a dimension prop will cause the browser to recalculate,\n// which will let our animations work\nexport default function triggerBrowserReflow(node) {\n  // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n  node.offsetHeight;\n}", "map": {"version": 3, "names": ["triggerBrowserReflow", "node", "offsetHeight"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/triggerBrowserReflow.js"], "sourcesContent": ["// reading a dimension prop will cause the browser to recalculate,\n// which will let our animations work\nexport default function triggerBrowserReflow(node) {\n  // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n  node.offsetHeight;\n}"], "mappings": "AAAA;AACA;AACA,eAAe,SAASA,oBAAoBA,CAACC,IAAI,EAAE;EACjD;EACAA,IAAI,CAACC,YAAY;AACnB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}