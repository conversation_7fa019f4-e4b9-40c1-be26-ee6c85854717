{"ast": null, "code": "import ownerDocument from './ownerDocument';\n/**\n * Returns the owner window of a given element.\n * \n * @param node the element\n */\n\nexport default function ownerWindow(node) {\n  var doc = ownerDocument(node);\n  return doc && doc.defaultView || window;\n}", "map": {"version": 3, "names": ["ownerDocument", "ownerWindow", "node", "doc", "defaultView", "window"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/dom-helpers/esm/ownerWindow.js"], "sourcesContent": ["import ownerDocument from './ownerDocument';\n/**\n * Returns the owner window of a given element.\n * \n * @param node the element\n */\n\nexport default function ownerWindow(node) {\n  var doc = ownerDocument(node);\n  return doc && doc.defaultView || window;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAE;EACxC,IAAIC,GAAG,GAAGH,aAAa,CAACE,IAAI,CAAC;EAC7B,OAAOC,GAAG,IAAIA,GAAG,CAACC,WAAW,IAAIC,MAAM;AACzC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}