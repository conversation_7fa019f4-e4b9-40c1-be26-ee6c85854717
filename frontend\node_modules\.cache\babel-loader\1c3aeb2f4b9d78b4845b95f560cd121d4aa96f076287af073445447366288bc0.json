{"ast": null, "code": "import * as React from 'react';\nimport { useEffect, useMemo, useRef, useCallback } from 'react';\nimport classNames from 'classnames';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport ToastFade from './ToastFade';\nimport ToastHeader from './ToastHeader';\nimport ToastBody from './ToastBody';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport ToastContext from './ToastContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Toast = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    transition: Transition = ToastFade,\n    show = true,\n    animation = true,\n    delay = 5000,\n    autohide = false,\n    onClose,\n    bg,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'toast');\n\n  // We use refs for these, because we don't want to restart the autohide\n  // timer in case these values change.\n  const delayRef = useRef(delay);\n  const onCloseRef = useRef(onClose);\n  useEffect(() => {\n    delayRef.current = delay;\n    onCloseRef.current = onClose;\n  }, [delay, onClose]);\n  const autohideTimeout = useTimeout();\n  const autohideToast = !!(autohide && show);\n  const autohideFunc = useCallback(() => {\n    if (autohideToast) {\n      onCloseRef.current == null ? void 0 : onCloseRef.current();\n    }\n  }, [autohideToast]);\n  useEffect(() => {\n    // Only reset timer if show or autohide changes.\n    autohideTimeout.set(autohideFunc, delayRef.current);\n  }, [autohideTimeout, autohideFunc]);\n  const toastContext = useMemo(() => ({\n    onClose\n  }), [onClose]);\n  const hasAnimation = !!(Transition && animation);\n  const toast = /*#__PURE__*/_jsx(\"div\", {\n    ...props,\n    ref: ref,\n    className: classNames(bsPrefix, className, bg && `bg-${bg}`, !hasAnimation && (show ? 'show' : 'hide')),\n    role: \"alert\",\n    \"aria-live\": \"assertive\",\n    \"aria-atomic\": \"true\"\n  });\n  return /*#__PURE__*/_jsx(ToastContext.Provider, {\n    value: toastContext,\n    children: hasAnimation && Transition ? /*#__PURE__*/_jsx(Transition, {\n      in: show,\n      unmountOnExit: true,\n      children: toast\n    }) : toast\n  });\n});\nToast.displayName = 'Toast';\nexport default Object.assign(Toast, {\n  Body: ToastBody,\n  Header: ToastHeader\n});", "map": {"version": 3, "names": ["React", "useEffect", "useMemo", "useRef", "useCallback", "classNames", "useTimeout", "ToastFade", "ToastHeader", "ToastBody", "useBootstrapPrefix", "ToastContext", "jsx", "_jsx", "Toast", "forwardRef", "_ref", "ref", "bsPrefix", "className", "transition", "Transition", "show", "animation", "delay", "autohide", "onClose", "bg", "props", "delayRef", "onCloseRef", "current", "autohideTimeout", "autohideToast", "autohideFunc", "set", "toastContext", "hasAnimation", "toast", "role", "Provider", "value", "children", "in", "unmountOnExit", "displayName", "Object", "assign", "Body", "Header"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Toast.js"], "sourcesContent": ["import * as React from 'react';\nimport { useEffect, useMemo, useRef, useCallback } from 'react';\nimport classNames from 'classnames';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport ToastFade from './ToastFade';\nimport ToastHeader from './ToastHeader';\nimport ToastBody from './ToastBody';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport ToastContext from './ToastContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Toast = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  transition: Transition = ToastFade,\n  show = true,\n  animation = true,\n  delay = 5000,\n  autohide = false,\n  onClose,\n  bg,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'toast');\n\n  // We use refs for these, because we don't want to restart the autohide\n  // timer in case these values change.\n  const delayRef = useRef(delay);\n  const onCloseRef = useRef(onClose);\n  useEffect(() => {\n    delayRef.current = delay;\n    onCloseRef.current = onClose;\n  }, [delay, onClose]);\n  const autohideTimeout = useTimeout();\n  const autohideToast = !!(autohide && show);\n  const autohideFunc = useCallback(() => {\n    if (autohideToast) {\n      onCloseRef.current == null ? void 0 : onCloseRef.current();\n    }\n  }, [autohideToast]);\n  useEffect(() => {\n    // Only reset timer if show or autohide changes.\n    autohideTimeout.set(autohideFunc, delayRef.current);\n  }, [autohideTimeout, autohideFunc]);\n  const toastContext = useMemo(() => ({\n    onClose\n  }), [onClose]);\n  const hasAnimation = !!(Transition && animation);\n  const toast = /*#__PURE__*/_jsx(\"div\", {\n    ...props,\n    ref: ref,\n    className: classNames(bsPrefix, className, bg && `bg-${bg}`, !hasAnimation && (show ? 'show' : 'hide')),\n    role: \"alert\",\n    \"aria-live\": \"assertive\",\n    \"aria-atomic\": \"true\"\n  });\n  return /*#__PURE__*/_jsx(ToastContext.Provider, {\n    value: toastContext,\n    children: hasAnimation && Transition ? /*#__PURE__*/_jsx(Transition, {\n      in: show,\n      unmountOnExit: true,\n      children: toast\n    }) : toast\n  });\n});\nToast.displayName = 'Toast';\nexport default Object.assign(Toast, {\n  Body: ToastBody,\n  Header: ToastHeader\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,KAAK,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,CAAAC,IAAA,EAWzCC,GAAG,KAAK;EAAA,IAXkC;IAC3CC,QAAQ;IACRC,SAAS;IACTC,UAAU,EAAEC,UAAU,GAAGd,SAAS;IAClCe,IAAI,GAAG,IAAI;IACXC,SAAS,GAAG,IAAI;IAChBC,KAAK,GAAG,IAAI;IACZC,QAAQ,GAAG,KAAK;IAChBC,OAAO;IACPC,EAAE;IACF,GAAGC;EACL,CAAC,GAAAZ,IAAA;EACCE,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,OAAO,CAAC;;EAEhD;EACA;EACA,MAAMW,QAAQ,GAAG1B,MAAM,CAACqB,KAAK,CAAC;EAC9B,MAAMM,UAAU,GAAG3B,MAAM,CAACuB,OAAO,CAAC;EAClCzB,SAAS,CAAC,MAAM;IACd4B,QAAQ,CAACE,OAAO,GAAGP,KAAK;IACxBM,UAAU,CAACC,OAAO,GAAGL,OAAO;EAC9B,CAAC,EAAE,CAACF,KAAK,EAAEE,OAAO,CAAC,CAAC;EACpB,MAAMM,eAAe,GAAG1B,UAAU,EAAE;EACpC,MAAM2B,aAAa,GAAG,CAAC,EAAER,QAAQ,IAAIH,IAAI,CAAC;EAC1C,MAAMY,YAAY,GAAG9B,WAAW,CAAC,MAAM;IACrC,IAAI6B,aAAa,EAAE;MACjBH,UAAU,CAACC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGD,UAAU,CAACC,OAAO,EAAE;IAC5D;EACF,CAAC,EAAE,CAACE,aAAa,CAAC,CAAC;EACnBhC,SAAS,CAAC,MAAM;IACd;IACA+B,eAAe,CAACG,GAAG,CAACD,YAAY,EAAEL,QAAQ,CAACE,OAAO,CAAC;EACrD,CAAC,EAAE,CAACC,eAAe,EAAEE,YAAY,CAAC,CAAC;EACnC,MAAME,YAAY,GAAGlC,OAAO,CAAC,OAAO;IAClCwB;EACF,CAAC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACd,MAAMW,YAAY,GAAG,CAAC,EAAEhB,UAAU,IAAIE,SAAS,CAAC;EAChD,MAAMe,KAAK,GAAG,aAAazB,IAAI,CAAC,KAAK,EAAE;IACrC,GAAGe,KAAK;IACRX,GAAG,EAAEA,GAAG;IACRE,SAAS,EAAEd,UAAU,CAACa,QAAQ,EAAEC,SAAS,EAAEQ,EAAE,IAAK,MAAKA,EAAG,EAAC,EAAE,CAACU,YAAY,KAAKf,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;IACvGiB,IAAI,EAAE,OAAO;IACb,WAAW,EAAE,WAAW;IACxB,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,OAAO,aAAa1B,IAAI,CAACF,YAAY,CAAC6B,QAAQ,EAAE;IAC9CC,KAAK,EAAEL,YAAY;IACnBM,QAAQ,EAAEL,YAAY,IAAIhB,UAAU,GAAG,aAAaR,IAAI,CAACQ,UAAU,EAAE;MACnEsB,EAAE,EAAErB,IAAI;MACRsB,aAAa,EAAE,IAAI;MACnBF,QAAQ,EAAEJ;IACZ,CAAC,CAAC,GAAGA;EACP,CAAC,CAAC;AACJ,CAAC,CAAC;AACFxB,KAAK,CAAC+B,WAAW,GAAG,OAAO;AAC3B,eAAeC,MAAM,CAACC,MAAM,CAACjC,KAAK,EAAE;EAClCkC,IAAI,EAAEvC,SAAS;EACfwC,MAAM,EAAEzC;AACV,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}