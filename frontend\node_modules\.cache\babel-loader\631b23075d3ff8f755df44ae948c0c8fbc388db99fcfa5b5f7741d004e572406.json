{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Nav, Collapse } from 'react-bootstrap';\nimport { Link, useLocation } from 'react-router-dom';\nimport './AdminSidebar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminSidebar = () => {\n  _s();\n  const location = useLocation();\n  const [dashboardOpen, setDashboardOpen] = useState(true);\n  const [elementsOpen, setElementsOpen] = useState(false);\n  const [additionalPagesOpen, setAdditionalPagesOpen] = useState(false);\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-sidebar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-avatar\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/api/placeholder/40/40\",\n            alt: \"Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"John David\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status online\",\n            children: \"\\u25CF Online\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"section-title\",\n          children: \"General\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav, {\n          className: \"flex-column\",\n          children: [/*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: \"div\",\n              className: `sidebar-link ${dashboardOpen ? 'active' : ''}`,\n              onClick: () => setDashboardOpen(!dashboardOpen),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-tachometer-alt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this), \"Dashboard\", /*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas fa-chevron-${dashboardOpen ? 'down' : 'right'} ms-auto`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n              in: dashboardOpen,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"submenu\",\n                children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin\",\n                  className: `submenu-link ${isActive('/admin') ? 'active' : ''}`,\n                  children: \"Overview\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin/analytics\",\n                  className: `submenu-link ${isActive('/admin/analytics') ? 'active' : ''}`,\n                  children: \"Analytics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/widgets\",\n              className: `sidebar-link ${isActive('/admin/widgets') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-th\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this), \"Widgets\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: \"div\",\n              className: `sidebar-link ${elementsOpen ? 'active' : ''}`,\n              onClick: () => setElementsOpen(!elementsOpen),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-cube\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this), \"Elements\", /*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas fa-chevron-${elementsOpen ? 'down' : 'right'} ms-auto`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n              in: elementsOpen,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"submenu\",\n                children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin/products\",\n                  className: `submenu-link ${isActive('/admin/products') ? 'active' : ''}`,\n                  children: \"Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin/categories\",\n                  className: `submenu-link ${isActive('/admin/categories') ? 'active' : ''}`,\n                  children: \"Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin/brands\",\n                  className: `submenu-link ${isActive('/admin/brands') ? 'active' : ''}`,\n                  children: \"Brands\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/tables\",\n              className: `sidebar-link ${isActive('/admin/tables') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-table\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), \"Tables\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/orders\",\n              className: `sidebar-link ${isActive('/admin/orders') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-shopping-cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), \"Orders\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/pricing\",\n              className: `sidebar-link ${isActive('/admin/pricing') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-dollar-sign\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), \"Pricing Tables\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/contact\",\n              className: `sidebar-link ${isActive('/admin/contact') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-envelope\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this), \"Contact\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: \"div\",\n              className: `sidebar-link ${additionalPagesOpen ? 'active' : ''}`,\n              onClick: () => setAdditionalPagesOpen(!additionalPagesOpen),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-plus\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), \"Additional Pages\", /*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas fa-chevron-${additionalPagesOpen ? 'down' : 'right'} ms-auto`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n              in: additionalPagesOpen,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"submenu\",\n                children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin/users\",\n                  className: `submenu-link ${isActive('/admin/users') ? 'active' : ''}`,\n                  children: \"Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin/reviews\",\n                  className: `submenu-link ${isActive('/admin/reviews') ? 'active' : ''}`,\n                  children: \"Reviews\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/map\",\n              className: `sidebar-link ${isActive('/admin/map') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-map\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), \"Map\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/charts\",\n              className: `sidebar-link ${isActive('/admin/charts') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-chart-bar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), \"Charts\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"weather-widget\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-sun\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Hot weather\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminSidebar, \"wRRKj2K5k2aZjblCWC/Rk5j98p0=\", false, function () {\n  return [useLocation];\n});\n_c = AdminSidebar;\nexport default AdminSidebar;\nvar _c;\n$RefreshReg$(_c, \"AdminSidebar\");", "map": {"version": 3, "names": ["React", "useState", "Nav", "Collapse", "Link", "useLocation", "jsxDEV", "_jsxDEV", "AdminSidebar", "_s", "location", "dashboardOpen", "setDashboardOpen", "elementsOpen", "setElementsOpen", "additionalPagesOpen", "setAdditionalPagesOpen", "isActive", "path", "pathname", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON>", "as", "onClick", "in", "to", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/admin/AdminSidebar.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Nav, Collapse } from 'react-bootstrap';\nimport { Link, useLocation } from 'react-router-dom';\nimport './AdminSidebar.css';\n\nconst AdminSidebar = () => {\n  const location = useLocation();\n  const [dashboardOpen, setDashboardOpen] = useState(true);\n  const [elementsOpen, setElementsOpen] = useState(false);\n  const [additionalPagesOpen, setAdditionalPagesOpen] = useState(false);\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <div className=\"admin-sidebar\">\n      <div className=\"sidebar-header\">\n        <div className=\"user-info\">\n          <div className=\"user-avatar\">\n            <img src=\"/api/placeholder/40/40\" alt=\"Admin\" />\n          </div>\n          <div className=\"user-details\">\n            <h6><PERSON></h6>\n            <span className=\"status online\">● Online</span>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"sidebar-content\">\n        <div className=\"sidebar-section\">\n          <h6 className=\"section-title\">General</h6>\n          \n          <Nav className=\"flex-column\">\n            <Nav.Item>\n              <Nav.Link \n                as=\"div\" \n                className={`sidebar-link ${dashboardOpen ? 'active' : ''}`}\n                onClick={() => setDashboardOpen(!dashboardOpen)}\n              >\n                <i className=\"fas fa-tachometer-alt\"></i>\n                Dashboard\n                <i className={`fas fa-chevron-${dashboardOpen ? 'down' : 'right'} ms-auto`}></i>\n              </Nav.Link>\n              <Collapse in={dashboardOpen}>\n                <div className=\"submenu\">\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin\" \n                    className={`submenu-link ${isActive('/admin') ? 'active' : ''}`}\n                  >\n                    Overview\n                  </Nav.Link>\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin/analytics\" \n                    className={`submenu-link ${isActive('/admin/analytics') ? 'active' : ''}`}\n                  >\n                    Analytics\n                  </Nav.Link>\n                </div>\n              </Collapse>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/widgets\" \n                className={`sidebar-link ${isActive('/admin/widgets') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-th\"></i>\n                Widgets\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as=\"div\" \n                className={`sidebar-link ${elementsOpen ? 'active' : ''}`}\n                onClick={() => setElementsOpen(!elementsOpen)}\n              >\n                <i className=\"fas fa-cube\"></i>\n                Elements\n                <i className={`fas fa-chevron-${elementsOpen ? 'down' : 'right'} ms-auto`}></i>\n              </Nav.Link>\n              <Collapse in={elementsOpen}>\n                <div className=\"submenu\">\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin/products\" \n                    className={`submenu-link ${isActive('/admin/products') ? 'active' : ''}`}\n                  >\n                    Products\n                  </Nav.Link>\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin/categories\" \n                    className={`submenu-link ${isActive('/admin/categories') ? 'active' : ''}`}\n                  >\n                    Categories\n                  </Nav.Link>\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin/brands\" \n                    className={`submenu-link ${isActive('/admin/brands') ? 'active' : ''}`}\n                  >\n                    Brands\n                  </Nav.Link>\n                </div>\n              </Collapse>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/tables\" \n                className={`sidebar-link ${isActive('/admin/tables') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-table\"></i>\n                Tables\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/orders\" \n                className={`sidebar-link ${isActive('/admin/orders') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-shopping-cart\"></i>\n                Orders\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/pricing\" \n                className={`sidebar-link ${isActive('/admin/pricing') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-dollar-sign\"></i>\n                Pricing Tables\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/contact\" \n                className={`sidebar-link ${isActive('/admin/contact') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-envelope\"></i>\n                Contact\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as=\"div\" \n                className={`sidebar-link ${additionalPagesOpen ? 'active' : ''}`}\n                onClick={() => setAdditionalPagesOpen(!additionalPagesOpen)}\n              >\n                <i className=\"fas fa-plus\"></i>\n                Additional Pages\n                <i className={`fas fa-chevron-${additionalPagesOpen ? 'down' : 'right'} ms-auto`}></i>\n              </Nav.Link>\n              <Collapse in={additionalPagesOpen}>\n                <div className=\"submenu\">\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin/users\" \n                    className={`submenu-link ${isActive('/admin/users') ? 'active' : ''}`}\n                  >\n                    Users\n                  </Nav.Link>\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin/reviews\" \n                    className={`submenu-link ${isActive('/admin/reviews') ? 'active' : ''}`}\n                  >\n                    Reviews\n                  </Nav.Link>\n                </div>\n              </Collapse>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/map\" \n                className={`sidebar-link ${isActive('/admin/map') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-map\"></i>\n                Map\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/charts\" \n                className={`sidebar-link ${isActive('/admin/charts') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-chart-bar\"></i>\n                Charts\n              </Nav.Link>\n            </Nav.Item>\n          </Nav>\n        </div>\n      </div>\n\n      <div className=\"sidebar-footer\">\n        <div className=\"weather-widget\">\n          <i className=\"fas fa-sun\"></i>\n          <span>Hot weather</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,QAAQ,QAAQ,iBAAiB;AAC/C,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGL,WAAW,EAAE;EAC9B,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACc,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAMgB,QAAQ,GAAIC,IAAI,IAAKR,QAAQ,CAACS,QAAQ,KAAKD,IAAI;EAErD,oBACEX,OAAA;IAAKa,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5Bd,OAAA;MAAKa,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7Bd,OAAA;QAAKa,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBd,OAAA;UAAKa,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1Bd,OAAA;YAAKe,GAAG,EAAC,wBAAwB;YAACC,GAAG,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAG;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAC5C,eACNpB,OAAA;UAAKa,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3Bd,OAAA;YAAAc,QAAA,EAAI;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eACnBpB,OAAA;YAAMa,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAO;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAC3C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF,eAENpB,OAAA;MAAKa,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9Bd,OAAA;QAAKa,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9Bd,OAAA;UAAIa,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK,eAE1CpB,OAAA,CAACL,GAAG;UAACkB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1Bd,OAAA,CAACL,GAAG,CAAC0B,IAAI;YAAAP,QAAA,gBACPd,OAAA,CAACL,GAAG,CAACE,IAAI;cACPyB,EAAE,EAAC,KAAK;cACRT,SAAS,EAAG,gBAAeT,aAAa,GAAG,QAAQ,GAAG,EAAG,EAAE;cAC3DmB,OAAO,EAAEA,CAAA,KAAMlB,gBAAgB,CAAC,CAACD,aAAa,CAAE;cAAAU,QAAA,gBAEhDd,OAAA;gBAAGa,SAAS,EAAC;cAAuB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,aAEzC,eAAApB,OAAA;gBAAGa,SAAS,EAAG,kBAAiBT,aAAa,GAAG,MAAM,GAAG,OAAQ;cAAU;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACvE,eACXpB,OAAA,CAACJ,QAAQ;cAAC4B,EAAE,EAAEpB,aAAc;cAAAU,QAAA,eAC1Bd,OAAA;gBAAKa,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtBd,OAAA,CAACL,GAAG,CAACE,IAAI;kBACPyB,EAAE,EAAEzB,IAAK;kBACT4B,EAAE,EAAC,QAAQ;kBACXZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EACjE;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW,eACXpB,OAAA,CAACL,GAAG,CAACE,IAAI;kBACPyB,EAAE,EAAEzB,IAAK;kBACT4B,EAAE,EAAC,kBAAkB;kBACrBZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,kBAAkB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EAC3E;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXpB,OAAA,CAACL,GAAG,CAAC0B,IAAI;YAAAP,QAAA,eACPd,OAAA,CAACL,GAAG,CAACE,IAAI;cACPyB,EAAE,EAAEzB,IAAK;cACT4B,EAAE,EAAC,gBAAgB;cACnBZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,gBAAgB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAExEd,OAAA;gBAAGa,SAAS,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,WAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXpB,OAAA,CAACL,GAAG,CAAC0B,IAAI;YAAAP,QAAA,gBACPd,OAAA,CAACL,GAAG,CAACE,IAAI;cACPyB,EAAE,EAAC,KAAK;cACRT,SAAS,EAAG,gBAAeP,YAAY,GAAG,QAAQ,GAAG,EAAG,EAAE;cAC1DiB,OAAO,EAAEA,CAAA,KAAMhB,eAAe,CAAC,CAACD,YAAY,CAAE;cAAAQ,QAAA,gBAE9Cd,OAAA;gBAAGa,SAAS,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,YAE/B,eAAApB,OAAA;gBAAGa,SAAS,EAAG,kBAAiBP,YAAY,GAAG,MAAM,GAAG,OAAQ;cAAU;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACtE,eACXpB,OAAA,CAACJ,QAAQ;cAAC4B,EAAE,EAAElB,YAAa;cAAAQ,QAAA,eACzBd,OAAA;gBAAKa,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtBd,OAAA,CAACL,GAAG,CAACE,IAAI;kBACPyB,EAAE,EAAEzB,IAAK;kBACT4B,EAAE,EAAC,iBAAiB;kBACpBZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,iBAAiB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EAC1E;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW,eACXpB,OAAA,CAACL,GAAG,CAACE,IAAI;kBACPyB,EAAE,EAAEzB,IAAK;kBACT4B,EAAE,EAAC,mBAAmB;kBACtBZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,mBAAmB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EAC5E;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW,eACXpB,OAAA,CAACL,GAAG,CAACE,IAAI;kBACPyB,EAAE,EAAEzB,IAAK;kBACT4B,EAAE,EAAC,eAAe;kBAClBZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EACxE;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXpB,OAAA,CAACL,GAAG,CAAC0B,IAAI;YAAAP,QAAA,eACPd,OAAA,CAACL,GAAG,CAACE,IAAI;cACPyB,EAAE,EAAEzB,IAAK;cACT4B,EAAE,EAAC,eAAe;cAClBZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAEvEd,OAAA;gBAAGa,SAAS,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,UAElC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXpB,OAAA,CAACL,GAAG,CAAC0B,IAAI;YAAAP,QAAA,eACPd,OAAA,CAACL,GAAG,CAACE,IAAI;cACPyB,EAAE,EAAEzB,IAAK;cACT4B,EAAE,EAAC,eAAe;cAClBZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAEvEd,OAAA;gBAAGa,SAAS,EAAC;cAAsB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,UAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXpB,OAAA,CAACL,GAAG,CAAC0B,IAAI;YAAAP,QAAA,eACPd,OAAA,CAACL,GAAG,CAACE,IAAI;cACPyB,EAAE,EAAEzB,IAAK;cACT4B,EAAE,EAAC,gBAAgB;cACnBZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,gBAAgB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAExEd,OAAA;gBAAGa,SAAS,EAAC;cAAoB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,kBAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXpB,OAAA,CAACL,GAAG,CAAC0B,IAAI;YAAAP,QAAA,eACPd,OAAA,CAACL,GAAG,CAACE,IAAI;cACPyB,EAAE,EAAEzB,IAAK;cACT4B,EAAE,EAAC,gBAAgB;cACnBZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,gBAAgB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAExEd,OAAA;gBAAGa,SAAS,EAAC;cAAiB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,WAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXpB,OAAA,CAACL,GAAG,CAAC0B,IAAI;YAAAP,QAAA,gBACPd,OAAA,CAACL,GAAG,CAACE,IAAI;cACPyB,EAAE,EAAC,KAAK;cACRT,SAAS,EAAG,gBAAeL,mBAAmB,GAAG,QAAQ,GAAG,EAAG,EAAE;cACjEe,OAAO,EAAEA,CAAA,KAAMd,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;cAAAM,QAAA,gBAE5Dd,OAAA;gBAAGa,SAAS,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,oBAE/B,eAAApB,OAAA;gBAAGa,SAAS,EAAG,kBAAiBL,mBAAmB,GAAG,MAAM,GAAG,OAAQ;cAAU;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAC7E,eACXpB,OAAA,CAACJ,QAAQ;cAAC4B,EAAE,EAAEhB,mBAAoB;cAAAM,QAAA,eAChCd,OAAA;gBAAKa,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtBd,OAAA,CAACL,GAAG,CAACE,IAAI;kBACPyB,EAAE,EAAEzB,IAAK;kBACT4B,EAAE,EAAC,cAAc;kBACjBZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,cAAc,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EACvE;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW,eACXpB,OAAA,CAACL,GAAG,CAACE,IAAI;kBACPyB,EAAE,EAAEzB,IAAK;kBACT4B,EAAE,EAAC,gBAAgB;kBACnBZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,gBAAgB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EACzE;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXpB,OAAA,CAACL,GAAG,CAAC0B,IAAI;YAAAP,QAAA,eACPd,OAAA,CAACL,GAAG,CAACE,IAAI;cACPyB,EAAE,EAAEzB,IAAK;cACT4B,EAAE,EAAC,YAAY;cACfZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,YAAY,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAEpEd,OAAA;gBAAGa,SAAS,EAAC;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,OAEhC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXpB,OAAA,CAACL,GAAG,CAAC0B,IAAI;YAAAP,QAAA,eACPd,OAAA,CAACL,GAAG,CAACE,IAAI;cACPyB,EAAE,EAAEzB,IAAK;cACT4B,EAAE,EAAC,eAAe;cAClBZ,SAAS,EAAG,gBAAeH,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAEvEd,OAAA;gBAAGa,SAAS,EAAC;cAAkB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,UAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF,eAENpB,OAAA;MAAKa,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7Bd,OAAA;QAAKa,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7Bd,OAAA;UAAGa,SAAS,EAAC;QAAY;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK,eAC9BpB,OAAA;UAAAc,QAAA,EAAM;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAO;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACpB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACF;AAEV,CAAC;AAAClB,EAAA,CApNID,YAAY;EAAA,QACCH,WAAW;AAAA;AAAA4B,EAAA,GADxBzB,YAAY;AAsNlB,eAAeA,YAAY;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}