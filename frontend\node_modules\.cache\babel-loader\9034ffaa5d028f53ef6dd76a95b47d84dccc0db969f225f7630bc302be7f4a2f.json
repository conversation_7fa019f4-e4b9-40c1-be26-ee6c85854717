{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport createWithBsPrefix from './createWithBsPrefix';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormCheckInput from './FormCheckInput';\nimport InputGroupContext from './InputGroupContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupText = createWithBsPrefix('input-group-text', {\n  Component: 'span'\n});\nconst InputGroupCheckbox = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"checkbox\",\n    ...props\n  })\n});\nconst InputGroupRadio = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"radio\",\n    ...props\n  })\n});\n/**\n *\n * @property {InputGroupText} Text\n * @property {InputGroupRadio} Radio\n * @property {InputGroupCheckbox} Checkbox\n */\nconst InputGroup = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    size,\n    hasValidation,\n    className,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group');\n\n  // Intentionally an empty object. Used in detecting if a dropdown\n  // exists under an input group.\n  const contextValue = useMemo(() => ({}), []);\n  return /*#__PURE__*/_jsx(InputGroupContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix, size && `${bsPrefix}-${size}`, hasValidation && 'has-validation')\n    })\n  });\n});\nInputGroup.displayName = 'InputGroup';\nexport default Object.assign(InputGroup, {\n  Text: InputGroupText,\n  Radio: InputGroupRadio,\n  Checkbox: InputGroupCheckbox\n});", "map": {"version": 3, "names": ["classNames", "React", "useMemo", "createWithBsPrefix", "useBootstrapPrefix", "FormCheckInput", "InputGroupContext", "jsx", "_jsx", "InputGroupText", "Component", "InputGroupCheckbox", "props", "children", "type", "InputGroupRadio", "InputGroup", "forwardRef", "_ref", "ref", "bsPrefix", "size", "hasValidation", "className", "as", "contextValue", "Provider", "value", "displayName", "Object", "assign", "Text", "Radio", "Checkbox"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/InputGroup.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport createWithBsPrefix from './createWithBsPrefix';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormCheckInput from './FormCheckInput';\nimport InputGroupContext from './InputGroupContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupText = createWithBsPrefix('input-group-text', {\n  Component: 'span'\n});\nconst InputGroupCheckbox = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"checkbox\",\n    ...props\n  })\n});\nconst InputGroupRadio = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"radio\",\n    ...props\n  })\n});\n/**\n *\n * @property {InputGroupText} Text\n * @property {InputGroupRadio} Radio\n * @property {InputGroupCheckbox} Checkbox\n */\nconst InputGroup = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  hasValidation,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group');\n\n  // Intentionally an empty object. Used in detecting if a dropdown\n  // exists under an input group.\n  const contextValue = useMemo(() => ({}), []);\n  return /*#__PURE__*/_jsx(InputGroupContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix, size && `${bsPrefix}-${size}`, hasValidation && 'has-validation')\n    })\n  });\n});\nInputGroup.displayName = 'InputGroup';\nexport default Object.assign(InputGroup, {\n  Text: InputGroupText,\n  Radio: InputGroupRadio,\n  Checkbox: InputGroupCheckbox\n});"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAGN,kBAAkB,CAAC,kBAAkB,EAAE;EAC5DO,SAAS,EAAE;AACb,CAAC,CAAC;AACF,MAAMC,kBAAkB,GAAGC,KAAK,IAAI,aAAaJ,IAAI,CAACC,cAAc,EAAE;EACpEI,QAAQ,EAAE,aAAaL,IAAI,CAACH,cAAc,EAAE;IAC1CS,IAAI,EAAE,UAAU;IAChB,GAAGF;EACL,CAAC;AACH,CAAC,CAAC;AACF,MAAMG,eAAe,GAAGH,KAAK,IAAI,aAAaJ,IAAI,CAACC,cAAc,EAAE;EACjEI,QAAQ,EAAE,aAAaL,IAAI,CAACH,cAAc,EAAE;IAC1CS,IAAI,EAAE,OAAO;IACb,GAAGF;EACL,CAAC;AACH,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,UAAU,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,CAAAC,IAAA,EAQ9CC,GAAG,KAAK;EAAA,IARuC;IAChDC,QAAQ;IACRC,IAAI;IACJC,aAAa;IACbC,SAAS;IACT;IACAC,EAAE,EAAEd,SAAS,GAAG,KAAK;IACrB,GAAGE;EACL,CAAC,GAAAM,IAAA;EACCE,QAAQ,GAAGhB,kBAAkB,CAACgB,QAAQ,EAAE,aAAa,CAAC;;EAEtD;EACA;EACA,MAAMK,YAAY,GAAGvB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EAC5C,OAAO,aAAaM,IAAI,CAACF,iBAAiB,CAACoB,QAAQ,EAAE;IACnDC,KAAK,EAAEF,YAAY;IACnBZ,QAAQ,EAAE,aAAaL,IAAI,CAACE,SAAS,EAAE;MACrCS,GAAG,EAAEA,GAAG;MACR,GAAGP,KAAK;MACRW,SAAS,EAAEvB,UAAU,CAACuB,SAAS,EAAEH,QAAQ,EAAEC,IAAI,IAAK,GAAED,QAAS,IAAGC,IAAK,EAAC,EAAEC,aAAa,IAAI,gBAAgB;IAC7G,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,UAAU,CAACY,WAAW,GAAG,YAAY;AACrC,eAAeC,MAAM,CAACC,MAAM,CAACd,UAAU,EAAE;EACvCe,IAAI,EAAEtB,cAAc;EACpBuB,KAAK,EAAEjB,eAAe;EACtBkB,QAAQ,EAAEtB;AACZ,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}