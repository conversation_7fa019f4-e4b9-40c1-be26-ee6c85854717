{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\formContainer.jsx\";\nimport React from 'react';\nimport { Container, Row, Col } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction FormContainer(_ref) {\n  let {\n    children\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-md-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        md: 6,\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 9\n  }, this);\n}\n_c = FormContainer;\nexport default FormContainer;\nvar _c;\n$RefreshReg$(_c, \"FormContainer\");", "map": {"version": 3, "names": ["React", "Container", "Row", "Col", "jsxDEV", "_jsxDEV", "FormContainer", "_ref", "children", "className", "xs", "md", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/formContainer.jsx"], "sourcesContent": ["import React from 'react';\nimport {Container, <PERSON>, Col} from 'react-bootstrap'\n\nfunction FormContainer({children}) {\n    return (\n        <Container>\n            <Row className='justify-content-md-center'>\n                <Col xs={12} md={6}>\n                {children}\n                </Col>\n            </Row>\n        </Container>\n    );\n}\n\nexport default FormContainer;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAAQC,SAAS,EAAEC,GAAG,EAAEC,GAAG,QAAO,iBAAiB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,aAAaA,CAAAC,IAAA,EAAa;EAAA,IAAZ;IAACC;EAAQ,CAAC,GAAAD,IAAA;EAC7B,oBACIF,OAAA,CAACJ,SAAS;IAAAO,QAAA,eACNH,OAAA,CAACH,GAAG;MAACO,SAAS,EAAC,2BAA2B;MAAAD,QAAA,eACtCH,OAAA,CAACF,GAAG;QAACO,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAH,QAAA,EAClBA;MAAQ;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACJ;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACE;AAEpB;AAACC,EAAA,GAVQV,aAAa;AAYtB,eAAeA,aAAa;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}