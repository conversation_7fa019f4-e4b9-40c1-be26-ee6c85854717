{"ast": null, "code": "import createWithBsPrefix from './createWithBsPrefix';\nimport divWithClassName from './divWithClassName';\nconst DivStyledAsH5 = divWithClassName('h5');\nexport default createWithBsPrefix('offcanvas-title', {\n  Component: DivStyledAsH5\n});", "map": {"version": 3, "names": ["createWithBsPrefix", "divWithClassName", "DivStyledAsH5", "Component"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/OffcanvasTitle.js"], "sourcesContent": ["import createWithBsPrefix from './createWithBsPrefix';\nimport divWithClassName from './divWithClassName';\nconst DivStyledAsH5 = divWithClassName('h5');\nexport default createWithBsPrefix('offcanvas-title', {\n  Component: DivStyledAsH5\n});"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,MAAMC,aAAa,GAAGD,gBAAgB,CAAC,IAAI,CAAC;AAC5C,eAAeD,kBAAkB,CAAC,iBAAiB,EAAE;EACnDG,SAAS,EAAED;AACb,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}