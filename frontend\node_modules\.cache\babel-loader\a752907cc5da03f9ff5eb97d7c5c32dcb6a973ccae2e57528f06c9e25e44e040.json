{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AccordionItemContext from './AccordionItemContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AccordionItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    bsPrefix,\n    className,\n    eventKey,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-item');\n  const contextValue = useMemo(() => ({\n    eventKey\n  }), [eventKey]);\n  return /*#__PURE__*/_jsx(AccordionItemContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix)\n    })\n  });\n});\nAccordionItem.displayName = 'AccordionItem';\nexport default AccordionItem;", "map": {"version": 3, "names": ["classNames", "React", "useMemo", "useBootstrapPrefix", "AccordionItemContext", "jsx", "_jsx", "AccordionItem", "forwardRef", "_ref", "ref", "as", "Component", "bsPrefix", "className", "eventKey", "props", "contextValue", "Provider", "value", "children", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/AccordionItem.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AccordionItemContext from './AccordionItemContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AccordionItem = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  eventKey,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-item');\n  const contextValue = useMemo(() => ({\n    eventKey\n  }), [eventKey]);\n  return /*#__PURE__*/_jsx(AccordionItemContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix)\n    })\n  });\n});\nAccordionItem.displayName = 'AccordionItem';\nexport default AccordionItem;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAAC,IAAA,EAOjDC,GAAG,KAAK;EAAA,IAP0C;IACnD;IACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrBC,QAAQ;IACRC,SAAS;IACTC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAAP,IAAA;EACCI,QAAQ,GAAGV,kBAAkB,CAACU,QAAQ,EAAE,gBAAgB,CAAC;EACzD,MAAMI,YAAY,GAAGf,OAAO,CAAC,OAAO;IAClCa;EACF,CAAC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACf,OAAO,aAAaT,IAAI,CAACF,oBAAoB,CAACc,QAAQ,EAAE;IACtDC,KAAK,EAAEF,YAAY;IACnBG,QAAQ,EAAE,aAAad,IAAI,CAACM,SAAS,EAAE;MACrCF,GAAG,EAAEA,GAAG;MACR,GAAGM,KAAK;MACRF,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAED,QAAQ;IAC3C,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,aAAa,CAACc,WAAW,GAAG,eAAe;AAC3C,eAAed,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}