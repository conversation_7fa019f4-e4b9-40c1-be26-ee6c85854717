{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\header.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext } from \"react\";\nimport { Container, Nav, Navbar, NavDropdown } from \"react-bootstrap\";\nimport { LinkContainer } from \"react-router-bootstrap\";\nimport UserContext from \"../context/userContext\";\nimport SearchBox from \"./searchBox\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Header(_ref) {\n  _s();\n  let {\n    keyword,\n    setKeyword\n  } = _ref;\n  const {\n    userInfo\n  } = useContext(UserContext);\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    children: /*#__PURE__*/_jsxDEV(Navbar, {\n      bg: \"dark\",\n      variant: \"dark\",\n      expand: \"lg\",\n      collapseOnSelect: true,\n      children: /*#__PURE__*/_jsxDEV(Container, {\n        className: \"\",\n        children: [/*#__PURE__*/_jsxDEV(LinkContainer, {\n          to: \"/\",\n          children: /*#__PURE__*/_jsxDEV(Navbar.Brand, {\n            children: \"Proshop\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SearchBox, {\n          keyword: keyword,\n          setKeyword: setKeyword\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Navbar.Toggle, {\n          \"aria-controls\": \"basic-navbar-nav\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Navbar.Collapse, {\n          id: \"basic-navbar-nav\",\n          children: /*#__PURE__*/_jsxDEV(Nav, {\n            className: \"ms-auto\",\n            children: [/*#__PURE__*/_jsxDEV(LinkContainer, {\n              to: \"/cart\",\n              children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-shopping-cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 23,\n                  columnNumber: 19\n                }, this), \" Cart\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this), userInfo && /*#__PURE__*/_jsxDEV(NavDropdown, {\n              title: userInfo.username,\n              id: \"username\",\n              children: [/*#__PURE__*/_jsxDEV(LinkContainer, {\n                to: \"/profile\",\n                children: /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                  children: \"Profile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(LinkContainer, {\n                to: \"/logout\",\n                children: /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                  children: \"Logout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 17\n            }, this), !userInfo && /*#__PURE__*/_jsxDEV(LinkContainer, {\n              to: \"/login\",\n              children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-user\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 21\n                }, this), \" Login\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n_s(Header, \"/KD1s1jRBq3X+az2/mCvD2j7GYM=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useContext", "Container", "Nav", "<PERSON><PERSON><PERSON>", "NavDropdown", "LinkContainer", "UserContext", "SearchBox", "jsxDEV", "_jsxDEV", "Header", "_ref", "_s", "keyword", "setKeyword", "userInfo", "children", "bg", "variant", "expand", "collapseOnSelect", "className", "to", "Brand", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Toggle", "Collapse", "id", "Link", "title", "username", "<PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/header.jsx"], "sourcesContent": ["import React, { useContext } from \"react\";\nimport { Container, Nav, Navbar, NavDropdown } from \"react-bootstrap\";\nimport { LinkContainer } from \"react-router-bootstrap\";\nimport UserContext from \"../context/userContext\";\nimport SearchBox from \"./searchBox\";\n\nfunction Header({keyword,setKeyword}) {\n  const { userInfo } = useContext(UserContext);\n\n  return (\n    <header>\n      <Navbar bg=\"dark\" variant=\"dark\" expand=\"lg\" collapseOnSelect>\n        <Container className=\"\">\n          <LinkContainer to=\"/\">\n            <Navbar.Brand>Proshop</Navbar.Brand>\n          </LinkContainer>\n          <SearchBox keyword={keyword} setKeyword={setKeyword}/>\n          <Navbar.Toggle aria-controls=\"basic-navbar-nav\" />\n          <Navbar.Collapse id=\"basic-navbar-nav\">\n            <Nav className=\"ms-auto\">\n              <LinkContainer to=\"/cart\">\n                <Nav.Link>\n                  <i className=\"fas fa-shopping-cart\" /> Cart\n                </Nav.Link>\n              </LinkContainer>\n              {userInfo && (\n                <NavDropdown title={userInfo.username} id=\"username\">\n                  <LinkContainer to=\"/profile\">\n                    <NavDropdown.Item>Profile</NavDropdown.Item>\n                  </LinkContainer>\n                  <LinkContainer to=\"/logout\">\n                    <NavDropdown.Item>Logout</NavDropdown.Item>\n                  </LinkContainer>\n                </NavDropdown>\n              )}\n              {!userInfo && (\n                <LinkContainer to=\"/login\">\n                  <Nav.Link>\n                    <i className=\"fas fa-user\" /> Login\n                  </Nav.Link>\n                </LinkContainer>\n              )}\n            </Nav>\n          </Navbar.Collapse>\n        </Container>\n      </Navbar>\n    </header>\n  );\n}\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,SAAS,EAAEC,GAAG,EAAEC,MAAM,EAAEC,WAAW,QAAQ,iBAAiB;AACrE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,SAAS,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,SAASC,MAAMA,CAAAC,IAAA,EAAuB;EAAAC,EAAA;EAAA,IAAtB;IAACC,OAAO;IAACC;EAAU,CAAC,GAAAH,IAAA;EAClC,MAAM;IAAEI;EAAS,CAAC,GAAGf,UAAU,CAACM,WAAW,CAAC;EAE5C,oBACEG,OAAA;IAAAO,QAAA,eACEP,OAAA,CAACN,MAAM;MAACc,EAAE,EAAC,MAAM;MAACC,OAAO,EAAC,MAAM;MAACC,MAAM,EAAC,IAAI;MAACC,gBAAgB;MAAAJ,QAAA,eAC3DP,OAAA,CAACR,SAAS;QAACoB,SAAS,EAAC,EAAE;QAAAL,QAAA,gBACrBP,OAAA,CAACJ,aAAa;UAACiB,EAAE,EAAC,GAAG;UAAAN,QAAA,eACnBP,OAAA,CAACN,MAAM,CAACoB,KAAK;YAAAP,QAAA,EAAC;UAAO;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAe;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACtB,eAChBlB,OAAA,CAACF,SAAS;UAACM,OAAO,EAAEA,OAAQ;UAACC,UAAU,EAAEA;QAAW;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAE,eACtDlB,OAAA,CAACN,MAAM,CAACyB,MAAM;UAAC,iBAAc;QAAkB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,eAClDlB,OAAA,CAACN,MAAM,CAAC0B,QAAQ;UAACC,EAAE,EAAC,kBAAkB;UAAAd,QAAA,eACpCP,OAAA,CAACP,GAAG;YAACmB,SAAS,EAAC,SAAS;YAAAL,QAAA,gBACtBP,OAAA,CAACJ,aAAa;cAACiB,EAAE,EAAC,OAAO;cAAAN,QAAA,eACvBP,OAAA,CAACP,GAAG,CAAC6B,IAAI;gBAAAf,QAAA,gBACPP,OAAA;kBAAGY,SAAS,EAAC;gBAAsB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG,SACxC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAW;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACG,EACfZ,QAAQ,iBACPN,OAAA,CAACL,WAAW;cAAC4B,KAAK,EAAEjB,QAAQ,CAACkB,QAAS;cAACH,EAAE,EAAC,UAAU;cAAAd,QAAA,gBAClDP,OAAA,CAACJ,aAAa;gBAACiB,EAAE,EAAC,UAAU;gBAAAN,QAAA,eAC1BP,OAAA,CAACL,WAAW,CAAC8B,IAAI;kBAAAlB,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAmB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAC9B,eAChBlB,OAAA,CAACJ,aAAa;gBAACiB,EAAE,EAAC,SAAS;gBAAAN,QAAA,eACzBP,OAAA,CAACL,WAAW,CAAC8B,IAAI;kBAAAlB,QAAA,EAAC;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAAmB;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAC7B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAEnB,EACA,CAACZ,QAAQ,iBACRN,OAAA,CAACJ,aAAa;cAACiB,EAAE,EAAC,QAAQ;cAAAN,QAAA,eACxBP,OAAA,CAACP,GAAG,CAAC6B,IAAI;gBAAAf,QAAA,gBACPP,OAAA;kBAAGY,SAAS,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAG,UAC/B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAW;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAEd;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACG;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACU;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACL;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACF;AAEb;AAACf,EAAA,CA1CQF,MAAM;AAAAyB,EAAA,GAANzB,MAAM;AA4Cf,eAAeA,MAAM;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}