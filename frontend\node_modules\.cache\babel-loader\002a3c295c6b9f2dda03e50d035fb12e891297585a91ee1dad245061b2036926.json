{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useIsRTL } from './ThemeProvider';\nimport { getOverlayDirection } from './helpers';\nimport getInitialPopperStyles from './getInitialPopperStyles';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst defaultProps = {\n  placement: 'right'\n};\nconst Tooltip = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    placement,\n    className,\n    style,\n    children,\n    arrowProps,\n    hasDoneInitialMeasure,\n    popper,\n    show,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'tooltip');\n  const isRTL = useIsRTL();\n  const [primaryPlacement] = (placement == null ? void 0 : placement.split('-')) || [];\n  const bsDirection = getOverlayDirection(primaryPlacement, isRTL);\n  let computedStyle = style;\n  if (show && !hasDoneInitialMeasure) {\n    computedStyle = {\n      ...style,\n      ...getInitialPopperStyles(popper == null ? void 0 : popper.strategy)\n    };\n  }\n  return /*#__PURE__*/_jsxs(\"div\", {\n    ref: ref,\n    style: computedStyle,\n    role: \"tooltip\",\n    \"x-placement\": primaryPlacement,\n    className: classNames(className, bsPrefix, `bs-tooltip-${bsDirection}`),\n    ...props,\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      className: \"tooltip-arrow\",\n      ...arrowProps\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: `${bsPrefix}-inner`,\n      children: children\n    })]\n  });\n});\nTooltip.defaultProps = defaultProps;\nTooltip.displayName = 'Tooltip';\nexport default Tooltip;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "useIsRTL", "getOverlayDirection", "getInitialPopperStyles", "jsx", "_jsx", "jsxs", "_jsxs", "defaultProps", "placement", "<PERSON><PERSON><PERSON>", "forwardRef", "_ref", "ref", "bsPrefix", "className", "style", "children", "arrowProps", "hasDoneInitialMeasure", "popper", "show", "props", "isRTL", "primaryPlacement", "split", "bsDirection", "computedStyle", "strategy", "role", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Tooltip.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useIsRTL } from './ThemeProvider';\nimport { getOverlayDirection } from './helpers';\nimport getInitialPopperStyles from './getInitialPopperStyles';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst defaultProps = {\n  placement: 'right'\n};\nconst Tooltip = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  placement,\n  className,\n  style,\n  children,\n  arrowProps,\n  hasDoneInitialMeasure,\n  popper,\n  show,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'tooltip');\n  const isRTL = useIsRTL();\n  const [primaryPlacement] = (placement == null ? void 0 : placement.split('-')) || [];\n  const bsDirection = getOverlayDirection(primaryPlacement, isRTL);\n  let computedStyle = style;\n  if (show && !hasDoneInitialMeasure) {\n    computedStyle = {\n      ...style,\n      ...getInitialPopperStyles(popper == null ? void 0 : popper.strategy)\n    };\n  }\n  return /*#__PURE__*/_jsxs(\"div\", {\n    ref: ref,\n    style: computedStyle,\n    role: \"tooltip\",\n    \"x-placement\": primaryPlacement,\n    className: classNames(className, bsPrefix, `bs-tooltip-${bsDirection}`),\n    ...props,\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      className: \"tooltip-arrow\",\n      ...arrowProps\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: `${bsPrefix}-inner`,\n      children: children\n    })]\n  });\n});\nTooltip.defaultProps = defaultProps;\nTooltip.displayName = 'Tooltip';\nexport default Tooltip;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,EAAEC,QAAQ,QAAQ,iBAAiB;AAC9D,SAASC,mBAAmB,QAAQ,WAAW;AAC/C,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,YAAY,GAAG;EACnBC,SAAS,EAAE;AACb,CAAC;AACD,MAAMC,OAAO,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,CAAAC,IAAA,EAW3CC,GAAG,KAAK;EAAA,IAXoC;IAC7CC,QAAQ;IACRL,SAAS;IACTM,SAAS;IACTC,KAAK;IACLC,QAAQ;IACRC,UAAU;IACVC,qBAAqB;IACrBC,MAAM;IACNC,IAAI;IACJ,GAAGC;EACL,CAAC,GAAAV,IAAA;EACCE,QAAQ,GAAGd,kBAAkB,CAACc,QAAQ,EAAE,SAAS,CAAC;EAClD,MAAMS,KAAK,GAAGtB,QAAQ,EAAE;EACxB,MAAM,CAACuB,gBAAgB,CAAC,GAAG,CAACf,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACgB,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE;EACpF,MAAMC,WAAW,GAAGxB,mBAAmB,CAACsB,gBAAgB,EAAED,KAAK,CAAC;EAChE,IAAII,aAAa,GAAGX,KAAK;EACzB,IAAIK,IAAI,IAAI,CAACF,qBAAqB,EAAE;IAClCQ,aAAa,GAAG;MACd,GAAGX,KAAK;MACR,GAAGb,sBAAsB,CAACiB,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACQ,QAAQ;IACrE,CAAC;EACH;EACA,OAAO,aAAarB,KAAK,CAAC,KAAK,EAAE;IAC/BM,GAAG,EAAEA,GAAG;IACRG,KAAK,EAAEW,aAAa;IACpBE,IAAI,EAAE,SAAS;IACf,aAAa,EAAEL,gBAAgB;IAC/BT,SAAS,EAAEjB,UAAU,CAACiB,SAAS,EAAED,QAAQ,EAAG,cAAaY,WAAY,EAAC,CAAC;IACvE,GAAGJ,KAAK;IACRL,QAAQ,EAAE,CAAC,aAAaZ,IAAI,CAAC,KAAK,EAAE;MAClCU,SAAS,EAAE,eAAe;MAC1B,GAAGG;IACL,CAAC,CAAC,EAAE,aAAab,IAAI,CAAC,KAAK,EAAE;MAC3BU,SAAS,EAAG,GAAED,QAAS,QAAO;MAC9BG,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFP,OAAO,CAACF,YAAY,GAAGA,YAAY;AACnCE,OAAO,CAACoB,WAAW,GAAG,SAAS;AAC/B,eAAepB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}