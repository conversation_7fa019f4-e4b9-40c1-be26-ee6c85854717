{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\context\\\\userContext.js\",\n  _s = $RefreshSig$();\nimport { createContext, useState, useEffect } from \"react\";\nimport httpService from \"../services/httpService\";\nimport Loader from \"../components/loader\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserContext = /*#__PURE__*/createContext();\nexport default UserContext;\nexport const UserProvider = _ref => {\n  _s();\n  let {\n    children\n  } = _ref;\n  const [authTokens, setAuthTokens] = useState(localStorage.getItem(\"authTokens\") ? JSON.parse(localStorage.getItem(\"authTokens\")) : null);\n  const [userInfo, setUserInfo] = useState(localStorage.getItem(\"userInfo\") ? JSON.parse(localStorage.getItem(\"userInfo\")) : null);\n  const [error, setError] = useState(\"\");\n  const [loading, setLoading] = useState(true);\n  const login = async (username, password) => {\n    try {\n      const {\n        data\n      } = await httpService.post(\"/auth/jwt/create/\", {\n        username,\n        password\n      });\n      // httpService.setJwt(data.access);\n      setAuthTokens({\n        access: data.access,\n        refresh: data.refresh\n      });\n      setUserInfo({\n        username: data.username,\n        email: data.email,\n        isAdmin: data.isAdmin\n      });\n      localStorage.setItem(\"authTokens\", JSON.stringify({\n        access: data.access,\n        refresh: data.refresh\n      }));\n      localStorage.setItem(\"userInfo\", JSON.stringify({\n        username: data.username,\n        email: data.email,\n        isAdmin: data.isAdmin\n      }));\n      setError(\"\");\n      return true;\n    } catch (ex) {\n      setError({\n        login: ex.response.data\n      });\n      return false;\n    }\n  };\n  const register = async (username, email, password) => {\n    try {\n      const {\n        data\n      } = await httpService.post(\"/auth/users/\", {\n        username,\n        email,\n        password\n      });\n      await login(username, password);\n      return true;\n    } catch (ex) {\n      setError({\n        register: ex.response.data\n      });\n      return false;\n    }\n  };\n  const logout = () => {\n    setAuthTokens(null);\n    setUserInfo(null);\n    localStorage.removeItem(\"authTokens\");\n    localStorage.removeItem(\"userInfo\");\n    // httpService.setJwt(undefined)\n  };\n\n  const refresh = async () => {\n    try {\n      const {\n        data\n      } = await httpService.post(\"/auth/jwt/refresh/\", {\n        refresh: authTokens.refresh\n      });\n      // httpService.setJwt(data.access)\n      setAuthTokens({\n        access: data.access,\n        refresh: data.refresh\n      });\n      localStorage.setItem(\"authTokens\", JSON.stringify({\n        access: data.access,\n        refresh: data.refresh\n      }));\n    } catch (ex) {\n      var _ex$response, _ex$response2;\n      // ✅ Chỉ logout khi refresh token thực sự hết hạn\n      console.error(\"Token refresh failed:\", (_ex$response = ex.response) === null || _ex$response === void 0 ? void 0 : _ex$response.status);\n      if (((_ex$response2 = ex.response) === null || _ex$response2 === void 0 ? void 0 : _ex$response2.status) === 401) {\n        logout();\n      }\n    }\n  };\n  useEffect(() => {\n    if (authTokens) {\n      refresh();\n    }\n    setLoading(false);\n  }, []);\n  useEffect(() => {\n    httpService.setJwt(authTokens && authTokens.access ? authTokens.access : null);\n  }, [loading, authTokens]);\n  useEffect(() => {\n    let timeInterval = 1000 * 60 * 60; // Refresh tokens after every 1 hour\n    const interval = setInterval(() => {\n      if (authTokens) refresh();\n    }, timeInterval);\n    return () => clearInterval(interval);\n  }, [authTokens]);\n  const updateProfile = async (username, email, password) => {\n    try {\n      let flag = 0,\n        payload = {};\n      if (username != userInfo.username && username != \"\") {\n        payload.username = username;\n        flag = 1;\n      }\n      if (email != userInfo.email && email != \"\") {\n        payload.email = email;\n        flag = 1;\n      }\n      if (password != \"\") {\n        payload.password = password;\n        flag = 1;\n      }\n      if (flag == 0) return;\n      const {\n        data\n      } = await httpService.patch(\"/auth/users/me/\", payload);\n      setUserInfo({\n        username: data.username,\n        email: data.email,\n        isAdmin: userInfo.isAdmin\n      });\n      localStorage.setItem(\"userInfo\", JSON.stringify({\n        username: data.username,\n        email: data.email,\n        isAdmin: userInfo.isAdmin\n      }));\n      setError(\"\");\n      return true;\n    } catch (ex) {\n      return false;\n    }\n  };\n  const contextData = {\n    authTokens,\n    userInfo,\n    error,\n    login,\n    register,\n    refresh,\n    logout,\n    updateProfile\n  };\n  return /*#__PURE__*/_jsxDEV(UserContext.Provider, {\n    value: contextData,\n    children: [loading && /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 19\n    }, this), !loading && children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this);\n};\n_s(UserProvider, \"VZGFj2hl1VTJp7KbAor4oxxC5gs=\");\n_c = UserProvider;\nvar _c;\n$RefreshReg$(_c, \"UserProvider\");", "map": {"version": 3, "names": ["createContext", "useState", "useEffect", "httpService", "Loader", "jsxDEV", "_jsxDEV", "UserContext", "UserProvider", "_ref", "_s", "children", "authTokens", "setAuthTokens", "localStorage", "getItem", "JSON", "parse", "userInfo", "setUserInfo", "error", "setError", "loading", "setLoading", "login", "username", "password", "data", "post", "access", "refresh", "email", "isAdmin", "setItem", "stringify", "ex", "response", "register", "logout", "removeItem", "_ex$response", "_ex$response2", "console", "status", "setJwt", "timeInterval", "interval", "setInterval", "clearInterval", "updateProfile", "flag", "payload", "patch", "contextData", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/context/userContext.js"], "sourcesContent": ["import { createContext, useState, useEffect } from \"react\";\nimport httpService from \"../services/httpService\";\nimport Loader from \"../components/loader\";\n\nconst UserContext = createContext();\n\nexport default UserContext;\n\nexport const UserProvider = ({ children }) => {\n  const [authTokens, setAuthTokens] = useState(\n    localStorage.getItem(\"authTokens\")\n      ? JSON.parse(localStorage.getItem(\"authTokens\"))\n      : null\n  );\n  const [userInfo, setUserInfo] = useState(\n    localStorage.getItem(\"userInfo\")\n      ? JSON.parse(localStorage.getItem(\"userInfo\"))\n      : null\n  );\n  const [error, setError] = useState(\"\");\n  const [loading, setLoading] = useState(true);\n\n  const login = async (username, password) => {\n    try {\n      const { data } = await httpService.post(\"/auth/jwt/create/\", {\n        username,\n        password,\n      });\n      // httpService.setJwt(data.access);\n      setAuthTokens({ access: data.access, refresh: data.refresh });\n      setUserInfo({\n        username: data.username,\n        email: data.email,\n        isAdmin: data.isAdmin,\n      });\n      localStorage.setItem(\n        \"authTokens\",\n        JSON.stringify({ access: data.access, refresh: data.refresh })\n      );\n      localStorage.setItem(\n        \"userInfo\",\n        JSON.stringify({\n          username: data.username,\n          email: data.email,\n          isAdmin: data.isAdmin,\n        })\n      );\n      setError(\"\");\n      return true;\n    } catch (ex) {\n      setError({ login: ex.response.data });\n      return false;\n    }\n  };\n\n  const register = async (username, email, password) => {\n    try {\n      const { data } = await httpService.post(\"/auth/users/\", {\n        username,\n        email,\n        password,\n      });\n      await login(username, password);\n      return true;\n    } catch (ex) {\n      setError({ register: ex.response.data });\n      return false;\n    }\n  };\n\n  const logout = () => {\n    setAuthTokens(null);\n    setUserInfo(null);\n    localStorage.removeItem(\"authTokens\");\n    localStorage.removeItem(\"userInfo\");\n    // httpService.setJwt(undefined)\n  };\n\n  const refresh = async () => {\n    try {\n      const { data } = await httpService.post(\"/auth/jwt/refresh/\", {\n        refresh: authTokens.refresh,\n      });\n      // httpService.setJwt(data.access)\n      setAuthTokens({ access: data.access, refresh: data.refresh });\n      localStorage.setItem(\n        \"authTokens\",\n        JSON.stringify({ access: data.access, refresh: data.refresh })\n      );\n    } catch (ex) {\n      // ✅ Chỉ logout khi refresh token thực sự hết hạn\n      console.error(\"Token refresh failed:\", ex.response?.status);\n      if (ex.response?.status === 401) {\n        logout();\n      }\n    }\n  };\n\n  useEffect(() => {\n    if (authTokens) {\n      refresh();\n    }\n    setLoading(false);\n  }, []);\n\n  useEffect(() => {\n    httpService.setJwt(authTokens && authTokens.access ? authTokens.access : null);\n  },[loading,authTokens])\n\n  useEffect(() => {\n    let timeInterval = 1000 * 60 * 60; // Refresh tokens after every 1 hour\n    const interval = setInterval(() => {\n      if (authTokens) refresh();\n    }, timeInterval);\n    return () => clearInterval(interval);\n  }, [authTokens]);\n\n  const updateProfile = async (username, email, password) => {\n    try {\n      let flag = 0,payload = {};\n      if (username != userInfo.username && username != \"\") {\n        payload.username = username;\n        flag = 1;\n      }\n      if (email != userInfo.email && email != \"\") {\n        payload.email = email;\n        flag = 1;\n      }\n      if (password != \"\") {\n        payload.password = password;\n        flag = 1;\n      }\n      if (flag == 0) return;\n\n      const { data } = await httpService.patch(\"/auth/users/me/\", payload);\n      setUserInfo({\n        username: data.username,\n        email: data.email,\n        isAdmin: userInfo.isAdmin,\n      });\n      localStorage.setItem(\n        \"userInfo\",\n        JSON.stringify({\n          username: data.username,\n          email: data.email,\n          isAdmin: userInfo.isAdmin,\n        })\n      );\n      setError(\"\");\n      return true;\n    } catch (ex) {\n      return false;\n    }\n  };\n\n  const contextData = {\n    authTokens,\n    userInfo,\n    error,\n    login,\n    register,\n    refresh,\n    logout,\n    updateProfile,\n  };\n\n  return (\n    <UserContext.Provider value={contextData}>\n      {loading && <Loader />}\n      {!loading && children}\n    </UserContext.Provider>\n  );\n};\n"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,WAAW,gBAAGP,aAAa,EAAE;AAEnC,eAAeO,WAAW;AAE1B,OAAO,MAAMC,YAAY,GAAGC,IAAA,IAAkB;EAAAC,EAAA;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAF,IAAA;EACvC,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAC1Ca,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAC9BC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,GAC9C,IAAI,CACT;EACD,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CACtCa,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,GAC5BC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,GAC5C,IAAI,CACT;EACD,MAAM,CAACK,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMuB,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAM;QAAEC;MAAK,CAAC,GAAG,MAAMxB,WAAW,CAACyB,IAAI,CAAC,mBAAmB,EAAE;QAC3DH,QAAQ;QACRC;MACF,CAAC,CAAC;MACF;MACAb,aAAa,CAAC;QAAEgB,MAAM,EAAEF,IAAI,CAACE,MAAM;QAAEC,OAAO,EAAEH,IAAI,CAACG;MAAQ,CAAC,CAAC;MAC7DX,WAAW,CAAC;QACVM,QAAQ,EAAEE,IAAI,CAACF,QAAQ;QACvBM,KAAK,EAAEJ,IAAI,CAACI,KAAK;QACjBC,OAAO,EAAEL,IAAI,CAACK;MAChB,CAAC,CAAC;MACFlB,YAAY,CAACmB,OAAO,CAClB,YAAY,EACZjB,IAAI,CAACkB,SAAS,CAAC;QAAEL,MAAM,EAAEF,IAAI,CAACE,MAAM;QAAEC,OAAO,EAAEH,IAAI,CAACG;MAAQ,CAAC,CAAC,CAC/D;MACDhB,YAAY,CAACmB,OAAO,CAClB,UAAU,EACVjB,IAAI,CAACkB,SAAS,CAAC;QACbT,QAAQ,EAAEE,IAAI,CAACF,QAAQ;QACvBM,KAAK,EAAEJ,IAAI,CAACI,KAAK;QACjBC,OAAO,EAAEL,IAAI,CAACK;MAChB,CAAC,CAAC,CACH;MACDX,QAAQ,CAAC,EAAE,CAAC;MACZ,OAAO,IAAI;IACb,CAAC,CAAC,OAAOc,EAAE,EAAE;MACXd,QAAQ,CAAC;QAAEG,KAAK,EAAEW,EAAE,CAACC,QAAQ,CAACT;MAAK,CAAC,CAAC;MACrC,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMU,QAAQ,GAAG,MAAAA,CAAOZ,QAAQ,EAAEM,KAAK,EAAEL,QAAQ,KAAK;IACpD,IAAI;MACF,MAAM;QAAEC;MAAK,CAAC,GAAG,MAAMxB,WAAW,CAACyB,IAAI,CAAC,cAAc,EAAE;QACtDH,QAAQ;QACRM,KAAK;QACLL;MACF,CAAC,CAAC;MACF,MAAMF,KAAK,CAACC,QAAQ,EAAEC,QAAQ,CAAC;MAC/B,OAAO,IAAI;IACb,CAAC,CAAC,OAAOS,EAAE,EAAE;MACXd,QAAQ,CAAC;QAAEgB,QAAQ,EAAEF,EAAE,CAACC,QAAQ,CAACT;MAAK,CAAC,CAAC;MACxC,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMW,MAAM,GAAGA,CAAA,KAAM;IACnBzB,aAAa,CAAC,IAAI,CAAC;IACnBM,WAAW,CAAC,IAAI,CAAC;IACjBL,YAAY,CAACyB,UAAU,CAAC,YAAY,CAAC;IACrCzB,YAAY,CAACyB,UAAU,CAAC,UAAU,CAAC;IACnC;EACF,CAAC;;EAED,MAAMT,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAM;QAAEH;MAAK,CAAC,GAAG,MAAMxB,WAAW,CAACyB,IAAI,CAAC,oBAAoB,EAAE;QAC5DE,OAAO,EAAElB,UAAU,CAACkB;MACtB,CAAC,CAAC;MACF;MACAjB,aAAa,CAAC;QAAEgB,MAAM,EAAEF,IAAI,CAACE,MAAM;QAAEC,OAAO,EAAEH,IAAI,CAACG;MAAQ,CAAC,CAAC;MAC7DhB,YAAY,CAACmB,OAAO,CAClB,YAAY,EACZjB,IAAI,CAACkB,SAAS,CAAC;QAAEL,MAAM,EAAEF,IAAI,CAACE,MAAM;QAAEC,OAAO,EAAEH,IAAI,CAACG;MAAQ,CAAC,CAAC,CAC/D;IACH,CAAC,CAAC,OAAOK,EAAE,EAAE;MAAA,IAAAK,YAAA,EAAAC,aAAA;MACX;MACAC,OAAO,CAACtB,KAAK,CAAC,uBAAuB,GAAAoB,YAAA,GAAEL,EAAE,CAACC,QAAQ,cAAAI,YAAA,uBAAXA,YAAA,CAAaG,MAAM,CAAC;MAC3D,IAAI,EAAAF,aAAA,GAAAN,EAAE,CAACC,QAAQ,cAAAK,aAAA,uBAAXA,aAAA,CAAaE,MAAM,MAAK,GAAG,EAAE;QAC/BL,MAAM,EAAE;MACV;IACF;EACF,CAAC;EAEDpC,SAAS,CAAC,MAAM;IACd,IAAIU,UAAU,EAAE;MACdkB,OAAO,EAAE;IACX;IACAP,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAENrB,SAAS,CAAC,MAAM;IACdC,WAAW,CAACyC,MAAM,CAAChC,UAAU,IAAIA,UAAU,CAACiB,MAAM,GAAGjB,UAAU,CAACiB,MAAM,GAAG,IAAI,CAAC;EAChF,CAAC,EAAC,CAACP,OAAO,EAACV,UAAU,CAAC,CAAC;EAEvBV,SAAS,CAAC,MAAM;IACd,IAAI2C,YAAY,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACnC,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,IAAInC,UAAU,EAAEkB,OAAO,EAAE;IAC3B,CAAC,EAAEe,YAAY,CAAC;IAChB,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAClC,UAAU,CAAC,CAAC;EAEhB,MAAMqC,aAAa,GAAG,MAAAA,CAAOxB,QAAQ,EAAEM,KAAK,EAAEL,QAAQ,KAAK;IACzD,IAAI;MACF,IAAIwB,IAAI,GAAG,CAAC;QAACC,OAAO,GAAG,CAAC,CAAC;MACzB,IAAI1B,QAAQ,IAAIP,QAAQ,CAACO,QAAQ,IAAIA,QAAQ,IAAI,EAAE,EAAE;QACnD0B,OAAO,CAAC1B,QAAQ,GAAGA,QAAQ;QAC3ByB,IAAI,GAAG,CAAC;MACV;MACA,IAAInB,KAAK,IAAIb,QAAQ,CAACa,KAAK,IAAIA,KAAK,IAAI,EAAE,EAAE;QAC1CoB,OAAO,CAACpB,KAAK,GAAGA,KAAK;QACrBmB,IAAI,GAAG,CAAC;MACV;MACA,IAAIxB,QAAQ,IAAI,EAAE,EAAE;QAClByB,OAAO,CAACzB,QAAQ,GAAGA,QAAQ;QAC3BwB,IAAI,GAAG,CAAC;MACV;MACA,IAAIA,IAAI,IAAI,CAAC,EAAE;MAEf,MAAM;QAAEvB;MAAK,CAAC,GAAG,MAAMxB,WAAW,CAACiD,KAAK,CAAC,iBAAiB,EAAED,OAAO,CAAC;MACpEhC,WAAW,CAAC;QACVM,QAAQ,EAAEE,IAAI,CAACF,QAAQ;QACvBM,KAAK,EAAEJ,IAAI,CAACI,KAAK;QACjBC,OAAO,EAAEd,QAAQ,CAACc;MACpB,CAAC,CAAC;MACFlB,YAAY,CAACmB,OAAO,CAClB,UAAU,EACVjB,IAAI,CAACkB,SAAS,CAAC;QACbT,QAAQ,EAAEE,IAAI,CAACF,QAAQ;QACvBM,KAAK,EAAEJ,IAAI,CAACI,KAAK;QACjBC,OAAO,EAAEd,QAAQ,CAACc;MACpB,CAAC,CAAC,CACH;MACDX,QAAQ,CAAC,EAAE,CAAC;MACZ,OAAO,IAAI;IACb,CAAC,CAAC,OAAOc,EAAE,EAAE;MACX,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMkB,WAAW,GAAG;IAClBzC,UAAU;IACVM,QAAQ;IACRE,KAAK;IACLI,KAAK;IACLa,QAAQ;IACRP,OAAO;IACPQ,MAAM;IACNW;EACF,CAAC;EAED,oBACE3C,OAAA,CAACC,WAAW,CAAC+C,QAAQ;IAACC,KAAK,EAAEF,WAAY;IAAA1C,QAAA,GACtCW,OAAO,iBAAIhB,OAAA,CAACF,MAAM;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG,EACrB,CAACrC,OAAO,IAAIX,QAAQ;EAAA;IAAA6C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACA;AAE3B,CAAC;AAACjD,EAAA,CApKWF,YAAY;AAAAoD,EAAA,GAAZpD,YAAY;AAAA,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}