{"ast": null, "code": "import createWithBsPrefix from './createWithBsPrefix';\nexport default createWithBsPrefix('form-floating');", "map": {"version": 3, "names": ["createWithBsPrefix"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/FormFloating.js"], "sourcesContent": ["import createWithBsPrefix from './createWithBsPrefix';\nexport default createWithBsPrefix('form-floating');"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,sBAAsB;AACrD,eAAeA,kBAAkB,CAAC,eAAe,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}