{"ast": null, "code": "import * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const DEFAULT_BREAKPOINTS = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];\nexport const DEFAULT_MIN_BREAKPOINT = 'xs';\nconst ThemeContext = /*#__PURE__*/React.createContext({\n  prefixes: {},\n  breakpoints: DEFAULT_BREAKPOINTS,\n  minBreakpoint: DEFAULT_MIN_BREAKPOINT\n});\nconst {\n  Consumer,\n  Provider\n} = ThemeContext;\nfunction ThemeProvider(_ref) {\n  let {\n    prefixes = {},\n    breakpoints = DEFAULT_BREAKPOINTS,\n    minBreakpoint = DEFAULT_MIN_BREAKPOINT,\n    dir,\n    children\n  } = _ref;\n  const contextValue = useMemo(() => ({\n    prefixes: {\n      ...prefixes\n    },\n    breakpoints,\n    minBreakpoint,\n    dir\n  }), [prefixes, breakpoints, minBreakpoint, dir]);\n  return /*#__PURE__*/_jsx(Provider, {\n    value: contextValue,\n    children: children\n  });\n}\nexport function useBootstrapPrefix(prefix, defaultPrefix) {\n  const {\n    prefixes\n  } = useContext(ThemeContext);\n  return prefix || prefixes[defaultPrefix] || defaultPrefix;\n}\nexport function useBootstrapBreakpoints() {\n  const {\n    breakpoints\n  } = useContext(ThemeContext);\n  return breakpoints;\n}\nexport function useBootstrapMinBreakpoint() {\n  const {\n    minBreakpoint\n  } = useContext(ThemeContext);\n  return minBreakpoint;\n}\nexport function useIsRTL() {\n  const {\n    dir\n  } = useContext(ThemeContext);\n  return dir === 'rtl';\n}\nfunction createBootstrapComponent(Component, opts) {\n  if (typeof opts === 'string') opts = {\n    prefix: opts\n  };\n  const isClassy = Component.prototype && Component.prototype.isReactComponent;\n  // If it's a functional component make sure we don't break it with a ref\n  const {\n    prefix,\n    forwardRefAs = isClassy ? 'ref' : 'innerRef'\n  } = opts;\n  const Wrapped = /*#__PURE__*/React.forwardRef((_ref2, ref) => {\n    let {\n      ...props\n    } = _ref2;\n    props[forwardRefAs] = ref;\n    const bsPrefix = useBootstrapPrefix(props.bsPrefix, prefix);\n    return /*#__PURE__*/_jsx(Component, {\n      ...props,\n      bsPrefix: bsPrefix\n    });\n  });\n  Wrapped.displayName = `Bootstrap(${Component.displayName || Component.name})`;\n  return Wrapped;\n}\nexport { createBootstrapComponent, Consumer as ThemeConsumer };\nexport default ThemeProvider;", "map": {"version": 3, "names": ["React", "useContext", "useMemo", "jsx", "_jsx", "DEFAULT_BREAKPOINTS", "DEFAULT_MIN_BREAKPOINT", "ThemeContext", "createContext", "prefixes", "breakpoints", "minBreakpoint", "Consumer", "Provider", "ThemeProvider", "_ref", "dir", "children", "contextValue", "value", "useBootstrapPrefix", "prefix", "defaultPrefix", "useBootstrapBreakpoints", "useBootstrapMinBreakpoint", "useIsRTL", "createBootstrapComponent", "Component", "opts", "isClassy", "prototype", "isReactComponent", "forwardRefAs", "Wrapped", "forwardRef", "_ref2", "ref", "props", "bsPrefix", "displayName", "name", "ThemeConsumer"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/ThemeProvider.js"], "sourcesContent": ["import * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const DEFAULT_BREAKPOINTS = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];\nexport const DEFAULT_MIN_BREAKPOINT = 'xs';\nconst ThemeContext = /*#__PURE__*/React.createContext({\n  prefixes: {},\n  breakpoints: DEFAULT_BREAKPOINTS,\n  minBreakpoint: DEFAULT_MIN_BREAKPOINT\n});\nconst {\n  Consumer,\n  Provider\n} = ThemeContext;\nfunction ThemeProvider({\n  prefixes = {},\n  breakpoints = DEFAULT_BREAKPOINTS,\n  minBreakpoint = DEFAULT_MIN_BREAKPOINT,\n  dir,\n  children\n}) {\n  const contextValue = useMemo(() => ({\n    prefixes: {\n      ...prefixes\n    },\n    breakpoints,\n    minBreakpoint,\n    dir\n  }), [prefixes, breakpoints, minBreakpoint, dir]);\n  return /*#__PURE__*/_jsx(Provider, {\n    value: contextValue,\n    children: children\n  });\n}\nexport function useBootstrapPrefix(prefix, defaultPrefix) {\n  const {\n    prefixes\n  } = useContext(ThemeContext);\n  return prefix || prefixes[defaultPrefix] || defaultPrefix;\n}\nexport function useBootstrapBreakpoints() {\n  const {\n    breakpoints\n  } = useContext(ThemeContext);\n  return breakpoints;\n}\nexport function useBootstrapMinBreakpoint() {\n  const {\n    minBreakpoint\n  } = useContext(ThemeContext);\n  return minBreakpoint;\n}\nexport function useIsRTL() {\n  const {\n    dir\n  } = useContext(ThemeContext);\n  return dir === 'rtl';\n}\nfunction createBootstrapComponent(Component, opts) {\n  if (typeof opts === 'string') opts = {\n    prefix: opts\n  };\n  const isClassy = Component.prototype && Component.prototype.isReactComponent;\n  // If it's a functional component make sure we don't break it with a ref\n  const {\n    prefix,\n    forwardRefAs = isClassy ? 'ref' : 'innerRef'\n  } = opts;\n  const Wrapped = /*#__PURE__*/React.forwardRef(({\n    ...props\n  }, ref) => {\n    props[forwardRefAs] = ref;\n    const bsPrefix = useBootstrapPrefix(props.bsPrefix, prefix);\n    return /*#__PURE__*/_jsx(Component, {\n      ...props,\n      bsPrefix: bsPrefix\n    });\n  });\n  Wrapped.displayName = `Bootstrap(${Component.displayName || Component.name})`;\n  return Wrapped;\n}\nexport { createBootstrapComponent, Consumer as ThemeConsumer };\nexport default ThemeProvider;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,mBAAmB,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACxE,OAAO,MAAMC,sBAAsB,GAAG,IAAI;AAC1C,MAAMC,YAAY,GAAG,aAAaP,KAAK,CAACQ,aAAa,CAAC;EACpDC,QAAQ,EAAE,CAAC,CAAC;EACZC,WAAW,EAAEL,mBAAmB;EAChCM,aAAa,EAAEL;AACjB,CAAC,CAAC;AACF,MAAM;EACJM,QAAQ;EACRC;AACF,CAAC,GAAGN,YAAY;AAChB,SAASO,aAAaA,CAAAC,IAAA,EAMnB;EAAA,IANoB;IACrBN,QAAQ,GAAG,CAAC,CAAC;IACbC,WAAW,GAAGL,mBAAmB;IACjCM,aAAa,GAAGL,sBAAsB;IACtCU,GAAG;IACHC;EACF,CAAC,GAAAF,IAAA;EACC,MAAMG,YAAY,GAAGhB,OAAO,CAAC,OAAO;IAClCO,QAAQ,EAAE;MACR,GAAGA;IACL,CAAC;IACDC,WAAW;IACXC,aAAa;IACbK;EACF,CAAC,CAAC,EAAE,CAACP,QAAQ,EAAEC,WAAW,EAAEC,aAAa,EAAEK,GAAG,CAAC,CAAC;EAChD,OAAO,aAAaZ,IAAI,CAACS,QAAQ,EAAE;IACjCM,KAAK,EAAED,YAAY;IACnBD,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACA,OAAO,SAASG,kBAAkBA,CAACC,MAAM,EAAEC,aAAa,EAAE;EACxD,MAAM;IACJb;EACF,CAAC,GAAGR,UAAU,CAACM,YAAY,CAAC;EAC5B,OAAOc,MAAM,IAAIZ,QAAQ,CAACa,aAAa,CAAC,IAAIA,aAAa;AAC3D;AACA,OAAO,SAASC,uBAAuBA,CAAA,EAAG;EACxC,MAAM;IACJb;EACF,CAAC,GAAGT,UAAU,CAACM,YAAY,CAAC;EAC5B,OAAOG,WAAW;AACpB;AACA,OAAO,SAASc,yBAAyBA,CAAA,EAAG;EAC1C,MAAM;IACJb;EACF,CAAC,GAAGV,UAAU,CAACM,YAAY,CAAC;EAC5B,OAAOI,aAAa;AACtB;AACA,OAAO,SAASc,QAAQA,CAAA,EAAG;EACzB,MAAM;IACJT;EACF,CAAC,GAAGf,UAAU,CAACM,YAAY,CAAC;EAC5B,OAAOS,GAAG,KAAK,KAAK;AACtB;AACA,SAASU,wBAAwBA,CAACC,SAAS,EAAEC,IAAI,EAAE;EACjD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAEA,IAAI,GAAG;IACnCP,MAAM,EAAEO;EACV,CAAC;EACD,MAAMC,QAAQ,GAAGF,SAAS,CAACG,SAAS,IAAIH,SAAS,CAACG,SAAS,CAACC,gBAAgB;EAC5E;EACA,MAAM;IACJV,MAAM;IACNW,YAAY,GAAGH,QAAQ,GAAG,KAAK,GAAG;EACpC,CAAC,GAAGD,IAAI;EACR,MAAMK,OAAO,GAAG,aAAajC,KAAK,CAACkC,UAAU,CAAC,CAAAC,KAAA,EAE3CC,GAAG,KAAK;IAAA,IAFoC;MAC7C,GAAGC;IACL,CAAC,GAAAF,KAAA;IACCE,KAAK,CAACL,YAAY,CAAC,GAAGI,GAAG;IACzB,MAAME,QAAQ,GAAGlB,kBAAkB,CAACiB,KAAK,CAACC,QAAQ,EAAEjB,MAAM,CAAC;IAC3D,OAAO,aAAajB,IAAI,CAACuB,SAAS,EAAE;MAClC,GAAGU,KAAK;MACRC,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;EACFL,OAAO,CAACM,WAAW,GAAI,aAAYZ,SAAS,CAACY,WAAW,IAAIZ,SAAS,CAACa,IAAK,GAAE;EAC7E,OAAOP,OAAO;AAChB;AACA,SAASP,wBAAwB,EAAEd,QAAQ,IAAI6B,aAAa;AAC5D,eAAe3B,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}