{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\pages\\\\logout.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useState, useEffect } from \"react\";\nimport UserContext from \"../context/userContext\";\nimport { useNavigate } from \"react-router-dom\";\nimport Loader from \"../components/loader\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Logout(props) {\n  _s();\n  const {\n    userInfo,\n    logout\n  } = useContext(UserContext);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (userInfo && userInfo.username) logout();\n    setLoading(false);\n    navigate(\"/login\");\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 23\n  }, this);\n}\n_s(Logout, \"/cpH5OFWbY20ZLdMk/Wk3kE3lkE=\", false, function () {\n  return [useNavigate];\n});\n_c = Logout;\nexport default Logout;\nvar _c;\n$RefreshReg$(_c, \"Logout\");", "map": {"version": 3, "names": ["React", "useContext", "useState", "useEffect", "UserContext", "useNavigate", "Loader", "jsxDEV", "_jsxDEV", "Logout", "props", "_s", "userInfo", "logout", "loading", "setLoading", "navigate", "username", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/pages/logout.jsx"], "sourcesContent": ["import React, { useContext, useState, useEffect } from \"react\";\nimport UserContext from \"../context/userContext\";\nimport { useNavigate } from \"react-router-dom\";\nimport Loader from \"../components/loader\";\n\nfunction Logout(props) {\n  const { userInfo, logout } = useContext(UserContext);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (userInfo && userInfo.username) logout();\n    setLoading(false);\n    navigate(\"/login\");\n  }, []);\n\n  if (loading) return <Loader />;\n}\n\nexport default Logout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC9D,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,SAASC,MAAMA,CAACC,KAAK,EAAE;EAAAC,EAAA;EACrB,MAAM;IAAEC,QAAQ;IAAEC;EAAO,CAAC,GAAGZ,UAAU,CAACG,WAAW,CAAC;EACpD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMc,QAAQ,GAAGX,WAAW,EAAE;EAE9BF,SAAS,CAAC,MAAM;IACd,IAAIS,QAAQ,IAAIA,QAAQ,CAACK,QAAQ,EAAEJ,MAAM,EAAE;IAC3CE,UAAU,CAAC,KAAK,CAAC;IACjBC,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIF,OAAO,EAAE,oBAAON,OAAA,CAACF,MAAM;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAAG;AAChC;AAACV,EAAA,CAZQF,MAAM;EAAA,QAGIJ,WAAW;AAAA;AAAAiB,EAAA,GAHrBb,MAAM;AAcf,eAAeA,MAAM;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}