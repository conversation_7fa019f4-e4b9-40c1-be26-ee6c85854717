[{"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "2", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js": "3", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "4", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "5", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "6", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "7", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "8", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "9", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "10", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "11", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "12", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "13", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "14", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "15", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "16", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "17", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "18", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "19", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "20", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "21", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "22", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx": "23", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "24", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "25", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "26", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "27", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx": "28", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx": "29", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "30", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "31", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "32", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "33", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "34", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "35", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "36", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx": "37", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx": "38", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "39", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx": "40", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx": "41", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx": "42", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx": "43", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx": "44", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx": "45", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx": "46", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx": "47"}, {"size": 649, "mtime": 1750943544495, "results": "48", "hashOfConfig": "49"}, {"size": 362, "mtime": 1750943544502, "results": "50", "hashOfConfig": "49"}, {"size": 5092, "mtime": 1750995236291, "results": "51", "hashOfConfig": "49"}, {"size": 1585, "mtime": 1750943544494, "results": "52", "hashOfConfig": "49"}, {"size": 4116, "mtime": 1750943544494, "results": "53", "hashOfConfig": "49"}, {"size": 4438, "mtime": 1750993170933, "results": "54", "hashOfConfig": "49"}, {"size": 1795, "mtime": 1750943544489, "results": "55", "hashOfConfig": "49"}, {"size": 3086, "mtime": 1750993154771, "results": "56", "hashOfConfig": "49"}, {"size": 562, "mtime": 1750943544488, "results": "57", "hashOfConfig": "49"}, {"size": 1880, "mtime": 1750943544498, "results": "58", "hashOfConfig": "49"}, {"size": 4041, "mtime": 1750943544496, "results": "59", "hashOfConfig": "49"}, {"size": 4556, "mtime": 1750943544500, "results": "60", "hashOfConfig": "49"}, {"size": 3166, "mtime": 1750943544501, "results": "61", "hashOfConfig": "49"}, {"size": 3374, "mtime": 1750943544501, "results": "62", "hashOfConfig": "49"}, {"size": 2746, "mtime": 1750943544500, "results": "63", "hashOfConfig": "49"}, {"size": 557, "mtime": 1750943544498, "results": "64", "hashOfConfig": "49"}, {"size": 1873, "mtime": 1750945277042, "results": "65", "hashOfConfig": "49"}, {"size": 5003, "mtime": 1750943544499, "results": "66", "hashOfConfig": "49"}, {"size": 1876, "mtime": 1750943544499, "results": "67", "hashOfConfig": "49"}, {"size": 6146, "mtime": 1750943544499, "results": "68", "hashOfConfig": "49"}, {"size": 4506, "mtime": 1750943544501, "results": "69", "hashOfConfig": "49"}, {"size": 392, "mtime": 1750943544489, "results": "70", "hashOfConfig": "49"}, {"size": 988, "mtime": 1750943544487, "results": "71", "hashOfConfig": "49"}, {"size": 373, "mtime": 1750943544502, "results": "72", "hashOfConfig": "49"}, {"size": 1221, "mtime": 1750943544493, "results": "73", "hashOfConfig": "49"}, {"size": 228, "mtime": 1750943544490, "results": "74", "hashOfConfig": "49"}, {"size": 1101, "mtime": 1750943544491, "results": "75", "hashOfConfig": "49"}, {"size": 737, "mtime": 1750943544487, "results": "76", "hashOfConfig": "49"}, {"size": 901, "mtime": 1750943544491, "results": "77", "hashOfConfig": "49"}, {"size": 3665, "mtime": 1750943544493, "results": "78", "hashOfConfig": "49"}, {"size": 372, "mtime": 1750943544489, "results": "79", "hashOfConfig": "49"}, {"size": 2548, "mtime": 1750943544493, "results": "80", "hashOfConfig": "49"}, {"size": 2457, "mtime": 1750996243341, "results": "81", "hashOfConfig": "49"}, {"size": 1519, "mtime": 1750943544488, "results": "82", "hashOfConfig": "49"}, {"size": 639, "mtime": 1750943544492, "results": "83", "hashOfConfig": "49"}, {"size": 1826, "mtime": 1750943544491, "results": "84", "hashOfConfig": "49"}, {"size": 11285, "mtime": 1750993002543, "results": "85", "hashOfConfig": "49"}, {"size": 10483, "mtime": 1750993066396, "results": "86", "hashOfConfig": "49"}, {"size": 7428, "mtime": 1750994039308, "results": "87", "hashOfConfig": "49"}, {"size": 445, "mtime": 1750994572623, "results": "88", "hashOfConfig": "49"}, {"size": 3138, "mtime": 1750996219826, "results": "89", "hashOfConfig": "49"}, {"size": 3307, "mtime": 1750993684234, "results": "90", "hashOfConfig": "49"}, {"size": 6893, "mtime": 1750993259064, "results": "91", "hashOfConfig": "49"}, {"size": 551, "mtime": 1750993273555, "results": "92", "hashOfConfig": "49"}, {"size": 6727, "mtime": 1750994995900, "results": "93", "hashOfConfig": "49"}, {"size": 8036, "mtime": 1750995034966, "results": "94", "hashOfConfig": "49"}, {"size": 8840, "mtime": 1750996401097, "results": "95", "hashOfConfig": "49"}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xy8mk5", {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js", ["237"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["238"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["239", "240", "241"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["242", "243", "244", "245", "246", "247", "248", "249", "250"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["251", "252"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["253"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["254", "255"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", ["256", "257"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["258", "259"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", ["260"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", ["261", "262"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["263"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["264"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", ["265", "266", "267"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", ["268"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["269", "270", "271", "272"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["273", "274", "275", "276", "277", "278", "279", "280", "281"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["282", "283"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["284"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["285", "286"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["287"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["288", "289", "290"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx", ["291"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx", [], [], {"ruleId": "292", "severity": 1, "message": "293", "line": 41, "column": 3, "nodeType": "294", "endLine": 41, "endColumn": 12, "suggestions": "295"}, {"ruleId": "296", "severity": 1, "message": "297", "line": 35, "column": 49, "nodeType": "298", "messageId": "299", "endLine": 35, "endColumn": 51}, {"ruleId": "296", "severity": 1, "message": "297", "line": 62, "column": 18, "nodeType": "298", "messageId": "299", "endLine": 62, "endColumn": 20}, {"ruleId": "296", "severity": 1, "message": "297", "line": 68, "column": 15, "nodeType": "298", "messageId": "299", "endLine": 68, "endColumn": 17}, {"ruleId": "296", "severity": 1, "message": "297", "line": 125, "column": 45, "nodeType": "298", "messageId": "299", "endLine": 125, "endColumn": 47}, {"ruleId": "300", "severity": 1, "message": "301", "line": 58, "column": 15, "nodeType": "294", "messageId": "302", "endLine": 58, "endColumn": 19}, {"ruleId": "292", "severity": 1, "message": "303", "line": 104, "column": 6, "nodeType": "304", "endLine": 104, "endColumn": 8, "suggestions": "305"}, {"ruleId": "292", "severity": 1, "message": "306", "line": 116, "column": 6, "nodeType": "304", "endLine": 116, "endColumn": 18, "suggestions": "307"}, {"ruleId": "296", "severity": 1, "message": "308", "line": 121, "column": 20, "nodeType": "298", "messageId": "299", "endLine": 121, "endColumn": 22}, {"ruleId": "296", "severity": 1, "message": "308", "line": 121, "column": 53, "nodeType": "298", "messageId": "299", "endLine": 121, "endColumn": 55}, {"ruleId": "296", "severity": 1, "message": "308", "line": 125, "column": 17, "nodeType": "298", "messageId": "299", "endLine": 125, "endColumn": 19}, {"ruleId": "296", "severity": 1, "message": "308", "line": 125, "column": 44, "nodeType": "298", "messageId": "299", "endLine": 125, "endColumn": 46}, {"ruleId": "296", "severity": 1, "message": "308", "line": 129, "column": 20, "nodeType": "298", "messageId": "299", "endLine": 129, "endColumn": 22}, {"ruleId": "296", "severity": 1, "message": "297", "line": 133, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 133, "endColumn": 18}, {"ruleId": "300", "severity": 1, "message": "309", "line": 13, "column": 23, "nodeType": "294", "messageId": "302", "endLine": 13, "endColumn": 38}, {"ruleId": "292", "severity": 1, "message": "310", "line": 27, "column": 6, "nodeType": "304", "endLine": 27, "endColumn": 8, "suggestions": "311"}, {"ruleId": "312", "severity": 1, "message": "313", "line": 14, "column": 13, "nodeType": "314", "messageId": "315", "endLine": 14, "endColumn": 107, "fix": "316"}, {"ruleId": "292", "severity": 1, "message": "317", "line": 21, "column": 6, "nodeType": "304", "endLine": 21, "endColumn": 8, "suggestions": "318"}, {"ruleId": "296", "severity": 1, "message": "308", "line": 25, "column": 13, "nodeType": "298", "messageId": "299", "endLine": 25, "endColumn": 15}, {"ruleId": "300", "severity": 1, "message": "319", "line": 1, "column": 29, "nodeType": "294", "messageId": "302", "endLine": 1, "endColumn": 38}, {"ruleId": "296", "severity": 1, "message": "308", "line": 25, "column": 13, "nodeType": "298", "messageId": "299", "endLine": 25, "endColumn": 15}, {"ruleId": "292", "severity": 1, "message": "320", "line": 34, "column": 6, "nodeType": "304", "endLine": 34, "endColumn": 8, "suggestions": "321"}, {"ruleId": "296", "severity": 1, "message": "308", "line": 43, "column": 13, "nodeType": "298", "messageId": "299", "endLine": 43, "endColumn": 15}, {"ruleId": "292", "severity": 1, "message": "322", "line": 31, "column": 6, "nodeType": "304", "endLine": 31, "endColumn": 8, "suggestions": "323"}, {"ruleId": "324", "severity": 1, "message": "325", "line": 18, "column": 28, "nodeType": "294", "messageId": "326", "endLine": 18, "endColumn": 36}, {"ruleId": "292", "severity": 1, "message": "310", "line": 22, "column": 6, "nodeType": "304", "endLine": 22, "endColumn": 8, "suggestions": "327"}, {"ruleId": "292", "severity": 1, "message": "322", "line": 22, "column": 6, "nodeType": "304", "endLine": 22, "endColumn": 8, "suggestions": "328"}, {"ruleId": "292", "severity": 1, "message": "329", "line": 15, "column": 6, "nodeType": "304", "endLine": 15, "endColumn": 8, "suggestions": "330"}, {"ruleId": "300", "severity": 1, "message": "331", "line": 29, "column": 11, "nodeType": "294", "messageId": "302", "endLine": 29, "endColumn": 13}, {"ruleId": "296", "severity": 1, "message": "297", "line": 59, "column": 38, "nodeType": "298", "messageId": "299", "endLine": 59, "endColumn": 40}, {"ruleId": "296", "severity": 1, "message": "297", "line": 129, "column": 53, "nodeType": "298", "messageId": "299", "endLine": 129, "endColumn": 55}, {"ruleId": "296", "severity": 1, "message": "297", "line": 37, "column": 33, "nodeType": "298", "messageId": "299", "endLine": 37, "endColumn": 35}, {"ruleId": "296", "severity": 1, "message": "297", "line": 26, "column": 47, "nodeType": "298", "messageId": "299", "endLine": 26, "endColumn": 49}, {"ruleId": "296", "severity": 1, "message": "297", "line": 27, "column": 47, "nodeType": "298", "messageId": "299", "endLine": 27, "endColumn": 49}, {"ruleId": "292", "severity": 1, "message": "332", "line": 34, "column": 6, "nodeType": "304", "endLine": 34, "endColumn": 8, "suggestions": "333"}, {"ruleId": "296", "severity": 1, "message": "308", "line": 40, "column": 17, "nodeType": "298", "messageId": "299", "endLine": 40, "endColumn": 19}, {"ruleId": "300", "severity": 1, "message": "309", "line": 17, "column": 24, "nodeType": "294", "messageId": "302", "endLine": 17, "endColumn": 39}, {"ruleId": "292", "severity": 1, "message": "334", "line": 42, "column": 6, "nodeType": "304", "endLine": 42, "endColumn": 8, "suggestions": "335"}, {"ruleId": "296", "severity": 1, "message": "308", "line": 48, "column": 21, "nodeType": "298", "messageId": "299", "endLine": 48, "endColumn": 23}, {"ruleId": "296", "severity": 1, "message": "297", "line": 50, "column": 34, "nodeType": "298", "messageId": "299", "endLine": 50, "endColumn": 36}, {"ruleId": "296", "severity": 1, "message": "308", "line": 54, "column": 24, "nodeType": "298", "messageId": "299", "endLine": 54, "endColumn": 26}, {"ruleId": "296", "severity": 1, "message": "297", "line": 56, "column": 37, "nodeType": "298", "messageId": "299", "endLine": 56, "endColumn": 39}, {"ruleId": "296", "severity": 1, "message": "308", "line": 62, "column": 13, "nodeType": "298", "messageId": "299", "endLine": 62, "endColumn": 15}, {"ruleId": "296", "severity": 1, "message": "308", "line": 62, "column": 35, "nodeType": "298", "messageId": "299", "endLine": 62, "endColumn": 37}, {"ruleId": "296", "severity": 1, "message": "308", "line": 65, "column": 20, "nodeType": "298", "messageId": "299", "endLine": 65, "endColumn": 22}, {"ruleId": "296", "severity": 1, "message": "297", "line": 4, "column": 13, "nodeType": "298", "messageId": "299", "endLine": 4, "endColumn": 15}, {"ruleId": "336", "severity": 1, "message": "337", "line": 11, "column": 1, "nodeType": "338", "endLine": 18, "endColumn": 3}, {"ruleId": "300", "severity": 1, "message": "319", "line": 1, "column": 17, "nodeType": "294", "messageId": "302", "endLine": 1, "endColumn": 26}, {"ruleId": "296", "severity": 1, "message": "297", "line": 90, "column": 34, "nodeType": "298", "messageId": "299", "endLine": 90, "endColumn": 36}, {"ruleId": "296", "severity": 1, "message": "297", "line": 90, "column": 51, "nodeType": "298", "messageId": "299", "endLine": 90, "endColumn": 53}, {"ruleId": "296", "severity": 1, "message": "297", "line": 21, "column": 47, "nodeType": "298", "messageId": "299", "endLine": 21, "endColumn": 49}, {"ruleId": "300", "severity": 1, "message": "319", "line": 1, "column": 17, "nodeType": "294", "messageId": "302", "endLine": 1, "endColumn": 26}, {"ruleId": "300", "severity": 1, "message": "339", "line": 8, "column": 8, "nodeType": "294", "messageId": "302", "endLine": 8, "endColumn": 19}, {"ruleId": "300", "severity": 1, "message": "340", "line": 14, "column": 10, "nodeType": "294", "messageId": "302", "endLine": 14, "endColumn": 15}, {"ruleId": "300", "severity": 1, "message": "341", "line": 2, "column": 55, "nodeType": "294", "messageId": "302", "endLine": 2, "endColumn": 59}, "react-hooks/exhaustive-deps", "React Hook useEffect contains a call to 'setKeyword'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [keywordParam] as a second argument to the useEffect Hook.", "Identifier", ["342"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "unusedVar", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["343"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["344"], "Expected '!==' and instead saw '!='.", "'setSearchParams' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate', 'redirect', and 'userInfo'. Either include them or remove the dependency array.", ["345"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "346", "text": "347"}, "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["348"], "'useEffect' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'loadProduct'. Either include them or remove the dependency array.", ["349"], "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["350"], "no-const-assign", "'redirect' is constant.", "const", ["351"], ["352"], "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["353"], "'id' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'logout'. Either include them or remove the dependency array.", ["354"], "React Hook useEffect has missing dependencies: 'brandParam', 'categoryParam', and 'loadProducts'. Either include them or remove the dependency array.", ["355"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", "'Form' is defined but never used.", {"desc": "356", "fix": "357"}, {"desc": "358", "fix": "359"}, {"desc": "358", "fix": "360"}, {"desc": "361", "fix": "362"}, [447, 447], " rel=\"noreferrer\"", {"desc": "363", "fix": "364"}, {"desc": "365", "fix": "366"}, {"desc": "367", "fix": "368"}, {"desc": "361", "fix": "369"}, {"desc": "367", "fix": "370"}, {"desc": "371", "fix": "372"}, {"desc": "373", "fix": "374"}, {"desc": "375", "fix": "376"}, "Add dependencies array: [keywordParam]", {"range": "377", "text": "378"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "379", "text": "380"}, {"range": "381", "text": "380"}, "Update the dependencies array to be: [navigate, redirect, userInfo]", {"range": "382", "text": "383"}, "Update the dependencies array to be: [loadProducts]", {"range": "384", "text": "385"}, "Update the dependencies array to be: [id, loadProduct]", {"range": "386", "text": "387"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "388", "text": "389"}, {"range": "390", "text": "383"}, {"range": "391", "text": "389"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "392", "text": "393"}, "Update the dependencies array to be: [id, logout]", {"range": "394", "text": "395"}, "Update the dependencies array to be: [brandParam, categoryParam, loadProducts]", {"range": "396", "text": "397"}, [1834, 1834], ", [keywordParam]", [2796, 2798], "[authTokens, refresh]", [3172, 3184], [956, 958], "[navigate, redirect, userInfo]", [782, 784], "[loadProducts]", [1011, 1013], "[id, loadProduct]", [1168, 1170], "[navigate, userInfo]", [833, 835], [858, 860], [491, 493], "[logout, navigate, userInfo]", [1286, 1288], "[id, logout]", [1431, 1433], "[brandParam, categoryParam, loadProducts]"]