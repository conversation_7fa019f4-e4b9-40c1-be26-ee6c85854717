{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport BreadcrumbItem from './BreadcrumbItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  label: 'breadcrumb',\n  listProps: {}\n};\nconst Breadcrumb = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    listProps,\n    children,\n    label,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'nav',\n    ...props\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'breadcrumb');\n  return /*#__PURE__*/_jsx(Component, {\n    \"aria-label\": label,\n    className: className,\n    ref: ref,\n    ...props,\n    children: /*#__PURE__*/_jsx(\"ol\", {\n      ...listProps,\n      className: classNames(prefix, listProps == null ? void 0 : listProps.className),\n      children: children\n    })\n  });\n});\nBreadcrumb.displayName = 'Breadcrumb';\nBreadcrumb.defaultProps = defaultProps;\nexport default Object.assign(Breadcrumb, {\n  Item: BreadcrumbItem\n});", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "BreadcrumbItem", "jsx", "_jsx", "defaultProps", "label", "listProps", "Breadcrumb", "forwardRef", "_ref", "ref", "bsPrefix", "className", "children", "as", "Component", "props", "prefix", "displayName", "Object", "assign", "<PERSON><PERSON>"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Breadcrumb.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport BreadcrumbItem from './BreadcrumbItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  label: 'breadcrumb',\n  listProps: {}\n};\nconst Breadcrumb = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  listProps,\n  children,\n  label,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'nav',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'breadcrumb');\n  return /*#__PURE__*/_jsx(Component, {\n    \"aria-label\": label,\n    className: className,\n    ref: ref,\n    ...props,\n    children: /*#__PURE__*/_jsx(\"ol\", {\n      ...listProps,\n      className: classNames(prefix, listProps == null ? void 0 : listProps.className),\n      children: children\n    })\n  });\n});\nBreadcrumb.displayName = 'Breadcrumb';\nBreadcrumb.defaultProps = defaultProps;\nexport default Object.assign(Breadcrumb, {\n  Item: BreadcrumbItem\n});"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE,YAAY;EACnBC,SAAS,EAAE,CAAC;AACd,CAAC;AACD,MAAMC,UAAU,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAAC,IAAA,EAS9CC,GAAG,KAAK;EAAA,IATuC;IAChDC,QAAQ;IACRC,SAAS;IACTN,SAAS;IACTO,QAAQ;IACRR,KAAK;IACL;IACAS,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrB,GAAGC;EACL,CAAC,GAAAP,IAAA;EACC,MAAMQ,MAAM,GAAGjB,kBAAkB,CAACW,QAAQ,EAAE,YAAY,CAAC;EACzD,OAAO,aAAaR,IAAI,CAACY,SAAS,EAAE;IAClC,YAAY,EAAEV,KAAK;IACnBO,SAAS,EAAEA,SAAS;IACpBF,GAAG,EAAEA,GAAG;IACR,GAAGM,KAAK;IACRH,QAAQ,EAAE,aAAaV,IAAI,CAAC,IAAI,EAAE;MAChC,GAAGG,SAAS;MACZM,SAAS,EAAEd,UAAU,CAACmB,MAAM,EAAEX,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACM,SAAS,CAAC;MAC/EC,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,UAAU,CAACW,WAAW,GAAG,YAAY;AACrCX,UAAU,CAACH,YAAY,GAAGA,YAAY;AACtC,eAAee,MAAM,CAACC,MAAM,CAACb,UAAU,EAAE;EACvCc,IAAI,EAAEpB;AACR,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}