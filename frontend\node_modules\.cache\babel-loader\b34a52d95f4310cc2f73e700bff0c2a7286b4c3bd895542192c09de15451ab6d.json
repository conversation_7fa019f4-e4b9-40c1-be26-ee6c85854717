{"ast": null, "code": "import { useEffect, useLayoutEffect } from 'react';\nvar isReactNative = typeof global !== 'undefined' &&\n// @ts-ignore\nglobal.navigator &&\n// @ts-ignore\nglobal.navigator.product === 'ReactNative';\nvar isDOM = typeof document !== 'undefined';\n/**\n * Is `useLayoutEffect` in a DOM or React Native environment, otherwise resolves to useEffect\n * Only useful to avoid the console warning.\n *\n * PREFER `useEffect` UNLESS YOU KNOW WHAT YOU ARE DOING.\n *\n * @category effects\n */\n\nexport default isDOM || isReactNative ? useLayoutEffect : useEffect;", "map": {"version": 3, "names": ["useEffect", "useLayoutEffect", "isReactNative", "global", "navigator", "product", "isDOM", "document"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useIsomorphicEffect.js"], "sourcesContent": ["import { useEffect, useLayoutEffect } from 'react';\nvar isReactNative = typeof global !== 'undefined' && // @ts-ignore\nglobal.navigator && // @ts-ignore\nglobal.navigator.product === 'ReactNative';\nvar isDOM = typeof document !== 'undefined';\n/**\n * Is `useLayoutEffect` in a DOM or React Native environment, otherwise resolves to useEffect\n * Only useful to avoid the console warning.\n *\n * PREFER `useEffect` UNLESS YOU KNOW WHAT YOU ARE DOING.\n *\n * @category effects\n */\n\nexport default isDOM || isReactNative ? useLayoutEffect : useEffect;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,eAAe,QAAQ,OAAO;AAClD,IAAIC,aAAa,GAAG,OAAOC,MAAM,KAAK,WAAW;AAAI;AACrDA,MAAM,CAACC,SAAS;AAAI;AACpBD,MAAM,CAACC,SAAS,CAACC,OAAO,KAAK,aAAa;AAC1C,IAAIC,KAAK,GAAG,OAAOC,QAAQ,KAAK,WAAW;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAeD,KAAK,IAAIJ,aAAa,GAAGD,eAAe,GAAGD,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}