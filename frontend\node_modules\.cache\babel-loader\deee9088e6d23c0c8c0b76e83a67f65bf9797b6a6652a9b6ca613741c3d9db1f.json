{"ast": null, "code": "import classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { useCol } from './Col';\nexport default function usePlaceholder(_ref) {\n  let {\n    animation,\n    bg,\n    bsPrefix,\n    size,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'placeholder');\n  const [{\n    className,\n    ...colProps\n  }] = useCol(props);\n  return {\n    ...colProps,\n    className: classNames(className, animation ? `${bsPrefix}-${animation}` : bsPrefix, size && `${bsPrefix}-${size}`, bg && `bg-${bg}`)\n  };\n}", "map": {"version": 3, "names": ["classNames", "useBootstrapPrefix", "useCol", "usePlaceholder", "_ref", "animation", "bg", "bsPrefix", "size", "props", "className", "colProps"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/usePlaceholder.js"], "sourcesContent": ["import classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { useCol } from './Col';\nexport default function usePlaceholder({\n  animation,\n  bg,\n  bsPrefix,\n  size,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'placeholder');\n  const [{\n    className,\n    ...colProps\n  }] = useCol(props);\n  return {\n    ...colProps,\n    className: classNames(className, animation ? `${bsPrefix}-${animation}` : bsPrefix, size && `${bsPrefix}-${size}`, bg && `bg-${bg}`)\n  };\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,MAAM,QAAQ,OAAO;AAC9B,eAAe,SAASC,cAAcA,CAAAC,IAAA,EAMnC;EAAA,IANoC;IACrCC,SAAS;IACTC,EAAE;IACFC,QAAQ;IACRC,IAAI;IACJ,GAAGC;EACL,CAAC,GAAAL,IAAA;EACCG,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,aAAa,CAAC;EACtD,MAAM,CAAC;IACLG,SAAS;IACT,GAAGC;EACL,CAAC,CAAC,GAAGT,MAAM,CAACO,KAAK,CAAC;EAClB,OAAO;IACL,GAAGE,QAAQ;IACXD,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAEL,SAAS,GAAI,GAAEE,QAAS,IAAGF,SAAU,EAAC,GAAGE,QAAQ,EAAEC,IAAI,IAAK,GAAED,QAAS,IAAGC,IAAK,EAAC,EAAEF,EAAE,IAAK,MAAKA,EAAG,EAAC;EACrI,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}