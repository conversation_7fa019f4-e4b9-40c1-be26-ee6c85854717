{"ast": null, "code": "import { useEffect } from 'react';\nimport useEventCallback from './useEventCallback';\n\n/**\n * Attaches an event handler outside directly to specified DOM element\n * bypassing the react synthetic event system.\n *\n * @param element The target to listen for events on\n * @param event The DOM event name\n * @param handler An event handler\n * @param capture Whether or not to listen during the capture event phase\n */\nexport default function useEventListener(eventTarget, event, listener, capture) {\n  if (capture === void 0) {\n    capture = false;\n  }\n  var handler = useEventCallback(listener);\n  useEffect(function () {\n    var target = typeof eventTarget === 'function' ? eventTarget() : eventTarget;\n    target.addEventListener(event, handler, capture);\n    return function () {\n      return target.removeEventListener(event, handler, capture);\n    };\n  }, [eventTarget]);\n}", "map": {"version": 3, "names": ["useEffect", "useEventCallback", "useEventListener", "eventTarget", "event", "listener", "capture", "handler", "target", "addEventListener", "removeEventListener"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useEventListener.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport useEventCallback from './useEventCallback';\n\n/**\n * Attaches an event handler outside directly to specified DOM element\n * bypassing the react synthetic event system.\n *\n * @param element The target to listen for events on\n * @param event The DOM event name\n * @param handler An event handler\n * @param capture Whether or not to listen during the capture event phase\n */\nexport default function useEventListener(eventTarget, event, listener, capture) {\n  if (capture === void 0) {\n    capture = false;\n  }\n\n  var handler = useEventCallback(listener);\n  useEffect(function () {\n    var target = typeof eventTarget === 'function' ? eventTarget() : eventTarget;\n    target.addEventListener(event, handler, capture);\n    return function () {\n      return target.removeEventListener(event, handler, capture);\n    };\n  }, [eventTarget]);\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,gBAAgB,MAAM,oBAAoB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,WAAW,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC9E,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,KAAK;EACjB;EAEA,IAAIC,OAAO,GAAGN,gBAAgB,CAACI,QAAQ,CAAC;EACxCL,SAAS,CAAC,YAAY;IACpB,IAAIQ,MAAM,GAAG,OAAOL,WAAW,KAAK,UAAU,GAAGA,WAAW,EAAE,GAAGA,WAAW;IAC5EK,MAAM,CAACC,gBAAgB,CAACL,KAAK,EAAEG,OAAO,EAAED,OAAO,CAAC;IAChD,OAAO,YAAY;MACjB,OAAOE,MAAM,CAACE,mBAAmB,CAACN,KAAK,EAAEG,OAAO,EAAED,OAAO,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,CAACH,WAAW,CAAC,CAAC;AACnB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}