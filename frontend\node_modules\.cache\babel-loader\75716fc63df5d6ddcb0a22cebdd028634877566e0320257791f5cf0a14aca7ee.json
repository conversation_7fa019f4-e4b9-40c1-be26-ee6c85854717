{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\pages\\\\paymentPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext } from \"react\";\nimport { Form, Button, Col } from \"react-bootstrap\";\nimport FormContainer from \"../components/formContainer\";\nimport CheckoutSteps from \"../components/checkoutSteps\";\nimport { useNavigate } from \"react-router-dom\";\nimport CartContext from \"../context/cartContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PaymentPage(props) {\n  _s();\n  const {\n    shippingAddress,\n    paymentMethod: method,\n    updatePaymentMethod\n  } = useContext(CartContext);\n  const [paymentMethod, setPaymentMethod] = useState(method);\n  const navigate = useNavigate();\n  if (!shippingAddress || !shippingAddress.address) navigate(\"/shipping\");\n  const handleSubmit = e => {\n    e.preventDefault();\n    updatePaymentMethod(paymentMethod);\n    navigate(\"/placeorder\");\n  };\n  return /*#__PURE__*/_jsxDEV(FormContainer, {\n    children: [/*#__PURE__*/_jsxDEV(CheckoutSteps, {\n      step1: true,\n      step2: true,\n      step3: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n          as: \"legend\",\n          children: \"Select Method\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Form.Check, {\n            type: \"radio\",\n            label: \"Stripe or Debit Card\",\n            id: \"stripe\",\n            name: \"paymentMethod\",\n            value: \"Stripe\",\n            onChange: e => {\n              setPaymentMethod(e.currentTarget.value);\n            },\n            checked: \"Stripe\" == paymentMethod\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"submit\",\n        variant: \"primary\",\n        children: \"Continue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n}\n_s(PaymentPage, \"***************************=\", false, function () {\n  return [useNavigate];\n});\n_c = PaymentPage;\nexport default PaymentPage;\nvar _c;\n$RefreshReg$(_c, \"PaymentPage\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "Form", "<PERSON><PERSON>", "Col", "FormContainer", "CheckoutSteps", "useNavigate", "CartContext", "jsxDEV", "_jsxDEV", "PaymentPage", "props", "_s", "shippingAddress", "paymentMethod", "method", "updatePaymentMethod", "setPaymentMethod", "navigate", "address", "handleSubmit", "e", "preventDefault", "children", "step1", "step2", "step3", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "Group", "Label", "as", "Check", "type", "label", "id", "name", "value", "onChange", "currentTarget", "checked", "variant", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/pages/paymentPage.jsx"], "sourcesContent": ["import React, { useState, useContext } from \"react\";\nimport { <PERSON>, Button, Col } from \"react-bootstrap\";\nimport FormContainer from \"../components/formContainer\";\nimport CheckoutSteps from \"../components/checkoutSteps\";\nimport { useNavigate } from \"react-router-dom\";\nimport CartContext from \"../context/cartContext\";\n\nfunction PaymentPage(props) {\n  const { shippingAddress, paymentMethod:method, updatePaymentMethod } = useContext(CartContext);\n  const [paymentMethod, setPaymentMethod] = useState(method);\n  const navigate = useNavigate();\n\n  if (!shippingAddress || !shippingAddress.address) navigate(\"/shipping\");\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    updatePaymentMethod(paymentMethod)\n    navigate(\"/placeorder\");\n  };\n\n  return (\n    <FormContainer>\n      <CheckoutSteps step1 step2 step3 />\n      <Form onSubmit={handleSubmit}>\n        <Form.Group>\n          <Form.Label as=\"legend\">Select Method</Form.Label>\n          <Col>\n            <Form.Check\n              type=\"radio\"\n              label=\"Stripe or Debit Card\"\n              id=\"stripe\"\n              name=\"paymentMethod\"\n              value=\"Stripe\"\n              onChange={(e) => {\n                setPaymentMethod(e.currentTarget.value);\n              }}\n              checked={\"Stripe\" == paymentMethod}\n            ></Form.Check>\n            {/* <Form.Check\n              type=\"radio\"\n              label=\"Cash on Delivery\"\n              id=\"cod\"\n              value=\"Cash on Delivery\"\n              onChange={(e) => {\n                setPaymentMethod(e.currentTarget.value);\n              }}\n              checked={\"Cash on Delivery\" == paymentMethod}\n            ></Form.Check> */}\n          </Col>\n        </Form.Group>\n        <Button type=\"submit\" variant=\"primary\">\n          Continue\n        </Button>\n      </Form>\n    </FormContainer>\n  );\n}\n\nexport default PaymentPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,SAASC,IAAI,EAAEC,MAAM,EAAEC,GAAG,QAAQ,iBAAiB;AACnD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,SAASC,WAAWA,CAACC,KAAK,EAAE;EAAAC,EAAA;EAC1B,MAAM;IAAEC,eAAe;IAAEC,aAAa,EAACC,MAAM;IAAEC;EAAoB,CAAC,GAAGhB,UAAU,CAACO,WAAW,CAAC;EAC9F,MAAM,CAACO,aAAa,EAAEG,gBAAgB,CAAC,GAAGlB,QAAQ,CAACgB,MAAM,CAAC;EAC1D,MAAMG,QAAQ,GAAGZ,WAAW,EAAE;EAE9B,IAAI,CAACO,eAAe,IAAI,CAACA,eAAe,CAACM,OAAO,EAAED,QAAQ,CAAC,WAAW,CAAC;EAEvE,MAAME,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,EAAE;IAClBN,mBAAmB,CAACF,aAAa,CAAC;IAClCI,QAAQ,CAAC,aAAa,CAAC;EACzB,CAAC;EAED,oBACET,OAAA,CAACL,aAAa;IAAAmB,QAAA,gBACZd,OAAA,CAACJ,aAAa;MAACmB,KAAK;MAACC,KAAK;MAACC,KAAK;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG,eACnCrB,OAAA,CAACR,IAAI;MAAC8B,QAAQ,EAAEX,YAAa;MAAAG,QAAA,gBAC3Bd,OAAA,CAACR,IAAI,CAAC+B,KAAK;QAAAT,QAAA,gBACTd,OAAA,CAACR,IAAI,CAACgC,KAAK;UAACC,EAAE,EAAC,QAAQ;UAAAX,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eAClDrB,OAAA,CAACN,GAAG;UAAAoB,QAAA,eACFd,OAAA,CAACR,IAAI,CAACkC,KAAK;YACTC,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,sBAAsB;YAC5BC,EAAE,EAAC,QAAQ;YACXC,IAAI,EAAC,eAAe;YACpBC,KAAK,EAAC,QAAQ;YACdC,QAAQ,EAAGpB,CAAC,IAAK;cACfJ,gBAAgB,CAACI,CAAC,CAACqB,aAAa,CAACF,KAAK,CAAC;YACzC,CAAE;YACFG,OAAO,EAAE,QAAQ,IAAI7B;UAAc;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACvB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAWV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACK,eACbrB,OAAA,CAACP,MAAM;QAACkC,IAAI,EAAC,QAAQ;QAACQ,OAAO,EAAC,SAAS;QAAArB,QAAA,EAAC;MAExC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAS;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACO;AAEpB;AAAClB,EAAA,CAjDQF,WAAW;EAAA,QAGDJ,WAAW;AAAA;AAAAuC,EAAA,GAHrBnC,WAAW;AAmDpB,eAAeA,WAAW;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}