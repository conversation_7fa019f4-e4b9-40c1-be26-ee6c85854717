{"ast": null, "code": "import PropTypes from 'prop-types';\nimport * as React from 'react';\nimport Tab<PERSON>ontainer from './TabContainer';\nimport TabContent from './TabContent';\nimport TabPane from './TabPane';\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/* eslint-disable react/no-unused-prop-types */\nconst propTypes = {\n  eventKey: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  /**\n   * Content for the tab title.\n   */\n  title: PropTypes.node.isRequired,\n  /**\n   * The disabled state of the tab.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Class to pass to the underlying nav link.\n   */\n  tabClassName: PropTypes.string,\n  /**\n   * Object containing attributes to pass to underlying nav link.\n   */\n  tabAttrs: PropTypes.object\n};\nconst Tab = () => {\n  throw new Error('ReactBootstrap: The `Tab` component is not meant to be rendered! ' + \"It's an abstract component that is only valid as a direct Child of the `Tabs` Component. \" + 'For custom tabs components use TabPane and TabsContainer directly');\n  // Needed otherwise docs error out.\n  return /*#__PURE__*/_jsx(_Fragment, {});\n};\nTab.propTypes = propTypes;\nexport default Object.assign(Tab, {\n  Container: TabContainer,\n  Content: TabContent,\n  Pane: TabPane\n});", "map": {"version": 3, "names": ["PropTypes", "React", "TabContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabPane", "Fragment", "_Fragment", "jsx", "_jsx", "propTypes", "eventKey", "oneOfType", "string", "number", "title", "node", "isRequired", "disabled", "bool", "tabClassName", "tabAttrs", "object", "Tab", "Error", "Object", "assign", "Container", "Content", "Pane"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Tab.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport * as React from 'react';\nimport Tab<PERSON>ontainer from './TabContainer';\nimport TabContent from './TabContent';\nimport TabPane from './TabPane';\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/* eslint-disable react/no-unused-prop-types */\nconst propTypes = {\n  eventKey: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  /**\n   * Content for the tab title.\n   */\n  title: PropTypes.node.isRequired,\n  /**\n   * The disabled state of the tab.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Class to pass to the underlying nav link.\n   */\n  tabClassName: PropTypes.string,\n  /**\n   * Object containing attributes to pass to underlying nav link.\n   */\n  tabAttrs: PropTypes.object\n};\nconst Tab = () => {\n  throw new Error('ReactBootstrap: The `Tab` component is not meant to be rendered! ' + \"It's an abstract component that is only valid as a direct Child of the `Tabs` Component. \" + 'For custom tabs components use TabPane and TabsContainer directly');\n  // Needed otherwise docs error out.\n  return /*#__PURE__*/_jsx(_Fragment, {});\n};\nTab.propTypes = propTypes;\nexport default Object.assign(Tab, {\n  Container: TabContainer,\n  Content: TabContent,\n  Pane: TabPane\n});"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,QAAQ,IAAIC,SAAS,QAAQ,mBAAmB;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA,MAAMC,SAAS,GAAG;EAChBC,QAAQ,EAAEV,SAAS,CAACW,SAAS,CAAC,CAACX,SAAS,CAACY,MAAM,EAAEZ,SAAS,CAACa,MAAM,CAAC,CAAC;EACnE;AACF;AACA;EACEC,KAAK,EAAEd,SAAS,CAACe,IAAI,CAACC,UAAU;EAChC;AACF;AACA;EACEC,QAAQ,EAAEjB,SAAS,CAACkB,IAAI;EACxB;AACF;AACA;EACEC,YAAY,EAAEnB,SAAS,CAACY,MAAM;EAC9B;AACF;AACA;EACEQ,QAAQ,EAAEpB,SAAS,CAACqB;AACtB,CAAC;AACD,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAChB,MAAM,IAAIC,KAAK,CAAC,mEAAmE,GAAG,2FAA2F,GAAG,mEAAmE,CAAC;EACxP;EACA,OAAO,aAAaf,IAAI,CAACF,SAAS,EAAE,CAAC,CAAC,CAAC;AACzC,CAAC;AACDgB,GAAG,CAACb,SAAS,GAAGA,SAAS;AACzB,eAAee,MAAM,CAACC,MAAM,CAACH,GAAG,EAAE;EAChCI,SAAS,EAAExB,YAAY;EACvByB,OAAO,EAAExB,UAAU;EACnByB,IAAI,EAAExB;AACR,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}