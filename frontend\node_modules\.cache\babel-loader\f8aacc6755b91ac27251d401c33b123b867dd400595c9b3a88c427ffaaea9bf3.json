{"ast": null, "code": "import { useEffect } from 'react';\nimport useCommittedRef from './useCommittedRef';\n/**\n * Creates a `setInterval` that is properly cleaned up when a component unmounted\n *\n * ```tsx\n *  function Timer() {\n *    const [timer, setTimer] = useState(0)\n *    useInterval(() => setTimer(i => i + 1), 1000)\n *\n *    return <span>{timer} seconds past</span>\n *  }\n * ```\n *\n * @param fn an function run on each interval\n * @param ms The milliseconds duration of the interval\n */\n\nfunction useInterval(fn, ms, paused, runImmediately) {\n  if (paused === void 0) {\n    paused = false;\n  }\n  if (runImmediately === void 0) {\n    runImmediately = false;\n  }\n  var handle;\n  var fnRef = useCommittedRef(fn); // this ref is necessary b/c useEffect will sometimes miss a paused toggle\n  // orphaning a setTimeout chain in the aether, so relying on it's refresh logic is not reliable.\n\n  var pausedRef = useCommittedRef(paused);\n  var tick = function tick() {\n    if (pausedRef.current) return;\n    fnRef.current();\n    schedule(); // eslint-disable-line no-use-before-define\n  };\n\n  var schedule = function schedule() {\n    clearTimeout(handle);\n    handle = setTimeout(tick, ms);\n  };\n  useEffect(function () {\n    if (runImmediately) {\n      tick();\n    } else {\n      schedule();\n    }\n    return function () {\n      return clearTimeout(handle);\n    };\n  }, [paused, runImmediately]);\n}\nexport default useInterval;", "map": {"version": 3, "names": ["useEffect", "useCommittedRef", "useInterval", "fn", "ms", "paused", "runImmediately", "handle", "fnRef", "pausedRef", "tick", "current", "schedule", "clearTimeout", "setTimeout"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useInterval.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport useCommittedRef from './useCommittedRef';\n/**\n * Creates a `setInterval` that is properly cleaned up when a component unmounted\n *\n * ```tsx\n *  function Timer() {\n *    const [timer, setTimer] = useState(0)\n *    useInterval(() => setTimer(i => i + 1), 1000)\n *\n *    return <span>{timer} seconds past</span>\n *  }\n * ```\n *\n * @param fn an function run on each interval\n * @param ms The milliseconds duration of the interval\n */\n\nfunction useInterval(fn, ms, paused, runImmediately) {\n  if (paused === void 0) {\n    paused = false;\n  }\n\n  if (runImmediately === void 0) {\n    runImmediately = false;\n  }\n\n  var handle;\n  var fnRef = useCommittedRef(fn); // this ref is necessary b/c useEffect will sometimes miss a paused toggle\n  // orphaning a setTimeout chain in the aether, so relying on it's refresh logic is not reliable.\n\n  var pausedRef = useCommittedRef(paused);\n\n  var tick = function tick() {\n    if (pausedRef.current) return;\n    fnRef.current();\n    schedule(); // eslint-disable-line no-use-before-define\n  };\n\n  var schedule = function schedule() {\n    clearTimeout(handle);\n    handle = setTimeout(tick, ms);\n  };\n\n  useEffect(function () {\n    if (runImmediately) {\n      tick();\n    } else {\n      schedule();\n    }\n\n    return function () {\n      return clearTimeout(handle);\n    };\n  }, [paused, runImmediately]);\n}\n\nexport default useInterval;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,WAAWA,CAACC,EAAE,EAAEC,EAAE,EAAEC,MAAM,EAAEC,cAAc,EAAE;EACnD,IAAID,MAAM,KAAK,KAAK,CAAC,EAAE;IACrBA,MAAM,GAAG,KAAK;EAChB;EAEA,IAAIC,cAAc,KAAK,KAAK,CAAC,EAAE;IAC7BA,cAAc,GAAG,KAAK;EACxB;EAEA,IAAIC,MAAM;EACV,IAAIC,KAAK,GAAGP,eAAe,CAACE,EAAE,CAAC,CAAC,CAAC;EACjC;;EAEA,IAAIM,SAAS,GAAGR,eAAe,CAACI,MAAM,CAAC;EAEvC,IAAIK,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG;IACzB,IAAID,SAAS,CAACE,OAAO,EAAE;IACvBH,KAAK,CAACG,OAAO,EAAE;IACfC,QAAQ,EAAE,CAAC,CAAC;EACd,CAAC;;EAED,IAAIA,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjCC,YAAY,CAACN,MAAM,CAAC;IACpBA,MAAM,GAAGO,UAAU,CAACJ,IAAI,EAAEN,EAAE,CAAC;EAC/B,CAAC;EAEDJ,SAAS,CAAC,YAAY;IACpB,IAAIM,cAAc,EAAE;MAClBI,IAAI,EAAE;IACR,CAAC,MAAM;MACLE,QAAQ,EAAE;IACZ;IAEA,OAAO,YAAY;MACjB,OAAOC,YAAY,CAACN,MAAM,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,CAACF,MAAM,EAAEC,cAAc,CAAC,CAAC;AAC9B;AAEA,eAAeJ,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}