{"ast": null, "code": "import { useState, useEffect } from 'react';\n\n/**\n * Fetch and load an image for programatic use such as in a `<canvas>` element.\n *\n * @param imageOrUrl The `HtmlImageElement` or image url to load\n * @param crossOrigin The `crossorigin` attribute to set\n *\n * ```ts\n * const { image, error } = useImage('/static/kittens.png')\n * const ref = useRef<HTMLCanvasElement>()\n *\n * useEffect(() => {\n *   const ctx = ref.current.getContext('2d')\n *\n *   if (image) {\n *     ctx.drawImage(image, 0, 0)\n *   }\n * }, [ref, image])\n *\n * return (\n *   <>\n *     {error && \"there was a problem loading the image\"}\n *     <canvas ref={ref} />\n *   </>\n * ```\n */\nexport default function useImage(imageOrUrl, crossOrigin) {\n  var _useState = useState({\n      image: null,\n      error: null\n    }),\n    state = _useState[0],\n    setState = _useState[1];\n  useEffect(function () {\n    if (!imageOrUrl) return undefined;\n    var image;\n    if (typeof imageOrUrl === 'string') {\n      image = new Image();\n      if (crossOrigin) image.crossOrigin = crossOrigin;\n      image.src = imageOrUrl;\n    } else {\n      image = imageOrUrl;\n      if (image.complete && image.naturalHeight > 0) {\n        setState({\n          image: image,\n          error: null\n        });\n        return;\n      }\n    }\n    function onLoad() {\n      setState({\n        image: image,\n        error: null\n      });\n    }\n    function onError(error) {\n      setState({\n        image: image,\n        error: error\n      });\n    }\n    image.addEventListener('load', onLoad);\n    image.addEventListener('error', onError);\n    return function () {\n      image.removeEventListener('load', onLoad);\n      image.removeEventListener('error', onError);\n    };\n  }, [imageOrUrl, crossOrigin]);\n  return state;\n}", "map": {"version": 3, "names": ["useState", "useEffect", "useImage", "imageOrUrl", "crossOrigin", "_useState", "image", "error", "state", "setState", "undefined", "Image", "src", "complete", "naturalHeight", "onLoad", "onError", "addEventListener", "removeEventListener"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useImage.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\n/**\n * Fetch and load an image for programatic use such as in a `<canvas>` element.\n *\n * @param imageOrUrl The `HtmlImageElement` or image url to load\n * @param crossOrigin The `crossorigin` attribute to set\n *\n * ```ts\n * const { image, error } = useImage('/static/kittens.png')\n * const ref = useRef<HTMLCanvasElement>()\n *\n * useEffect(() => {\n *   const ctx = ref.current.getContext('2d')\n *\n *   if (image) {\n *     ctx.drawImage(image, 0, 0)\n *   }\n * }, [ref, image])\n *\n * return (\n *   <>\n *     {error && \"there was a problem loading the image\"}\n *     <canvas ref={ref} />\n *   </>\n * ```\n */\nexport default function useImage(imageOrUrl, crossOrigin) {\n  var _useState = useState({\n    image: null,\n    error: null\n  }),\n      state = _useState[0],\n      setState = _useState[1];\n\n  useEffect(function () {\n    if (!imageOrUrl) return undefined;\n    var image;\n\n    if (typeof imageOrUrl === 'string') {\n      image = new Image();\n      if (crossOrigin) image.crossOrigin = crossOrigin;\n      image.src = imageOrUrl;\n    } else {\n      image = imageOrUrl;\n\n      if (image.complete && image.naturalHeight > 0) {\n        setState({\n          image: image,\n          error: null\n        });\n        return;\n      }\n    }\n\n    function onLoad() {\n      setState({\n        image: image,\n        error: null\n      });\n    }\n\n    function onError(error) {\n      setState({\n        image: image,\n        error: error\n      });\n    }\n\n    image.addEventListener('load', onLoad);\n    image.addEventListener('error', onError);\n    return function () {\n      image.removeEventListener('load', onLoad);\n      image.removeEventListener('error', onError);\n    };\n  }, [imageOrUrl, crossOrigin]);\n  return state;\n}"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,QAAQA,CAACC,UAAU,EAAEC,WAAW,EAAE;EACxD,IAAIC,SAAS,GAAGL,QAAQ,CAAC;MACvBM,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;IACT,CAAC,CAAC;IACEC,KAAK,GAAGH,SAAS,CAAC,CAAC,CAAC;IACpBI,QAAQ,GAAGJ,SAAS,CAAC,CAAC,CAAC;EAE3BJ,SAAS,CAAC,YAAY;IACpB,IAAI,CAACE,UAAU,EAAE,OAAOO,SAAS;IACjC,IAAIJ,KAAK;IAET,IAAI,OAAOH,UAAU,KAAK,QAAQ,EAAE;MAClCG,KAAK,GAAG,IAAIK,KAAK,EAAE;MACnB,IAAIP,WAAW,EAAEE,KAAK,CAACF,WAAW,GAAGA,WAAW;MAChDE,KAAK,CAACM,GAAG,GAAGT,UAAU;IACxB,CAAC,MAAM;MACLG,KAAK,GAAGH,UAAU;MAElB,IAAIG,KAAK,CAACO,QAAQ,IAAIP,KAAK,CAACQ,aAAa,GAAG,CAAC,EAAE;QAC7CL,QAAQ,CAAC;UACPH,KAAK,EAAEA,KAAK;UACZC,KAAK,EAAE;QACT,CAAC,CAAC;QACF;MACF;IACF;IAEA,SAASQ,MAAMA,CAAA,EAAG;MAChBN,QAAQ,CAAC;QACPH,KAAK,EAAEA,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;IAEA,SAASS,OAAOA,CAACT,KAAK,EAAE;MACtBE,QAAQ,CAAC;QACPH,KAAK,EAAEA,KAAK;QACZC,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;IAEAD,KAAK,CAACW,gBAAgB,CAAC,MAAM,EAAEF,MAAM,CAAC;IACtCT,KAAK,CAACW,gBAAgB,CAAC,OAAO,EAAED,OAAO,CAAC;IACxC,OAAO,YAAY;MACjBV,KAAK,CAACY,mBAAmB,CAAC,MAAM,EAAEH,MAAM,CAAC;MACzCT,KAAK,CAACY,mBAAmB,CAAC,OAAO,EAAEF,OAAO,CAAC;IAC7C,CAAC;EACH,CAAC,EAAE,CAACb,UAAU,EAAEC,WAAW,CAAC,CAAC;EAC7B,OAAOI,KAAK;AACd"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}