{"ast": null, "code": "import { useReducer } from 'react';\n/**\n * Returns a function that triggers a component update. the hook equivalent to\n * `this.forceUpdate()` in a class component. In most cases using a state value directly\n * is preferable but may be required in some advanced usages of refs for interop or\n * when direct DOM manipulation is required.\n *\n * ```ts\n * const forceUpdate = useForceUpdate();\n *\n * const updateOnClick = useCallback(() => {\n *  forceUpdate()\n * }, [forceUpdate])\n *\n * return <button type=\"button\" onClick={updateOnClick}>Hi there</button>\n * ```\n */\n\nexport default function useForceUpdate() {\n  // The toggling state value is designed to defeat React optimizations for skipping\n  // updates when they are stricting equal to the last state value\n  var _useReducer = useReducer(function (state) {\n      return !state;\n    }, false),\n    dispatch = _useReducer[1];\n  return dispatch;\n}", "map": {"version": 3, "names": ["useReducer", "useForceUpdate", "_useReducer", "state", "dispatch"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useForceUpdate.js"], "sourcesContent": ["import { useReducer } from 'react';\n/**\n * Returns a function that triggers a component update. the hook equivalent to\n * `this.forceUpdate()` in a class component. In most cases using a state value directly\n * is preferable but may be required in some advanced usages of refs for interop or\n * when direct DOM manipulation is required.\n *\n * ```ts\n * const forceUpdate = useForceUpdate();\n *\n * const updateOnClick = useCallback(() => {\n *  forceUpdate()\n * }, [forceUpdate])\n *\n * return <button type=\"button\" onClick={updateOnClick}>Hi there</button>\n * ```\n */\n\nexport default function useForceUpdate() {\n  // The toggling state value is designed to defeat React optimizations for skipping\n  // updates when they are stricting equal to the last state value\n  var _useReducer = useReducer(function (state) {\n    return !state;\n  }, false),\n      dispatch = _useReducer[1];\n\n  return dispatch;\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,cAAcA,CAAA,EAAG;EACvC;EACA;EACA,IAAIC,WAAW,GAAGF,UAAU,CAAC,UAAUG,KAAK,EAAE;MAC5C,OAAO,CAACA,KAAK;IACf,CAAC,EAAE,KAAK,CAAC;IACLC,QAAQ,GAAGF,WAAW,CAAC,CAAC,CAAC;EAE7B,OAAOE,QAAQ;AACjB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}