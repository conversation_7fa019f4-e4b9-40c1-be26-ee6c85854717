{"ast": null, "code": "import useMediaQuery from './useMediaQuery';\nimport { useMemo } from 'react';\n\n/**\n * Create a responsive hook we a set of breakpoint names and widths.\n * You can use any valid css units as well as a numbers (for pixels).\n *\n * **NOTE:** The object key order is important! it's assumed to be in order from smallest to largest\n *\n * ```ts\n * const useBreakpoint = createBreakpointHook({\n *  xs: 0,\n *  sm: 576,\n *  md: 768,\n *  lg: 992,\n *  xl: 1200,\n * })\n * ```\n *\n * **Watch out!** using string values will sometimes construct media queries using css `calc()` which\n * is NOT supported in media queries by all browsers at the moment. use numbers for\n * the widest range of browser support.\n *\n * @param breakpointValues A object hash of names to breakpoint dimensions\n */\nexport function createBreakpointHook(breakpointValues) {\n  var names = Object.keys(breakpointValues);\n  function and(query, next) {\n    if (query === next) {\n      return next;\n    }\n    return query ? query + \" and \" + next : next;\n  }\n  function getNext(breakpoint) {\n    return names[Math.min(names.indexOf(breakpoint) + 1, names.length - 1)];\n  }\n  function getMaxQuery(breakpoint) {\n    var next = getNext(breakpoint);\n    var value = breakpointValues[next];\n    if (typeof value === 'number') value = value - 0.2 + \"px\";else value = \"calc(\" + value + \" - 0.2px)\";\n    return \"(max-width: \" + value + \")\";\n  }\n  function getMinQuery(breakpoint) {\n    var value = breakpointValues[breakpoint];\n    if (typeof value === 'number') {\n      value = value + \"px\";\n    }\n    return \"(min-width: \" + value + \")\";\n  }\n  /**\n   * Match a set of breakpoints\n   *\n   * ```tsx\n   * const MidSizeOnly = () => {\n   *   const isMid = useBreakpoint({ lg: 'down', sm: 'up' });\n   *\n   *   if (isMid) return <div>On a Reasonable sized Screen!</div>\n   *   return null;\n   * }\n   * ```\n   * @param breakpointMap An object map of breakpoints and directions, queries are constructed using \"and\" to join\n   * breakpoints together\n   * @param window Optionally specify the target window to match against (useful when rendering into iframes)\n   */\n\n  function useBreakpoint(breakpointOrMap, direction, window) {\n    var breakpointMap;\n    if (typeof breakpointOrMap === 'object') {\n      breakpointMap = breakpointOrMap;\n      window = direction;\n      direction = true;\n    } else {\n      var _breakpointMap;\n      direction = direction || true;\n      breakpointMap = (_breakpointMap = {}, _breakpointMap[breakpointOrMap] = direction, _breakpointMap);\n    }\n    var query = useMemo(function () {\n      return Object.entries(breakpointMap).reduce(function (query, _ref) {\n        var key = _ref[0],\n          direction = _ref[1];\n        if (direction === 'up' || direction === true) {\n          query = and(query, getMinQuery(key));\n        }\n        if (direction === 'down' || direction === true) {\n          query = and(query, getMaxQuery(key));\n        }\n        return query;\n      }, '');\n    }, [JSON.stringify(breakpointMap)]);\n    return useMediaQuery(query, window);\n  }\n  return useBreakpoint;\n}\nvar useBreakpoint = createBreakpointHook({\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1400\n});\nexport default useBreakpoint;", "map": {"version": 3, "names": ["useMediaQuery", "useMemo", "createBreakpointHook", "breakpoint<PERSON><PERSON><PERSON>", "names", "Object", "keys", "and", "query", "next", "getNext", "breakpoint", "Math", "min", "indexOf", "length", "getMaxQuery", "value", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "useBreakpoint", "breakpointOrMap", "direction", "window", "breakpointMap", "_breakpointMap", "entries", "reduce", "_ref", "key", "JSON", "stringify", "xs", "sm", "md", "lg", "xl", "xxl"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useBreakpoint.js"], "sourcesContent": ["import useMediaQuery from './useMediaQuery';\nimport { useMemo } from 'react';\n\n/**\n * Create a responsive hook we a set of breakpoint names and widths.\n * You can use any valid css units as well as a numbers (for pixels).\n *\n * **NOTE:** The object key order is important! it's assumed to be in order from smallest to largest\n *\n * ```ts\n * const useBreakpoint = createBreakpointHook({\n *  xs: 0,\n *  sm: 576,\n *  md: 768,\n *  lg: 992,\n *  xl: 1200,\n * })\n * ```\n *\n * **Watch out!** using string values will sometimes construct media queries using css `calc()` which\n * is NOT supported in media queries by all browsers at the moment. use numbers for\n * the widest range of browser support.\n *\n * @param breakpointValues A object hash of names to breakpoint dimensions\n */\nexport function createBreakpointHook(breakpointValues) {\n  var names = Object.keys(breakpointValues);\n\n  function and(query, next) {\n    if (query === next) {\n      return next;\n    }\n\n    return query ? query + \" and \" + next : next;\n  }\n\n  function getNext(breakpoint) {\n    return names[Math.min(names.indexOf(breakpoint) + 1, names.length - 1)];\n  }\n\n  function getMaxQuery(breakpoint) {\n    var next = getNext(breakpoint);\n    var value = breakpointValues[next];\n    if (typeof value === 'number') value = value - 0.2 + \"px\";else value = \"calc(\" + value + \" - 0.2px)\";\n    return \"(max-width: \" + value + \")\";\n  }\n\n  function getMinQuery(breakpoint) {\n    var value = breakpointValues[breakpoint];\n\n    if (typeof value === 'number') {\n      value = value + \"px\";\n    }\n\n    return \"(min-width: \" + value + \")\";\n  }\n  /**\n   * Match a set of breakpoints\n   *\n   * ```tsx\n   * const MidSizeOnly = () => {\n   *   const isMid = useBreakpoint({ lg: 'down', sm: 'up' });\n   *\n   *   if (isMid) return <div>On a Reasonable sized Screen!</div>\n   *   return null;\n   * }\n   * ```\n   * @param breakpointMap An object map of breakpoints and directions, queries are constructed using \"and\" to join\n   * breakpoints together\n   * @param window Optionally specify the target window to match against (useful when rendering into iframes)\n   */\n\n\n  function useBreakpoint(breakpointOrMap, direction, window) {\n    var breakpointMap;\n\n    if (typeof breakpointOrMap === 'object') {\n      breakpointMap = breakpointOrMap;\n      window = direction;\n      direction = true;\n    } else {\n      var _breakpointMap;\n\n      direction = direction || true;\n      breakpointMap = (_breakpointMap = {}, _breakpointMap[breakpointOrMap] = direction, _breakpointMap);\n    }\n\n    var query = useMemo(function () {\n      return Object.entries(breakpointMap).reduce(function (query, _ref) {\n        var key = _ref[0],\n            direction = _ref[1];\n\n        if (direction === 'up' || direction === true) {\n          query = and(query, getMinQuery(key));\n        }\n\n        if (direction === 'down' || direction === true) {\n          query = and(query, getMaxQuery(key));\n        }\n\n        return query;\n      }, '');\n    }, [JSON.stringify(breakpointMap)]);\n    return useMediaQuery(query, window);\n  }\n\n  return useBreakpoint;\n}\nvar useBreakpoint = createBreakpointHook({\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200,\n  xxl: 1400\n});\nexport default useBreakpoint;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,OAAO;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,gBAAgB,EAAE;EACrD,IAAIC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACH,gBAAgB,CAAC;EAEzC,SAASI,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAE;IACxB,IAAID,KAAK,KAAKC,IAAI,EAAE;MAClB,OAAOA,IAAI;IACb;IAEA,OAAOD,KAAK,GAAGA,KAAK,GAAG,OAAO,GAAGC,IAAI,GAAGA,IAAI;EAC9C;EAEA,SAASC,OAAOA,CAACC,UAAU,EAAE;IAC3B,OAAOP,KAAK,CAACQ,IAAI,CAACC,GAAG,CAACT,KAAK,CAACU,OAAO,CAACH,UAAU,CAAC,GAAG,CAAC,EAAEP,KAAK,CAACW,MAAM,GAAG,CAAC,CAAC,CAAC;EACzE;EAEA,SAASC,WAAWA,CAACL,UAAU,EAAE;IAC/B,IAAIF,IAAI,GAAGC,OAAO,CAACC,UAAU,CAAC;IAC9B,IAAIM,KAAK,GAAGd,gBAAgB,CAACM,IAAI,CAAC;IAClC,IAAI,OAAOQ,KAAK,KAAK,QAAQ,EAAEA,KAAK,GAAGA,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,KAAKA,KAAK,GAAG,OAAO,GAAGA,KAAK,GAAG,WAAW;IACpG,OAAO,cAAc,GAAGA,KAAK,GAAG,GAAG;EACrC;EAEA,SAASC,WAAWA,CAACP,UAAU,EAAE;IAC/B,IAAIM,KAAK,GAAGd,gBAAgB,CAACQ,UAAU,CAAC;IAExC,IAAI,OAAOM,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGA,KAAK,GAAG,IAAI;IACtB;IAEA,OAAO,cAAc,GAAGA,KAAK,GAAG,GAAG;EACrC;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE,SAASE,aAAaA,CAACC,eAAe,EAAEC,SAAS,EAAEC,MAAM,EAAE;IACzD,IAAIC,aAAa;IAEjB,IAAI,OAAOH,eAAe,KAAK,QAAQ,EAAE;MACvCG,aAAa,GAAGH,eAAe;MAC/BE,MAAM,GAAGD,SAAS;MAClBA,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM;MACL,IAAIG,cAAc;MAElBH,SAAS,GAAGA,SAAS,IAAI,IAAI;MAC7BE,aAAa,IAAIC,cAAc,GAAG,CAAC,CAAC,EAAEA,cAAc,CAACJ,eAAe,CAAC,GAAGC,SAAS,EAAEG,cAAc,CAAC;IACpG;IAEA,IAAIhB,KAAK,GAAGP,OAAO,CAAC,YAAY;MAC9B,OAAOI,MAAM,CAACoB,OAAO,CAACF,aAAa,CAAC,CAACG,MAAM,CAAC,UAAUlB,KAAK,EAAEmB,IAAI,EAAE;QACjE,IAAIC,GAAG,GAAGD,IAAI,CAAC,CAAC,CAAC;UACbN,SAAS,GAAGM,IAAI,CAAC,CAAC,CAAC;QAEvB,IAAIN,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,IAAI,EAAE;UAC5Cb,KAAK,GAAGD,GAAG,CAACC,KAAK,EAAEU,WAAW,CAACU,GAAG,CAAC,CAAC;QACtC;QAEA,IAAIP,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,IAAI,EAAE;UAC9Cb,KAAK,GAAGD,GAAG,CAACC,KAAK,EAAEQ,WAAW,CAACY,GAAG,CAAC,CAAC;QACtC;QAEA,OAAOpB,KAAK;MACd,CAAC,EAAE,EAAE,CAAC;IACR,CAAC,EAAE,CAACqB,IAAI,CAACC,SAAS,CAACP,aAAa,CAAC,CAAC,CAAC;IACnC,OAAOvB,aAAa,CAACQ,KAAK,EAAEc,MAAM,CAAC;EACrC;EAEA,OAAOH,aAAa;AACtB;AACA,IAAIA,aAAa,GAAGjB,oBAAoB,CAAC;EACvC6B,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,IAAI;EACRC,GAAG,EAAE;AACP,CAAC,CAAC;AACF,eAAejB,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}