{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormCheckInput = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    id,\n    bsPrefix,\n    className,\n    type = 'checkbox',\n    isValid = false,\n    isInvalid = false,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'input',\n    ...props\n  } = _ref;\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-check-input');\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    type: type,\n    id: id || controlId,\n    className: classNames(className, bsPrefix, isValid && 'is-valid', isInvalid && 'is-invalid')\n  });\n});\nFormCheckInput.displayName = 'FormCheckInput';\nexport default FormCheckInput;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "FormContext", "useBootstrapPrefix", "jsx", "_jsx", "FormCheckInput", "forwardRef", "_ref", "ref", "id", "bsPrefix", "className", "type", "<PERSON><PERSON><PERSON><PERSON>", "isInvalid", "as", "Component", "props", "controlId", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/FormCheckInput.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormCheckInput = /*#__PURE__*/React.forwardRef(({\n  id,\n  bsPrefix,\n  className,\n  type = 'checkbox',\n  isValid = false,\n  isInvalid = false,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'input',\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-check-input');\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    type: type,\n    id: id || controlId,\n    className: classNames(className, bsPrefix, isValid && 'is-valid', isInvalid && 'is-invalid')\n  });\n});\nFormCheckInput.displayName = 'FormCheckInput';\nexport default FormCheckInput;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAAC,IAAA,EAUlDC,GAAG,KAAK;EAAA,IAV2C;IACpDC,EAAE;IACFC,QAAQ;IACRC,SAAS;IACTC,IAAI,GAAG,UAAU;IACjBC,OAAO,GAAG,KAAK;IACfC,SAAS,GAAG,KAAK;IACjB;IACAC,EAAE,EAAEC,SAAS,GAAG,OAAO;IACvB,GAAGC;EACL,CAAC,GAAAV,IAAA;EACC,MAAM;IACJW;EACF,CAAC,GAAGlB,UAAU,CAACC,WAAW,CAAC;EAC3BS,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,kBAAkB,CAAC;EAC3D,OAAO,aAAaN,IAAI,CAACY,SAAS,EAAE;IAClC,GAAGC,KAAK;IACRT,GAAG,EAAEA,GAAG;IACRI,IAAI,EAAEA,IAAI;IACVH,EAAE,EAAEA,EAAE,IAAIS,SAAS;IACnBP,SAAS,EAAEb,UAAU,CAACa,SAAS,EAAED,QAAQ,EAAEG,OAAO,IAAI,UAAU,EAAEC,SAAS,IAAI,YAAY;EAC7F,CAAC,CAAC;AACJ,CAAC,CAAC;AACFT,cAAc,CAACc,WAAW,GAAG,gBAAgB;AAC7C,eAAed,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}