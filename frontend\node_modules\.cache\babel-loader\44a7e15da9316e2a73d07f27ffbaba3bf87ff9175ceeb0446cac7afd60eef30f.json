{"ast": null, "code": "'use strict';\n\nvar utils = require('../utils');\nmodule.exports = function normalizeHeaderName(headers, normalizedName) {\n  utils.forEach(headers, function processHeader(value, name) {\n    if (name !== normalizedName && name.toUpperCase() === normalizedName.toUpperCase()) {\n      headers[normalizedName] = value;\n      delete headers[name];\n    }\n  });\n};", "map": {"version": 3, "names": ["utils", "require", "module", "exports", "normalizeHeaderName", "headers", "normalizedName", "for<PERSON>ach", "processHeader", "value", "name", "toUpperCase"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/axios/lib/helpers/normalizeHeaderName.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('../utils');\n\nmodule.exports = function normalizeHeaderName(headers, normalizedName) {\n  utils.forEach(headers, function processHeader(value, name) {\n    if (name !== normalizedName && name.toUpperCase() === normalizedName.toUpperCase()) {\n      headers[normalizedName] = value;\n      delete headers[name];\n    }\n  });\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,UAAU,CAAC;AAE/BC,MAAM,CAACC,OAAO,GAAG,SAASC,mBAAmBA,CAACC,OAAO,EAAEC,cAAc,EAAE;EACrEN,KAAK,CAACO,OAAO,CAACF,OAAO,EAAE,SAASG,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAE;IACzD,IAAIA,IAAI,KAAKJ,cAAc,IAAII,IAAI,CAACC,WAAW,EAAE,KAAKL,cAAc,CAACK,WAAW,EAAE,EAAE;MAClFN,OAAO,CAACC,cAAc,CAAC,GAAGG,KAAK;MAC/B,OAAOJ,OAAO,CAACK,IAAI,CAAC;IACtB;EACF,CAAC,CAAC;AACJ,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}