{"ast": null, "code": "const _excluded = [\"as\", \"disabled\"];\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function isTrivialHref(href) {\n  return !href || href.trim() === '#';\n}\nexport function useButtonProps(_ref2) {\n  let {\n    tagName,\n    disabled,\n    href,\n    target,\n    rel,\n    role,\n    onClick,\n    tabIndex = 0,\n    type\n  } = _ref2;\n  if (!tagName) {\n    if (href != null || target != null || rel != null) {\n      tagName = 'a';\n    } else {\n      tagName = 'button';\n    }\n  }\n  const meta = {\n    tagName\n  };\n  if (tagName === 'button') {\n    return [{\n      type: type || 'button',\n      disabled\n    }, meta];\n  }\n  const handleClick = event => {\n    if (disabled || tagName === 'a' && isTrivialHref(href)) {\n      event.preventDefault();\n    }\n    if (disabled) {\n      event.stopPropagation();\n      return;\n    }\n    onClick == null ? void 0 : onClick(event);\n  };\n  const handleKeyDown = event => {\n    if (event.key === ' ') {\n      event.preventDefault();\n      handleClick(event);\n    }\n  };\n  if (tagName === 'a') {\n    // Ensure there's a href so Enter can trigger anchor button.\n    href || (href = '#');\n    if (disabled) {\n      href = undefined;\n    }\n  }\n  return [{\n    role: role != null ? role : 'button',\n    // explicitly undefined so that it overrides the props disabled in a spread\n    // e.g. <Tag {...props} {...hookProps} />\n    disabled: undefined,\n    tabIndex: disabled ? undefined : tabIndex,\n    href,\n    target: tagName === 'a' ? target : undefined,\n    'aria-disabled': !disabled ? undefined : disabled,\n    rel: tagName === 'a' ? rel : undefined,\n    onClick: handleClick,\n    onKeyDown: handleKeyDown\n  }, meta];\n}\nconst Button = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      as: asProp,\n      disabled\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [buttonProps, {\n    tagName: Component\n  }] = useButtonProps(Object.assign({\n    tagName: asProp,\n    disabled\n  }, props));\n  return /*#__PURE__*/_jsx(Component, Object.assign({}, props, buttonProps, {\n    ref: ref\n  }));\n});\nButton.displayName = 'Button';\nexport default Button;", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "source", "excluded", "target", "sourceKeys", "Object", "keys", "key", "i", "length", "indexOf", "React", "jsx", "_jsx", "isTrivialHref", "href", "trim", "useButtonProps", "_ref2", "tagName", "disabled", "rel", "role", "onClick", "tabIndex", "type", "meta", "handleClick", "event", "preventDefault", "stopPropagation", "handleKeyDown", "undefined", "onKeyDown", "<PERSON><PERSON>", "forwardRef", "_ref", "ref", "as", "asProp", "props", "buttonProps", "Component", "assign", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/ui/esm/Button.js"], "sourcesContent": ["const _excluded = [\"as\", \"disabled\"];\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function isTrivialHref(href) {\n  return !href || href.trim() === '#';\n}\nexport function useButtonProps({\n  tagName,\n  disabled,\n  href,\n  target,\n  rel,\n  role,\n  onClick,\n  tabIndex = 0,\n  type\n}) {\n  if (!tagName) {\n    if (href != null || target != null || rel != null) {\n      tagName = 'a';\n    } else {\n      tagName = 'button';\n    }\n  }\n  const meta = {\n    tagName\n  };\n  if (tagName === 'button') {\n    return [{\n      type: type || 'button',\n      disabled\n    }, meta];\n  }\n  const handleClick = event => {\n    if (disabled || tagName === 'a' && isTrivialHref(href)) {\n      event.preventDefault();\n    }\n    if (disabled) {\n      event.stopPropagation();\n      return;\n    }\n    onClick == null ? void 0 : onClick(event);\n  };\n  const handleKeyDown = event => {\n    if (event.key === ' ') {\n      event.preventDefault();\n      handleClick(event);\n    }\n  };\n  if (tagName === 'a') {\n    // Ensure there's a href so Enter can trigger anchor button.\n    href || (href = '#');\n    if (disabled) {\n      href = undefined;\n    }\n  }\n  return [{\n    role: role != null ? role : 'button',\n    // explicitly undefined so that it overrides the props disabled in a spread\n    // e.g. <Tag {...props} {...hookProps} />\n    disabled: undefined,\n    tabIndex: disabled ? undefined : tabIndex,\n    href,\n    target: tagName === 'a' ? target : undefined,\n    'aria-disabled': !disabled ? undefined : disabled,\n    rel: tagName === 'a' ? rel : undefined,\n    onClick: handleClick,\n    onKeyDown: handleKeyDown\n  }, meta];\n}\nconst Button = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      as: asProp,\n      disabled\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [buttonProps, {\n    tagName: Component\n  }] = useButtonProps(Object.assign({\n    tagName: asProp,\n    disabled\n  }, props));\n  return /*#__PURE__*/_jsx(Component, Object.assign({}, props, buttonProps, {\n    ref: ref\n  }));\n});\nButton.displayName = 'Button';\nexport default Button;"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC;AACpC,SAASC,6BAA6BA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC;EAAE,IAAIM,GAAG,EAAEC,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IAAED,GAAG,GAAGH,UAAU,CAACI,CAAC,CAAC;IAAE,IAAIN,QAAQ,CAACQ,OAAO,CAACH,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUJ,MAAM,CAACI,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC;EAAE;EAAE,OAAOJ,MAAM;AAAE;AAClT,OAAO,KAAKQ,KAAK,MAAM,OAAO;AAC9B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,CAACA,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE,KAAK,GAAG;AACrC;AACA,OAAO,SAASC,cAAcA,CAAAC,KAAA,EAU3B;EAAA,IAV4B;IAC7BC,OAAO;IACPC,QAAQ;IACRL,IAAI;IACJZ,MAAM;IACNkB,GAAG;IACHC,IAAI;IACJC,OAAO;IACPC,QAAQ,GAAG,CAAC;IACZC;EACF,CAAC,GAAAP,KAAA;EACC,IAAI,CAACC,OAAO,EAAE;IACZ,IAAIJ,IAAI,IAAI,IAAI,IAAIZ,MAAM,IAAI,IAAI,IAAIkB,GAAG,IAAI,IAAI,EAAE;MACjDF,OAAO,GAAG,GAAG;IACf,CAAC,MAAM;MACLA,OAAO,GAAG,QAAQ;IACpB;EACF;EACA,MAAMO,IAAI,GAAG;IACXP;EACF,CAAC;EACD,IAAIA,OAAO,KAAK,QAAQ,EAAE;IACxB,OAAO,CAAC;MACNM,IAAI,EAAEA,IAAI,IAAI,QAAQ;MACtBL;IACF,CAAC,EAAEM,IAAI,CAAC;EACV;EACA,MAAMC,WAAW,GAAGC,KAAK,IAAI;IAC3B,IAAIR,QAAQ,IAAID,OAAO,KAAK,GAAG,IAAIL,aAAa,CAACC,IAAI,CAAC,EAAE;MACtDa,KAAK,CAACC,cAAc,EAAE;IACxB;IACA,IAAIT,QAAQ,EAAE;MACZQ,KAAK,CAACE,eAAe,EAAE;MACvB;IACF;IACAP,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,KAAK,CAAC;EAC3C,CAAC;EACD,MAAMG,aAAa,GAAGH,KAAK,IAAI;IAC7B,IAAIA,KAAK,CAACrB,GAAG,KAAK,GAAG,EAAE;MACrBqB,KAAK,CAACC,cAAc,EAAE;MACtBF,WAAW,CAACC,KAAK,CAAC;IACpB;EACF,CAAC;EACD,IAAIT,OAAO,KAAK,GAAG,EAAE;IACnB;IACAJ,IAAI,KAAKA,IAAI,GAAG,GAAG,CAAC;IACpB,IAAIK,QAAQ,EAAE;MACZL,IAAI,GAAGiB,SAAS;IAClB;EACF;EACA,OAAO,CAAC;IACNV,IAAI,EAAEA,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAG,QAAQ;IACpC;IACA;IACAF,QAAQ,EAAEY,SAAS;IACnBR,QAAQ,EAAEJ,QAAQ,GAAGY,SAAS,GAAGR,QAAQ;IACzCT,IAAI;IACJZ,MAAM,EAAEgB,OAAO,KAAK,GAAG,GAAGhB,MAAM,GAAG6B,SAAS;IAC5C,eAAe,EAAE,CAACZ,QAAQ,GAAGY,SAAS,GAAGZ,QAAQ;IACjDC,GAAG,EAAEF,OAAO,KAAK,GAAG,GAAGE,GAAG,GAAGW,SAAS;IACtCT,OAAO,EAAEI,WAAW;IACpBM,SAAS,EAAEF;EACb,CAAC,EAAEL,IAAI,CAAC;AACV;AACA,MAAMQ,MAAM,GAAG,aAAavB,KAAK,CAACwB,UAAU,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;EAC1D,IAAI;MACAC,EAAE,EAAEC,MAAM;MACVnB;IACF,CAAC,GAAGgB,IAAI;IACRI,KAAK,GAAGxC,6BAA6B,CAACoC,IAAI,EAAErC,SAAS,CAAC;EACxD,MAAM,CAAC0C,WAAW,EAAE;IAClBtB,OAAO,EAAEuB;EACX,CAAC,CAAC,GAAGzB,cAAc,CAACZ,MAAM,CAACsC,MAAM,CAAC;IAChCxB,OAAO,EAAEoB,MAAM;IACfnB;EACF,CAAC,EAAEoB,KAAK,CAAC,CAAC;EACV,OAAO,aAAa3B,IAAI,CAAC6B,SAAS,EAAErC,MAAM,CAACsC,MAAM,CAAC,CAAC,CAAC,EAAEH,KAAK,EAAEC,WAAW,EAAE;IACxEJ,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFH,MAAM,CAACU,WAAW,GAAG,QAAQ;AAC7B,eAAeV,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}