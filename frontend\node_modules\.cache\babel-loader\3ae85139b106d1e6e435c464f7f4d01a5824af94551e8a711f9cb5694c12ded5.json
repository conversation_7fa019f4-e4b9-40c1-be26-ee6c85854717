{"ast": null, "code": "var rUpper = /([A-Z])/g;\nexport default function hyphenate(string) {\n  return string.replace(rUpper, '-$1').toLowerCase();\n}", "map": {"version": 3, "names": ["rUpper", "hyphenate", "string", "replace", "toLowerCase"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/dom-helpers/esm/hyphenate.js"], "sourcesContent": ["var rUpper = /([A-Z])/g;\nexport default function hyphenate(string) {\n  return string.replace(rUpper, '-$1').toLowerCase();\n}"], "mappings": "AAAA,IAAIA,MAAM,GAAG,UAAU;AACvB,eAAe,SAASC,SAASA,CAACC,MAAM,EAAE;EACxC,OAAOA,MAAM,CAACC,OAAO,CAACH,MAAM,EAAE,KAAK,CAAC,CAACI,WAAW,EAAE;AACpD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}