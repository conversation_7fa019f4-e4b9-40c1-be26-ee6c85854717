{"ast": null, "code": "import createWithBsPrefix from './createWithBsPrefix';\nconst FigureCaption = createWithBsPrefix('figure-caption', {\n  Component: 'figcaption'\n});\nexport default FigureCaption;", "map": {"version": 3, "names": ["createWithBsPrefix", "FigureCaption", "Component"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/FigureCaption.js"], "sourcesContent": ["import createWithBsPrefix from './createWithBsPrefix';\nconst FigureCaption = createWithBsPrefix('figure-caption', {\n  Component: 'figcaption'\n});\nexport default FigureCaption;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,sBAAsB;AACrD,MAAMC,aAAa,GAAGD,kBAAkB,CAAC,gBAAgB,EAAE;EACzDE,SAAS,EAAE;AACb,CAAC,CAAC;AACF,eAAeD,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}