{"ast": null, "code": "var isNullOrUndefined = require('../internals/is-null-or-undefined');\nvar $TypeError = TypeError;\n\n// `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (isNullOrUndefined(it)) throw $TypeError(\"Can't call method on \" + it);\n  return it;\n};", "map": {"version": 3, "names": ["isNullOrUndefined", "require", "$TypeError", "TypeError", "module", "exports", "it"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/core-js-pure/internals/require-object-coercible.js"], "sourcesContent": ["var isNullOrUndefined = require('../internals/is-null-or-undefined');\n\nvar $TypeError = TypeError;\n\n// `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (isNullOrUndefined(it)) throw $TypeError(\"Can't call method on \" + it);\n  return it;\n};\n"], "mappings": "AAAA,IAAIA,iBAAiB,GAAGC,OAAO,CAAC,mCAAmC,CAAC;AAEpE,IAAIC,UAAU,GAAGC,SAAS;;AAE1B;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,EAAE,EAAE;EAC7B,IAAIN,iBAAiB,CAACM,EAAE,CAAC,EAAE,MAAMJ,UAAU,CAAC,uBAAuB,GAAGI,EAAE,CAAC;EACzE,OAAOA,EAAE;AACX,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}