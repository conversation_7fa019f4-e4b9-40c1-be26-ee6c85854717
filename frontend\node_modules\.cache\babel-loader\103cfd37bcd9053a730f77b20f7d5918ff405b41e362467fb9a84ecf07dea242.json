{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst positionClasses = {\n  'top-start': 'top-0 start-0',\n  'top-center': 'top-0 start-50 translate-middle-x',\n  'top-end': 'top-0 end-0',\n  'middle-start': 'top-50 start-0 translate-middle-y',\n  'middle-center': 'top-50 start-50 translate-middle',\n  'middle-end': 'top-50 end-0 translate-middle-y',\n  'bottom-start': 'bottom-0 start-0',\n  'bottom-center': 'bottom-0 start-50 translate-middle-x',\n  'bottom-end': 'bottom-0 end-0'\n};\nconst ToastContainer = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    position,\n    containerPosition = 'absolute',\n    className,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'toast-container');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(bsPrefix, position && [containerPosition ? `position-${containerPosition}` : null, positionClasses[position]], className)\n  });\n});\nToastContainer.displayName = 'ToastContainer';\nexport default ToastContainer;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "positionClasses", "ToastContainer", "forwardRef", "_ref", "ref", "bsPrefix", "position", "containerPosition", "className", "as", "Component", "props", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/ToastContainer.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst positionClasses = {\n  'top-start': 'top-0 start-0',\n  'top-center': 'top-0 start-50 translate-middle-x',\n  'top-end': 'top-0 end-0',\n  'middle-start': 'top-50 start-0 translate-middle-y',\n  'middle-center': 'top-50 start-50 translate-middle',\n  'middle-end': 'top-50 end-0 translate-middle-y',\n  'bottom-start': 'bottom-0 start-0',\n  'bottom-center': 'bottom-0 start-50 translate-middle-x',\n  'bottom-end': 'bottom-0 end-0'\n};\nconst ToastContainer = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  position,\n  containerPosition = 'absolute',\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'toast-container');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(bsPrefix, position && [containerPosition ? `position-${containerPosition}` : null, positionClasses[position]], className)\n  });\n});\nToastContainer.displayName = 'ToastContainer';\nexport default ToastContainer;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,eAAe,GAAG;EACtB,WAAW,EAAE,eAAe;EAC5B,YAAY,EAAE,mCAAmC;EACjD,SAAS,EAAE,aAAa;EACxB,cAAc,EAAE,mCAAmC;EACnD,eAAe,EAAE,kCAAkC;EACnD,YAAY,EAAE,iCAAiC;EAC/C,cAAc,EAAE,kBAAkB;EAClC,eAAe,EAAE,sCAAsC;EACvD,YAAY,EAAE;AAChB,CAAC;AACD,MAAMC,cAAc,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAAC,IAAA,EAQlDC,GAAG,KAAK;EAAA,IAR2C;IACpDC,QAAQ;IACRC,QAAQ;IACRC,iBAAiB,GAAG,UAAU;IAC9BC,SAAS;IACT;IACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrB,GAAGC;EACL,CAAC,GAAAR,IAAA;EACCE,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,iBAAiB,CAAC;EAC1D,OAAO,aAAaN,IAAI,CAACW,SAAS,EAAE;IAClCN,GAAG,EAAEA,GAAG;IACR,GAAGO,KAAK;IACRH,SAAS,EAAEb,UAAU,CAACU,QAAQ,EAAEC,QAAQ,IAAI,CAACC,iBAAiB,GAAI,YAAWA,iBAAkB,EAAC,GAAG,IAAI,EAAEP,eAAe,CAACM,QAAQ,CAAC,CAAC,EAAEE,SAAS;EAChJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFP,cAAc,CAACW,WAAW,GAAG,gBAAgB;AAC7C,eAAeX,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}