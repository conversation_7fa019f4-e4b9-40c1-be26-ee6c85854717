{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  vertical: false,\n  role: 'group'\n};\nconst ButtonGroup = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    size,\n    vertical,\n    className,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    ...rest\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn-group');\n  let baseClass = prefix;\n  if (vertical) baseClass = `${prefix}-vertical`;\n  return /*#__PURE__*/_jsx(Component, {\n    ...rest,\n    ref: ref,\n    className: classNames(className, baseClass, size && `${prefix}-${size}`)\n  });\n});\nButtonGroup.displayName = 'ButtonGroup';\nButtonGroup.defaultProps = defaultProps;\nexport default ButtonGroup;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "defaultProps", "vertical", "role", "ButtonGroup", "forwardRef", "_ref", "ref", "bsPrefix", "size", "className", "as", "Component", "rest", "prefix", "baseClass", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/ButtonGroup.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  vertical: false,\n  role: 'group'\n};\nconst ButtonGroup = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  vertical,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...rest\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn-group');\n  let baseClass = prefix;\n  if (vertical) baseClass = `${prefix}-vertical`;\n  return /*#__PURE__*/_jsx(Component, {\n    ...rest,\n    ref: ref,\n    className: classNames(className, baseClass, size && `${prefix}-${size}`)\n  });\n});\nButtonGroup.displayName = 'ButtonGroup';\nButtonGroup.defaultProps = defaultProps;\nexport default ButtonGroup;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG;EACnBC,QAAQ,EAAE,KAAK;EACfC,IAAI,EAAE;AACR,CAAC;AACD,MAAMC,WAAW,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAAC,IAAA,EAQ/CC,GAAG,KAAK;EAAA,IARwC;IACjDC,QAAQ;IACRC,IAAI;IACJP,QAAQ;IACRQ,SAAS;IACT;IACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrB,GAAGC;EACL,CAAC,GAAAP,IAAA;EACC,MAAMQ,MAAM,GAAGhB,kBAAkB,CAACU,QAAQ,EAAE,WAAW,CAAC;EACxD,IAAIO,SAAS,GAAGD,MAAM;EACtB,IAAIZ,QAAQ,EAAEa,SAAS,GAAI,GAAED,MAAO,WAAU;EAC9C,OAAO,aAAad,IAAI,CAACY,SAAS,EAAE;IAClC,GAAGC,IAAI;IACPN,GAAG,EAAEA,GAAG;IACRG,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAEK,SAAS,EAAEN,IAAI,IAAK,GAAEK,MAAO,IAAGL,IAAK,EAAC;EACzE,CAAC,CAAC;AACJ,CAAC,CAAC;AACFL,WAAW,CAACY,WAAW,GAAG,aAAa;AACvCZ,WAAW,CAACH,YAAY,GAAGA,YAAY;AACvC,eAAeG,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}