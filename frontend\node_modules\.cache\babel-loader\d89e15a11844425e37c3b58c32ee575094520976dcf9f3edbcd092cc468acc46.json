{"ast": null, "code": "import useUpdatedRef from './useUpdatedRef';\nimport { useEffect } from 'react';\n/**\n * Attach a callback that fires when a component unmounts\n *\n * @param fn Handler to run when the component unmounts\n * @category effects\n */\n\nexport default function useWillUnmount(fn) {\n  var onUnmount = useUpdatedRef(fn);\n  useEffect(function () {\n    return function () {\n      return onUnmount.current();\n    };\n  }, []);\n}", "map": {"version": 3, "names": ["useUpdatedRef", "useEffect", "useWillUnmount", "fn", "onUnmount", "current"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useWillUnmount.js"], "sourcesContent": ["import useUpdatedRef from './useUpdatedRef';\nimport { useEffect } from 'react';\n/**\n * Attach a callback that fires when a component unmounts\n *\n * @param fn Handler to run when the component unmounts\n * @category effects\n */\n\nexport default function useWillUnmount(fn) {\n  var onUnmount = useUpdatedRef(fn);\n  useEffect(function () {\n    return function () {\n      return onUnmount.current();\n    };\n  }, []);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,SAASC,SAAS,QAAQ,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,cAAcA,CAACC,EAAE,EAAE;EACzC,IAAIC,SAAS,GAAGJ,aAAa,CAACG,EAAE,CAAC;EACjCF,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjB,OAAOG,SAAS,CAACC,OAAO,EAAE;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;AACR"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}