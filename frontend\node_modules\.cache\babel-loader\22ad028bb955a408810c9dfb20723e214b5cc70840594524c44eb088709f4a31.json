{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport createUtilityClassName, { responsivePropType } from './createUtilityClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Stack = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    as: Component = 'div',\n    bsPrefix,\n    className,\n    direction,\n    gap,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, direction === 'horizontal' ? 'hstack' : 'vstack');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    className: classNames(className, bsPrefix, ...createUtilityClassName({\n      gap\n    }, breakpoints, minBreakpoint))\n  });\n});\nStack.displayName = 'Stack';\nexport default Stack;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "useBootstrapBreakpoints", "useBootstrapMinBreakpoint", "createUtilityClassName", "responsivePropType", "jsx", "_jsx", "<PERSON><PERSON>", "forwardRef", "_ref", "ref", "as", "Component", "bsPrefix", "className", "direction", "gap", "props", "breakpoints", "minBreakpoint", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Stack.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport createUtilityClassName, { responsivePropType } from './createUtilityClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Stack = /*#__PURE__*/React.forwardRef(({\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  direction,\n  gap,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, direction === 'horizontal' ? 'hstack' : 'vstack');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    className: classNames(className, bsPrefix, ...createUtilityClassName({\n      gap\n    }, breakpoints, minBreakpoint))\n  });\n});\nStack.displayName = 'Stack';\nexport default Stack;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,EAAEC,uBAAuB,EAAEC,yBAAyB,QAAQ,iBAAiB;AACxG,OAAOC,sBAAsB,IAAIC,kBAAkB,QAAQ,wBAAwB;AACnF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,KAAK,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAAC,IAAA,EAOzCC,GAAG,KAAK;EAAA,IAPkC;IAC3CC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrBC,QAAQ;IACRC,SAAS;IACTC,SAAS;IACTC,GAAG;IACH,GAAGC;EACL,CAAC,GAAAR,IAAA;EACCI,QAAQ,GAAGb,kBAAkB,CAACa,QAAQ,EAAEE,SAAS,KAAK,YAAY,GAAG,QAAQ,GAAG,QAAQ,CAAC;EACzF,MAAMG,WAAW,GAAGjB,uBAAuB,EAAE;EAC7C,MAAMkB,aAAa,GAAGjB,yBAAyB,EAAE;EACjD,OAAO,aAAaI,IAAI,CAACM,SAAS,EAAE;IAClC,GAAGK,KAAK;IACRP,GAAG,EAAEA,GAAG;IACRI,SAAS,EAAEhB,UAAU,CAACgB,SAAS,EAAED,QAAQ,EAAE,GAAGV,sBAAsB,CAAC;MACnEa;IACF,CAAC,EAAEE,WAAW,EAAEC,aAAa,CAAC;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC;AACFZ,KAAK,CAACa,WAAW,GAAG,OAAO;AAC3B,eAAeb,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}