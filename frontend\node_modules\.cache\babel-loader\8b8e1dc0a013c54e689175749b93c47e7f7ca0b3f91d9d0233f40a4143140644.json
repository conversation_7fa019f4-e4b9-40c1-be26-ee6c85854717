{"ast": null, "code": "var isObject = require('../internals/is-object');\nvar $String = String;\nvar $TypeError = TypeError;\n\n// `Assert: Type(argument) is Object`\nmodule.exports = function (argument) {\n  if (isObject(argument)) return argument;\n  throw $TypeError($String(argument) + ' is not an object');\n};", "map": {"version": 3, "names": ["isObject", "require", "$String", "String", "$TypeError", "TypeError", "module", "exports", "argument"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/core-js-pure/internals/an-object.js"], "sourcesContent": ["var isObject = require('../internals/is-object');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\n// `Assert: Type(argument) is Object`\nmodule.exports = function (argument) {\n  if (isObject(argument)) return argument;\n  throw $TypeError($String(argument) + ' is not an object');\n};\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAEhD,IAAIC,OAAO,GAAGC,MAAM;AACpB,IAAIC,UAAU,GAAGC,SAAS;;AAE1B;AACAC,MAAM,CAACC,OAAO,GAAG,UAAUC,QAAQ,EAAE;EACnC,IAAIR,QAAQ,CAACQ,QAAQ,CAAC,EAAE,OAAOA,QAAQ;EACvC,MAAMJ,UAAU,CAACF,OAAO,CAACM,QAAQ,CAAC,GAAG,mBAAmB,CAAC;AAC3D,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}