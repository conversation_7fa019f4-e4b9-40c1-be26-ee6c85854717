{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminHeader.jsx\";\nimport React from 'react';\nimport { Navbar, Nav, Dropdown, Badge } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\nimport './AdminHeader.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminHeader = () => {\n  return /*#__PURE__*/_jsxDEV(Navbar, {\n    className: \"admin-header\",\n    expand: \"lg\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-header-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"sidebar-toggle\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-bars\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-circle\",\n            style: {\n              color: '#00BCD4'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-text\",\n            children: \"Pluto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-center\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"page-title\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-right\",\n        children: /*#__PURE__*/_jsxDEV(Nav, {\n          className: \"header-nav\",\n          children: [/*#__PURE__*/_jsxDEV(Nav.Item, {\n            className: \"nav-item-icon\",\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              href: \"#\",\n              className: \"nav-icon\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-bell\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                bg: \"danger\",\n                className: \"notification-badge\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            className: \"nav-item-icon\",\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              href: \"#\",\n              className: \"nav-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-question-circle\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            className: \"nav-item-icon\",\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              href: \"#\",\n              className: \"nav-icon\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-envelope\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                bg: \"danger\",\n                className: \"notification-badge\",\n                children: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n            align: \"end\",\n            children: [/*#__PURE__*/_jsxDEV(Dropdown.Toggle, {\n              variant: \"link\",\n              className: \"user-dropdown\",\n              id: \"user-dropdown\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/api/placeholder/32/32\",\n                alt: \"User\",\n                className: \"user-avatar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"user-name\",\n                children: \"John David\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-chevron-down\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dropdown.Menu, {\n              className: \"user-menu\",\n              children: [/*#__PURE__*/_jsxDEV(Dropdown.Item, {\n                as: Link,\n                to: \"/admin/profile\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-user\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 19\n                }, this), \"Profile\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n                as: Link,\n                to: \"/admin/settings\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-cog\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 19\n                }, this), \"Settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown.Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown.Item, {\n                as: Link,\n                to: \"/logout\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-sign-out-alt\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 19\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = AdminHeader;\nexport default AdminHeader;\nvar _c;\n$RefreshReg$(_c, \"AdminHeader\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Nav", "Dropdown", "Badge", "Link", "jsxDEV", "_jsxDEV", "Ad<PERSON><PERSON><PERSON><PERSON>", "className", "expand", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "<PERSON><PERSON>", "href", "bg", "align", "Toggle", "variant", "id", "src", "alt", "<PERSON><PERSON>", "as", "to", "Divider", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/admin/AdminHeader.jsx"], "sourcesContent": ["import React from 'react';\nimport { Navbar, Nav, Dropdown, Badge } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\nimport './AdminHeader.css';\n\nconst AdminHeader = () => {\n  return (\n    <Navbar className=\"admin-header\" expand=\"lg\">\n      <div className=\"admin-header-content\">\n        <div className=\"header-left\">\n          <button className=\"sidebar-toggle\">\n            <i className=\"fas fa-bars\"></i>\n          </button>\n          \n          <div className=\"logo\">\n            <i className=\"fas fa-circle\" style={{ color: '#00BCD4' }}></i>\n            <span className=\"logo-text\">Pluto</span>\n          </div>\n        </div>\n\n        <div className=\"header-center\">\n          <h4 className=\"page-title\">Dashboard</h4>\n        </div>\n\n        <div className=\"header-right\">\n          <Nav className=\"header-nav\">\n            <Nav.Item className=\"nav-item-icon\">\n              <Nav.Link href=\"#\" className=\"nav-icon\">\n                <i className=\"fas fa-bell\"></i>\n                <Badge bg=\"danger\" className=\"notification-badge\">3</Badge>\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item className=\"nav-item-icon\">\n              <Nav.Link href=\"#\" className=\"nav-icon\">\n                <i className=\"fas fa-question-circle\"></i>\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item className=\"nav-item-icon\">\n              <Nav.Link href=\"#\" className=\"nav-icon\">\n                <i className=\"fas fa-envelope\"></i>\n                <Badge bg=\"danger\" className=\"notification-badge\">1</Badge>\n              </Nav.Link>\n            </Nav.Item>\n\n            <Dropdown align=\"end\">\n              <Dropdown.Toggle \n                variant=\"link\" \n                className=\"user-dropdown\"\n                id=\"user-dropdown\"\n              >\n                <img \n                  src=\"/api/placeholder/32/32\" \n                  alt=\"User\" \n                  className=\"user-avatar\"\n                />\n                <span className=\"user-name\">John David</span>\n                <i className=\"fas fa-chevron-down\"></i>\n              </Dropdown.Toggle>\n\n              <Dropdown.Menu className=\"user-menu\">\n                <Dropdown.Item as={Link} to=\"/admin/profile\">\n                  <i className=\"fas fa-user\"></i>\n                  Profile\n                </Dropdown.Item>\n                <Dropdown.Item as={Link} to=\"/admin/settings\">\n                  <i className=\"fas fa-cog\"></i>\n                  Settings\n                </Dropdown.Item>\n                <Dropdown.Divider />\n                <Dropdown.Item as={Link} to=\"/logout\">\n                  <i className=\"fas fa-sign-out-alt\"></i>\n                  Logout\n                </Dropdown.Item>\n              </Dropdown.Menu>\n            </Dropdown>\n          </Nav>\n        </div>\n      </div>\n    </Navbar>\n  );\n};\n\nexport default AdminHeader;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,iBAAiB;AAC9D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxB,oBACED,OAAA,CAACN,MAAM;IAACQ,SAAS,EAAC,cAAc;IAACC,MAAM,EAAC,IAAI;IAAAC,QAAA,eAC1CJ,OAAA;MAAKE,SAAS,EAAC,sBAAsB;MAAAE,QAAA,gBACnCJ,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAE,QAAA,gBAC1BJ,OAAA;UAAQE,SAAS,EAAC,gBAAgB;UAAAE,QAAA,eAChCJ,OAAA;YAAGE,SAAS,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAK;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACxB,eAETR,OAAA;UAAKE,SAAS,EAAC,MAAM;UAAAE,QAAA,gBACnBJ,OAAA;YAAGE,SAAS,EAAC,eAAe;YAACO,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eAC9DR,OAAA;YAAME,SAAS,EAAC,WAAW;YAAAE,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAO;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACpC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACF,eAENR,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAE,QAAA,eAC5BJ,OAAA;UAAIE,SAAS,EAAC,YAAY;UAAAE,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAK;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACrC,eAENR,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAE,QAAA,eAC3BJ,OAAA,CAACL,GAAG;UAACO,SAAS,EAAC,YAAY;UAAAE,QAAA,gBACzBJ,OAAA,CAACL,GAAG,CAACgB,IAAI;YAACT,SAAS,EAAC,eAAe;YAAAE,QAAA,eACjCJ,OAAA,CAACL,GAAG,CAACG,IAAI;cAACc,IAAI,EAAC,GAAG;cAACV,SAAS,EAAC,UAAU;cAAAE,QAAA,gBACrCJ,OAAA;gBAAGE,SAAS,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,eAC/BR,OAAA,CAACH,KAAK;gBAACgB,EAAE,EAAC,QAAQ;gBAACX,SAAS,EAAC,oBAAoB;gBAAAE,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAQ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAClD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXR,OAAA,CAACL,GAAG,CAACgB,IAAI;YAACT,SAAS,EAAC,eAAe;YAAAE,QAAA,eACjCJ,OAAA,CAACL,GAAG,CAACG,IAAI;cAACc,IAAI,EAAC,GAAG;cAACV,SAAS,EAAC,UAAU;cAAAE,QAAA,eACrCJ,OAAA;gBAAGE,SAAS,EAAC;cAAwB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YAAK;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACjC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXR,OAAA,CAACL,GAAG,CAACgB,IAAI;YAACT,SAAS,EAAC,eAAe;YAAAE,QAAA,eACjCJ,OAAA,CAACL,GAAG,CAACG,IAAI;cAACc,IAAI,EAAC,GAAG;cAACV,SAAS,EAAC,UAAU;cAAAE,QAAA,gBACrCJ,OAAA;gBAAGE,SAAS,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,eACnCR,OAAA,CAACH,KAAK;gBAACgB,EAAE,EAAC,QAAQ;gBAACX,SAAS,EAAC,oBAAoB;gBAAAE,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAQ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAClD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXR,OAAA,CAACJ,QAAQ;YAACkB,KAAK,EAAC,KAAK;YAAAV,QAAA,gBACnBJ,OAAA,CAACJ,QAAQ,CAACmB,MAAM;cACdC,OAAO,EAAC,MAAM;cACdd,SAAS,EAAC,eAAe;cACzBe,EAAE,EAAC,eAAe;cAAAb,QAAA,gBAElBJ,OAAA;gBACEkB,GAAG,EAAC,wBAAwB;gBAC5BC,GAAG,EAAC,MAAM;gBACVjB,SAAS,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACvB,eACFR,OAAA;gBAAME,SAAS,EAAC,WAAW;gBAAAE,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAO,eAC7CR,OAAA;gBAAGE,SAAS,EAAC;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACvB,eAElBR,OAAA,CAACJ,QAAQ,CAACwB,IAAI;cAAClB,SAAS,EAAC,WAAW;cAAAE,QAAA,gBAClCJ,OAAA,CAACJ,QAAQ,CAACe,IAAI;gBAACU,EAAE,EAAEvB,IAAK;gBAACwB,EAAE,EAAC,gBAAgB;gBAAAlB,QAAA,gBAC1CJ,OAAA;kBAAGE,SAAS,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAK,WAEjC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAgB,eAChBR,OAAA,CAACJ,QAAQ,CAACe,IAAI;gBAACU,EAAE,EAAEvB,IAAK;gBAACwB,EAAE,EAAC,iBAAiB;gBAAAlB,QAAA,gBAC3CJ,OAAA;kBAAGE,SAAS,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAK,YAEhC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAgB,eAChBR,OAAA,CAACJ,QAAQ,CAAC2B,OAAO;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAG,eACpBR,OAAA,CAACJ,QAAQ,CAACe,IAAI;gBAACU,EAAE,EAAEvB,IAAK;gBAACwB,EAAE,EAAC,SAAS;gBAAAlB,QAAA,gBACnCJ,OAAA;kBAAGE,SAAS,EAAC;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAK,UAEzC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAgB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EACF;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACC;AAEb,CAAC;AAACgB,EAAA,GA7EIvB,WAAW;AA+EjB,eAAeA,WAAW;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}