{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\brandCard.jsx\";\nimport React from \"react\";\nimport { Card } from \"react-bootstrap\";\nimport { Link } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction BrandCard(_ref) {\n  let {\n    brand\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"my-3 p-3 rounded\",\n    children: [/*#__PURE__*/_jsxDEV(Link, {\n      to: `/search?keyword=&brand=${brand.id}&category=`,\n      children: /*#__PURE__*/_jsxDEV(Card.Img, {\n        src: brand.image,\n        alt: brand.title,\n        style: {\n          objectFit: 'contain',\n          minHeight: '4rem'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: `/search?keyword=&brand=${brand.id}&category=`,\n        className: \"text-decoration-none\",\n        children: /*#__PURE__*/_jsxDEV(Card.Title, {\n          as: \"div\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: brand.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = BrandCard;\nexport default BrandCard;\nvar _c;\n$RefreshReg$(_c, \"BrandCard\");", "map": {"version": 3, "names": ["React", "Card", "Link", "jsxDEV", "_jsxDEV", "BrandCard", "_ref", "brand", "className", "children", "to", "id", "Img", "src", "image", "alt", "title", "style", "objectFit", "minHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "Title", "as", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/brandCard.jsx"], "sourcesContent": ["import React from \"react\";\nimport { Card } from \"react-bootstrap\";\nimport { Link } from \"react-router-dom\";\n\nfunction BrandCard({brand}) {\n  return (\n    <Card className=\"my-3 p-3 rounded\">\n      <Link to={`/search?keyword=&brand=${brand.id}&category=`}>\n        <Card.Img src={brand.image} alt={brand.title} style={{objectFit: 'contain',minHeight:'4rem'}}/>\n      </Link>\n      <Card.Body>\n        <Link to={`/search?keyword=&brand=${brand.id}&category=`} className=\"text-decoration-none\">\n          <Card.Title as=\"div\">\n            <strong>{brand.title}</strong>\n          </Card.Title>\n        </Link>\n        {/* <Card.Text as=\"p\">{brand.description}</Card.Text> */}\n      </Card.Body>\n    </Card>\n  );\n}\n\nexport default BrandCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,SAASA,CAAAC,IAAA,EAAU;EAAA,IAAT;IAACC;EAAK,CAAC,GAAAD,IAAA;EACxB,oBACEF,OAAA,CAACH,IAAI;IAACO,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAChCL,OAAA,CAACF,IAAI;MAACQ,EAAE,EAAG,0BAAyBH,KAAK,CAACI,EAAG,YAAY;MAAAF,QAAA,eACvDL,OAAA,CAACH,IAAI,CAACW,GAAG;QAACC,GAAG,EAAEN,KAAK,CAACO,KAAM;QAACC,GAAG,EAAER,KAAK,CAACS,KAAM;QAACC,KAAK,EAAE;UAACC,SAAS,EAAE,SAAS;UAACC,SAAS,EAAC;QAAM;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC1F,eACPnB,OAAA,CAACH,IAAI,CAACuB,IAAI;MAAAf,QAAA,eACRL,OAAA,CAACF,IAAI;QAACQ,EAAE,EAAG,0BAAyBH,KAAK,CAACI,EAAG,YAAY;QAACH,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACxFL,OAAA,CAACH,IAAI,CAACwB,KAAK;UAACC,EAAE,EAAC,KAAK;UAAAjB,QAAA,eAClBL,OAAA;YAAAK,QAAA,EAASF,KAAK,CAACS;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAU;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACnB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAEG;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACP;AAEX;AAACI,EAAA,GAhBQtB,SAAS;AAkBlB,eAAeA,SAAS;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}