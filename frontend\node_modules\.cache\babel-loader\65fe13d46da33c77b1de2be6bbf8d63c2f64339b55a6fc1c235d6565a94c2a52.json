{"ast": null, "code": "import { createContext, useContext } from 'react';\nimport canUseDOM from 'dom-helpers/canUseDOM';\nconst Context = /*#__PURE__*/createContext(canUseDOM ? window : undefined);\nexport const WindowProvider = Context.Provider;\n\n/**\n * The document \"window\" placed in React context. Helpful for determining\n * SSR context, or when rendering into an iframe.\n *\n * @returns the current window\n */\nexport default function useWindow() {\n  return useContext(Context);\n}", "map": {"version": 3, "names": ["createContext", "useContext", "canUseDOM", "Context", "window", "undefined", "WindowProvider", "Provider", "useWindow"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/ui/esm/useWindow.js"], "sourcesContent": ["import { createContext, useContext } from 'react';\nimport canUseDOM from 'dom-helpers/canUseDOM';\nconst Context = /*#__PURE__*/createContext(canUseDOM ? window : undefined);\nexport const WindowProvider = Context.Provider;\n\n/**\n * The document \"window\" placed in React context. Helpful for determining\n * SSR context, or when rendering into an iframe.\n *\n * @returns the current window\n */\nexport default function useWindow() {\n  return useContext(Context);\n}"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,OAAO;AACjD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,MAAMC,OAAO,GAAG,aAAaH,aAAa,CAACE,SAAS,GAAGE,MAAM,GAAGC,SAAS,CAAC;AAC1E,OAAO,MAAMC,cAAc,GAAGH,OAAO,CAACI,QAAQ;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,SAASA,CAAA,EAAG;EAClC,OAAOP,UAAU,CAACE,OAAO,CAAC;AAC5B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}