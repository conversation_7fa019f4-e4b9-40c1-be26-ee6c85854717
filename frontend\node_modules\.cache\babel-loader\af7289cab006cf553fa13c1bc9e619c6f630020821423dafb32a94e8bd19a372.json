{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    striped,\n    bordered,\n    borderless,\n    hover,\n    size,\n    variant,\n    responsive,\n    ...props\n  } = _ref;\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nexport default Table;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "Table", "forwardRef", "_ref", "ref", "bsPrefix", "className", "striped", "bordered", "borderless", "hover", "size", "variant", "responsive", "props", "decoratedBsPrefix", "classes", "table", "responsiveClass", "children"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Table.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nexport default Table;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,KAAK,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAAC,IAAA,EAWzCC,GAAG,KAAK;EAAA,IAXkC;IAC3CC,QAAQ;IACRC,SAAS;IACTC,OAAO;IACPC,QAAQ;IACRC,UAAU;IACVC,KAAK;IACLC,IAAI;IACJC,OAAO;IACPC,UAAU;IACV,GAAGC;EACL,CAAC,GAAAX,IAAA;EACC,MAAMY,iBAAiB,GAAGjB,kBAAkB,CAACO,QAAQ,EAAE,OAAO,CAAC;EAC/D,MAAMW,OAAO,GAAGpB,UAAU,CAACU,SAAS,EAAES,iBAAiB,EAAEH,OAAO,IAAK,GAAEG,iBAAkB,IAAGH,OAAQ,EAAC,EAAED,IAAI,IAAK,GAAEI,iBAAkB,IAAGJ,IAAK,EAAC,EAAEJ,OAAO,IAAK,GAAEQ,iBAAkB,IAAG,OAAOR,OAAO,KAAK,QAAQ,GAAI,WAAUA,OAAQ,EAAC,GAAG,SAAU,EAAC,EAAEC,QAAQ,IAAK,GAAEO,iBAAkB,WAAU,EAAEN,UAAU,IAAK,GAAEM,iBAAkB,aAAY,EAAEL,KAAK,IAAK,GAAEK,iBAAkB,QAAO,CAAC;EACxX,MAAME,KAAK,GAAG,aAAajB,IAAI,CAAC,OAAO,EAAE;IACvC,GAAGc,KAAK;IACRR,SAAS,EAAEU,OAAO;IAClBZ,GAAG,EAAEA;EACP,CAAC,CAAC;EACF,IAAIS,UAAU,EAAE;IACd,IAAIK,eAAe,GAAI,GAAEH,iBAAkB,aAAY;IACvD,IAAI,OAAOF,UAAU,KAAK,QAAQ,EAAE;MAClCK,eAAe,GAAI,GAAEA,eAAgB,IAAGL,UAAW,EAAC;IACtD;IACA,OAAO,aAAab,IAAI,CAAC,KAAK,EAAE;MAC9BM,SAAS,EAAEY,eAAe;MAC1BC,QAAQ,EAAEF;IACZ,CAAC,CAAC;EACJ;EACA,OAAOA,KAAK;AACd,CAAC,CAAC;AACF,eAAehB,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}