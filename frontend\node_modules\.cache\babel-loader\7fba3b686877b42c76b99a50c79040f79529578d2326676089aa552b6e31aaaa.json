{"ast": null, "code": "const reportWebVitals = onPerfEntry => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(_ref => {\n      let {\n        getCLS,\n        getFID,\n        getFCP,\n        getLCP,\n        getTTFB\n      } = _ref;\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\nexport default reportWebVitals;", "map": {"version": 3, "names": ["reportWebVitals", "onPerfEntry", "Function", "then", "_ref", "getCLS", "getFID", "getFCP", "getLCP", "getTTFB"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/reportWebVitals.js"], "sourcesContent": ["const reportWebVitals = onPerfEntry => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\n\nexport default reportWebVitals;\n"], "mappings": "AAAA,MAAMA,eAAe,GAAGC,WAAW,IAAI;EACrC,IAAIA,WAAW,IAAIA,WAAW,YAAYC,QAAQ,EAAE;IAClD,MAAM,CAAC,YAAY,CAAC,CAACC,IAAI,CAACC,IAAA,IAAiD;MAAA,IAAhD;QAAEC,MAAM;QAAEC,MAAM;QAAEC,MAAM;QAAEC,MAAM;QAAEC;MAAQ,CAAC,GAAAL,IAAA;MACpEC,MAAM,CAACJ,WAAW,CAAC;MACnBK,MAAM,CAACL,WAAW,CAAC;MACnBM,MAAM,CAACN,WAAW,CAAC;MACnBO,MAAM,CAACP,WAAW,CAAC;MACnBQ,OAAO,CAACR,WAAW,CAAC;IACtB,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAeD,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}