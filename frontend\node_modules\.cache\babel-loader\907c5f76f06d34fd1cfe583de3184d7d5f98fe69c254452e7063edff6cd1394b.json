{"ast": null, "code": "import * as React from 'react';\nimport Tabs from '@restart/ui/Tabs';\nimport getTabTransitionComponent from './getTabTransitionComponent';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst TabContainer = _ref => {\n  let {\n    transition,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/_jsx(Tabs, {\n    ...props,\n    transition: getTabTransitionComponent(transition)\n  });\n};\nTabContainer.displayName = 'TabContainer';\nexport default TabContainer;", "map": {"version": 3, "names": ["React", "Tabs", "getTabTransitionComponent", "jsx", "_jsx", "TabContainer", "_ref", "transition", "props", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/TabContainer.js"], "sourcesContent": ["import * as React from 'react';\nimport Tabs from '@restart/ui/Tabs';\nimport getTabTransitionComponent from './getTabTransitionComponent';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst TabContainer = ({\n  transition,\n  ...props\n}) => /*#__PURE__*/_jsx(Tabs, {\n  ...props,\n  transition: getTabTransitionComponent(transition)\n});\nTabContainer.displayName = 'TabContainer';\nexport default TabContainer;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAGC,IAAA;EAAA,IAAC;IACpBC,UAAU;IACV,GAAGC;EACL,CAAC,GAAAF,IAAA;EAAA,OAAK,aAAaF,IAAI,CAACH,IAAI,EAAE;IAC5B,GAAGO,KAAK;IACRD,UAAU,EAAEL,yBAAyB,CAACK,UAAU;EAClD,CAAC,CAAC;AAAA;AACFF,YAAY,CAACI,WAAW,GAAG,cAAc;AACzC,eAAeJ,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}