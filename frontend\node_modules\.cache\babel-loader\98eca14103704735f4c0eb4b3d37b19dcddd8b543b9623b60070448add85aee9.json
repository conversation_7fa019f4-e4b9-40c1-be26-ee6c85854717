{"ast": null, "code": "import classNames from 'classnames';\nimport all from 'prop-types-extra/lib/all';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport BaseNav from '@restart/ui/Nav';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport CardHeaderContext from './CardHeaderContext';\nimport NavItem from './NavItem';\nimport NavLink from './NavLink';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  justify: false,\n  fill: false\n};\nconst Nav = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    as = 'div',\n    bsPrefix: initialBsPrefix,\n    variant,\n    fill,\n    justify,\n    navbar,\n    navbarScroll,\n    className,\n    activeKey,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    activeKey: 'onSelect'\n  });\n  const bsPrefix = useBootstrapPrefix(initialBsPrefix, 'nav');\n  let navbarBsPrefix;\n  let cardHeaderBsPrefix;\n  let isNavbar = false;\n  const navbarContext = useContext(NavbarContext);\n  const cardHeaderContext = useContext(CardHeaderContext);\n  if (navbarContext) {\n    navbarBsPrefix = navbarContext.bsPrefix;\n    isNavbar = navbar == null ? true : navbar;\n  } else if (cardHeaderContext) {\n    ({\n      cardHeaderBsPrefix\n    } = cardHeaderContext);\n  }\n  return /*#__PURE__*/_jsx(BaseNav, {\n    as: as,\n    ref: ref,\n    activeKey: activeKey,\n    className: classNames(className, {\n      [bsPrefix]: !isNavbar,\n      [`${navbarBsPrefix}-nav`]: isNavbar,\n      [`${navbarBsPrefix}-nav-scroll`]: isNavbar && navbarScroll,\n      [`${cardHeaderBsPrefix}-${variant}`]: !!cardHeaderBsPrefix,\n      [`${bsPrefix}-${variant}`]: !!variant,\n      [`${bsPrefix}-fill`]: fill,\n      [`${bsPrefix}-justified`]: justify\n    }),\n    ...props\n  });\n});\nNav.displayName = 'Nav';\nNav.defaultProps = defaultProps;\nexport default Object.assign(Nav, {\n  Item: NavItem,\n  Link: NavLink\n});", "map": {"version": 3, "names": ["classNames", "all", "React", "useContext", "useUncontrolled", "BaseNav", "useBootstrapPrefix", "NavbarContext", "CardHeaderContext", "NavItem", "NavLink", "jsx", "_jsx", "defaultProps", "justify", "fill", "Nav", "forwardRef", "uncontrolledProps", "ref", "as", "bsPrefix", "initialBsPrefix", "variant", "navbar", "navbarScroll", "className", "active<PERSON><PERSON>", "props", "navbarBsPrefix", "cardHeaderBsPrefix", "isNavbar", "navbarContext", "cardHeaderContext", "displayName", "Object", "assign", "<PERSON><PERSON>", "Link"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Nav.js"], "sourcesContent": ["import classNames from 'classnames';\nimport all from 'prop-types-extra/lib/all';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport BaseNav from '@restart/ui/Nav';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport CardHeaderContext from './CardHeaderContext';\nimport NavItem from './NavItem';\nimport NavLink from './NavLink';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  justify: false,\n  fill: false\n};\nconst Nav = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    as = 'div',\n    bsPrefix: initialBsPrefix,\n    variant,\n    fill,\n    justify,\n    navbar,\n    navbarScroll,\n    className,\n    activeKey,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    activeKey: 'onSelect'\n  });\n  const bsPrefix = useBootstrapPrefix(initialBsPrefix, 'nav');\n  let navbarBsPrefix;\n  let cardHeaderBsPrefix;\n  let isNavbar = false;\n  const navbarContext = useContext(NavbarContext);\n  const cardHeaderContext = useContext(CardHeaderContext);\n  if (navbarContext) {\n    navbarBsPrefix = navbarContext.bsPrefix;\n    isNavbar = navbar == null ? true : navbar;\n  } else if (cardHeaderContext) {\n    ({\n      cardHeaderBsPrefix\n    } = cardHeaderContext);\n  }\n  return /*#__PURE__*/_jsx(BaseNav, {\n    as: as,\n    ref: ref,\n    activeKey: activeKey,\n    className: classNames(className, {\n      [bsPrefix]: !isNavbar,\n      [`${navbarBsPrefix}-nav`]: isNavbar,\n      [`${navbarBsPrefix}-nav-scroll`]: isNavbar && navbarScroll,\n      [`${cardHeaderBsPrefix}-${variant}`]: !!cardHeaderBsPrefix,\n      [`${bsPrefix}-${variant}`]: !!variant,\n      [`${bsPrefix}-fill`]: fill,\n      [`${bsPrefix}-justified`]: justify\n    }),\n    ...props\n  });\n});\nNav.displayName = 'Nav';\nNav.defaultProps = defaultProps;\nexport default Object.assign(Nav, {\n  Item: NavItem,\n  Link: NavLink\n});"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,GAAG,MAAM,0BAA0B;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,KAAK;EACdC,IAAI,EAAE;AACR,CAAC;AACD,MAAMC,GAAG,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,CAACC,iBAAiB,EAAEC,GAAG,KAAK;EACpE,MAAM;IACJC,EAAE,GAAG,KAAK;IACVC,QAAQ,EAAEC,eAAe;IACzBC,OAAO;IACPR,IAAI;IACJD,OAAO;IACPU,MAAM;IACNC,YAAY;IACZC,SAAS;IACTC,SAAS;IACT,GAAGC;EACL,CAAC,GAAGxB,eAAe,CAACc,iBAAiB,EAAE;IACrCS,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMN,QAAQ,GAAGf,kBAAkB,CAACgB,eAAe,EAAE,KAAK,CAAC;EAC3D,IAAIO,cAAc;EAClB,IAAIC,kBAAkB;EACtB,IAAIC,QAAQ,GAAG,KAAK;EACpB,MAAMC,aAAa,GAAG7B,UAAU,CAACI,aAAa,CAAC;EAC/C,MAAM0B,iBAAiB,GAAG9B,UAAU,CAACK,iBAAiB,CAAC;EACvD,IAAIwB,aAAa,EAAE;IACjBH,cAAc,GAAGG,aAAa,CAACX,QAAQ;IACvCU,QAAQ,GAAGP,MAAM,IAAI,IAAI,GAAG,IAAI,GAAGA,MAAM;EAC3C,CAAC,MAAM,IAAIS,iBAAiB,EAAE;IAC5B,CAAC;MACCH;IACF,CAAC,GAAGG,iBAAiB;EACvB;EACA,OAAO,aAAarB,IAAI,CAACP,OAAO,EAAE;IAChCe,EAAE,EAAEA,EAAE;IACND,GAAG,EAAEA,GAAG;IACRQ,SAAS,EAAEA,SAAS;IACpBD,SAAS,EAAE1B,UAAU,CAAC0B,SAAS,EAAE;MAC/B,CAACL,QAAQ,GAAG,CAACU,QAAQ;MACrB,CAAE,GAAEF,cAAe,MAAK,GAAGE,QAAQ;MACnC,CAAE,GAAEF,cAAe,aAAY,GAAGE,QAAQ,IAAIN,YAAY;MAC1D,CAAE,GAAEK,kBAAmB,IAAGP,OAAQ,EAAC,GAAG,CAAC,CAACO,kBAAkB;MAC1D,CAAE,GAAET,QAAS,IAAGE,OAAQ,EAAC,GAAG,CAAC,CAACA,OAAO;MACrC,CAAE,GAAEF,QAAS,OAAM,GAAGN,IAAI;MAC1B,CAAE,GAAEM,QAAS,YAAW,GAAGP;IAC7B,CAAC,CAAC;IACF,GAAGc;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFZ,GAAG,CAACkB,WAAW,GAAG,KAAK;AACvBlB,GAAG,CAACH,YAAY,GAAGA,YAAY;AAC/B,eAAesB,MAAM,CAACC,MAAM,CAACpB,GAAG,EAAE;EAChCqB,IAAI,EAAE5B,OAAO;EACb6B,IAAI,EAAE5B;AACR,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}