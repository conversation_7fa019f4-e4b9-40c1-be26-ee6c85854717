[{"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "2", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js": "3", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "4", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "5", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "6", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "7", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "8", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "9", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "10", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "11", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "12", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "13", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "14", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "15", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "16", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "17", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "18", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "19", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "20", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "21", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "22", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx": "23", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "24", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "25", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "26", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "27", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx": "28", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx": "29", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "30", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "31", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "32", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "33", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "34", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "35", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "36", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx": "37", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx": "38", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "39", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx": "40", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx": "41", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx": "42", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx": "43", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx": "44", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx": "45", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx": "46"}, {"size": 649, "mtime": 1750943544495, "results": "47", "hashOfConfig": "48"}, {"size": 362, "mtime": 1750943544502, "results": "49", "hashOfConfig": "48"}, {"size": 4473, "mtime": 1750994199878, "results": "50", "hashOfConfig": "48"}, {"size": 1585, "mtime": 1750943544494, "results": "51", "hashOfConfig": "48"}, {"size": 4116, "mtime": 1750943544494, "results": "52", "hashOfConfig": "48"}, {"size": 4438, "mtime": 1750993170933, "results": "53", "hashOfConfig": "48"}, {"size": 1795, "mtime": 1750943544489, "results": "54", "hashOfConfig": "48"}, {"size": 3086, "mtime": 1750993154771, "results": "55", "hashOfConfig": "48"}, {"size": 562, "mtime": 1750943544488, "results": "56", "hashOfConfig": "48"}, {"size": 1880, "mtime": 1750943544498, "results": "57", "hashOfConfig": "48"}, {"size": 4041, "mtime": 1750943544496, "results": "58", "hashOfConfig": "48"}, {"size": 4556, "mtime": 1750943544500, "results": "59", "hashOfConfig": "48"}, {"size": 3166, "mtime": 1750943544501, "results": "60", "hashOfConfig": "48"}, {"size": 3374, "mtime": 1750943544501, "results": "61", "hashOfConfig": "48"}, {"size": 2746, "mtime": 1750943544500, "results": "62", "hashOfConfig": "48"}, {"size": 557, "mtime": 1750943544498, "results": "63", "hashOfConfig": "48"}, {"size": 1873, "mtime": 1750945277042, "results": "64", "hashOfConfig": "48"}, {"size": 5003, "mtime": 1750943544499, "results": "65", "hashOfConfig": "48"}, {"size": 1876, "mtime": 1750943544499, "results": "66", "hashOfConfig": "48"}, {"size": 6146, "mtime": 1750943544499, "results": "67", "hashOfConfig": "48"}, {"size": 4506, "mtime": 1750943544501, "results": "68", "hashOfConfig": "48"}, {"size": 392, "mtime": 1750943544489, "results": "69", "hashOfConfig": "48"}, {"size": 988, "mtime": 1750943544487, "results": "70", "hashOfConfig": "48"}, {"size": 373, "mtime": 1750943544502, "results": "71", "hashOfConfig": "48"}, {"size": 1221, "mtime": 1750943544493, "results": "72", "hashOfConfig": "48"}, {"size": 228, "mtime": 1750943544490, "results": "73", "hashOfConfig": "48"}, {"size": 1101, "mtime": 1750943544491, "results": "74", "hashOfConfig": "48"}, {"size": 737, "mtime": 1750943544487, "results": "75", "hashOfConfig": "48"}, {"size": 901, "mtime": 1750943544491, "results": "76", "hashOfConfig": "48"}, {"size": 3665, "mtime": 1750943544493, "results": "77", "hashOfConfig": "48"}, {"size": 372, "mtime": 1750943544489, "results": "78", "hashOfConfig": "48"}, {"size": 2548, "mtime": 1750943544493, "results": "79", "hashOfConfig": "48"}, {"size": 2420, "mtime": 1750943544490, "results": "80", "hashOfConfig": "48"}, {"size": 1519, "mtime": 1750943544488, "results": "81", "hashOfConfig": "48"}, {"size": 639, "mtime": 1750943544492, "results": "82", "hashOfConfig": "48"}, {"size": 1826, "mtime": 1750943544491, "results": "83", "hashOfConfig": "48"}, {"size": 11285, "mtime": 1750993002543, "results": "84", "hashOfConfig": "48"}, {"size": 10483, "mtime": 1750993066396, "results": "85", "hashOfConfig": "48"}, {"size": 7428, "mtime": 1750994039308, "results": "86", "hashOfConfig": "48"}, {"size": 445, "mtime": 1750994572623, "results": "87", "hashOfConfig": "48"}, {"size": 2869, "mtime": 1750992850126, "results": "88", "hashOfConfig": "48"}, {"size": 3307, "mtime": 1750993684234, "results": "89", "hashOfConfig": "48"}, {"size": 6893, "mtime": 1750993259064, "results": "90", "hashOfConfig": "48"}, {"size": 551, "mtime": 1750993273555, "results": "91", "hashOfConfig": "48"}, {"size": 6723, "mtime": 1750994176856, "results": "92", "hashOfConfig": "48"}, {"size": 8101, "mtime": 1750994147005, "results": "93", "hashOfConfig": "48"}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xy8mk5", {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js", ["232"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["233"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["234", "235", "236"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["237", "238", "239", "240", "241", "242", "243", "244", "245"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["246", "247"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["248"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["249", "250"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", ["251", "252"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["253", "254"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", ["255"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", ["256", "257"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["258"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["259"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", ["260", "261", "262"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", ["263"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["264", "265", "266", "267"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["268", "269", "270", "271", "272", "273", "274", "275", "276"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["277", "278"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["279"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["280", "281"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["282"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["283", "284", "285"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx", ["286"], [], {"ruleId": "287", "severity": 1, "message": "288", "line": 39, "column": 3, "nodeType": "289", "endLine": 39, "endColumn": 12, "suggestions": "290"}, {"ruleId": "291", "severity": 1, "message": "292", "line": 35, "column": 49, "nodeType": "293", "messageId": "294", "endLine": 35, "endColumn": 51}, {"ruleId": "291", "severity": 1, "message": "292", "line": 62, "column": 18, "nodeType": "293", "messageId": "294", "endLine": 62, "endColumn": 20}, {"ruleId": "291", "severity": 1, "message": "292", "line": 68, "column": 15, "nodeType": "293", "messageId": "294", "endLine": 68, "endColumn": 17}, {"ruleId": "291", "severity": 1, "message": "292", "line": 125, "column": 45, "nodeType": "293", "messageId": "294", "endLine": 125, "endColumn": 47}, {"ruleId": "295", "severity": 1, "message": "296", "line": 58, "column": 15, "nodeType": "289", "messageId": "297", "endLine": 58, "endColumn": 19}, {"ruleId": "287", "severity": 1, "message": "298", "line": 104, "column": 6, "nodeType": "299", "endLine": 104, "endColumn": 8, "suggestions": "300"}, {"ruleId": "287", "severity": 1, "message": "301", "line": 116, "column": 6, "nodeType": "299", "endLine": 116, "endColumn": 18, "suggestions": "302"}, {"ruleId": "291", "severity": 1, "message": "303", "line": 121, "column": 20, "nodeType": "293", "messageId": "294", "endLine": 121, "endColumn": 22}, {"ruleId": "291", "severity": 1, "message": "303", "line": 121, "column": 53, "nodeType": "293", "messageId": "294", "endLine": 121, "endColumn": 55}, {"ruleId": "291", "severity": 1, "message": "303", "line": 125, "column": 17, "nodeType": "293", "messageId": "294", "endLine": 125, "endColumn": 19}, {"ruleId": "291", "severity": 1, "message": "303", "line": 125, "column": 44, "nodeType": "293", "messageId": "294", "endLine": 125, "endColumn": 46}, {"ruleId": "291", "severity": 1, "message": "303", "line": 129, "column": 20, "nodeType": "293", "messageId": "294", "endLine": 129, "endColumn": 22}, {"ruleId": "291", "severity": 1, "message": "292", "line": 133, "column": 16, "nodeType": "293", "messageId": "294", "endLine": 133, "endColumn": 18}, {"ruleId": "295", "severity": 1, "message": "304", "line": 13, "column": 23, "nodeType": "289", "messageId": "297", "endLine": 13, "endColumn": 38}, {"ruleId": "287", "severity": 1, "message": "305", "line": 27, "column": 6, "nodeType": "299", "endLine": 27, "endColumn": 8, "suggestions": "306"}, {"ruleId": "307", "severity": 1, "message": "308", "line": 14, "column": 13, "nodeType": "309", "messageId": "310", "endLine": 14, "endColumn": 107, "fix": "311"}, {"ruleId": "287", "severity": 1, "message": "312", "line": 21, "column": 6, "nodeType": "299", "endLine": 21, "endColumn": 8, "suggestions": "313"}, {"ruleId": "291", "severity": 1, "message": "303", "line": 25, "column": 13, "nodeType": "293", "messageId": "294", "endLine": 25, "endColumn": 15}, {"ruleId": "295", "severity": 1, "message": "314", "line": 1, "column": 29, "nodeType": "289", "messageId": "297", "endLine": 1, "endColumn": 38}, {"ruleId": "291", "severity": 1, "message": "303", "line": 25, "column": 13, "nodeType": "293", "messageId": "294", "endLine": 25, "endColumn": 15}, {"ruleId": "287", "severity": 1, "message": "315", "line": 34, "column": 6, "nodeType": "299", "endLine": 34, "endColumn": 8, "suggestions": "316"}, {"ruleId": "291", "severity": 1, "message": "303", "line": 43, "column": 13, "nodeType": "293", "messageId": "294", "endLine": 43, "endColumn": 15}, {"ruleId": "287", "severity": 1, "message": "317", "line": 31, "column": 6, "nodeType": "299", "endLine": 31, "endColumn": 8, "suggestions": "318"}, {"ruleId": "319", "severity": 1, "message": "320", "line": 18, "column": 28, "nodeType": "289", "messageId": "321", "endLine": 18, "endColumn": 36}, {"ruleId": "287", "severity": 1, "message": "305", "line": 22, "column": 6, "nodeType": "299", "endLine": 22, "endColumn": 8, "suggestions": "322"}, {"ruleId": "287", "severity": 1, "message": "317", "line": 22, "column": 6, "nodeType": "299", "endLine": 22, "endColumn": 8, "suggestions": "323"}, {"ruleId": "287", "severity": 1, "message": "324", "line": 15, "column": 6, "nodeType": "299", "endLine": 15, "endColumn": 8, "suggestions": "325"}, {"ruleId": "295", "severity": 1, "message": "326", "line": 29, "column": 11, "nodeType": "289", "messageId": "297", "endLine": 29, "endColumn": 13}, {"ruleId": "291", "severity": 1, "message": "292", "line": 59, "column": 38, "nodeType": "293", "messageId": "294", "endLine": 59, "endColumn": 40}, {"ruleId": "291", "severity": 1, "message": "292", "line": 129, "column": 53, "nodeType": "293", "messageId": "294", "endLine": 129, "endColumn": 55}, {"ruleId": "291", "severity": 1, "message": "292", "line": 37, "column": 33, "nodeType": "293", "messageId": "294", "endLine": 37, "endColumn": 35}, {"ruleId": "291", "severity": 1, "message": "292", "line": 26, "column": 47, "nodeType": "293", "messageId": "294", "endLine": 26, "endColumn": 49}, {"ruleId": "291", "severity": 1, "message": "292", "line": 27, "column": 47, "nodeType": "293", "messageId": "294", "endLine": 27, "endColumn": 49}, {"ruleId": "287", "severity": 1, "message": "327", "line": 34, "column": 6, "nodeType": "299", "endLine": 34, "endColumn": 8, "suggestions": "328"}, {"ruleId": "291", "severity": 1, "message": "303", "line": 40, "column": 17, "nodeType": "293", "messageId": "294", "endLine": 40, "endColumn": 19}, {"ruleId": "295", "severity": 1, "message": "304", "line": 17, "column": 24, "nodeType": "289", "messageId": "297", "endLine": 17, "endColumn": 39}, {"ruleId": "287", "severity": 1, "message": "329", "line": 42, "column": 6, "nodeType": "299", "endLine": 42, "endColumn": 8, "suggestions": "330"}, {"ruleId": "291", "severity": 1, "message": "303", "line": 48, "column": 21, "nodeType": "293", "messageId": "294", "endLine": 48, "endColumn": 23}, {"ruleId": "291", "severity": 1, "message": "292", "line": 50, "column": 34, "nodeType": "293", "messageId": "294", "endLine": 50, "endColumn": 36}, {"ruleId": "291", "severity": 1, "message": "303", "line": 54, "column": 24, "nodeType": "293", "messageId": "294", "endLine": 54, "endColumn": 26}, {"ruleId": "291", "severity": 1, "message": "292", "line": 56, "column": 37, "nodeType": "293", "messageId": "294", "endLine": 56, "endColumn": 39}, {"ruleId": "291", "severity": 1, "message": "303", "line": 62, "column": 13, "nodeType": "293", "messageId": "294", "endLine": 62, "endColumn": 15}, {"ruleId": "291", "severity": 1, "message": "303", "line": 62, "column": 35, "nodeType": "293", "messageId": "294", "endLine": 62, "endColumn": 37}, {"ruleId": "291", "severity": 1, "message": "303", "line": 65, "column": 20, "nodeType": "293", "messageId": "294", "endLine": 65, "endColumn": 22}, {"ruleId": "291", "severity": 1, "message": "292", "line": 4, "column": 13, "nodeType": "293", "messageId": "294", "endLine": 4, "endColumn": 15}, {"ruleId": "331", "severity": 1, "message": "332", "line": 11, "column": 1, "nodeType": "333", "endLine": 18, "endColumn": 3}, {"ruleId": "295", "severity": 1, "message": "314", "line": 1, "column": 17, "nodeType": "289", "messageId": "297", "endLine": 1, "endColumn": 26}, {"ruleId": "291", "severity": 1, "message": "292", "line": 90, "column": 34, "nodeType": "293", "messageId": "294", "endLine": 90, "endColumn": 36}, {"ruleId": "291", "severity": 1, "message": "292", "line": 90, "column": 51, "nodeType": "293", "messageId": "294", "endLine": 90, "endColumn": 53}, {"ruleId": "291", "severity": 1, "message": "292", "line": 21, "column": 47, "nodeType": "293", "messageId": "294", "endLine": 21, "endColumn": 49}, {"ruleId": "295", "severity": 1, "message": "314", "line": 1, "column": 17, "nodeType": "289", "messageId": "297", "endLine": 1, "endColumn": 26}, {"ruleId": "295", "severity": 1, "message": "334", "line": 8, "column": 8, "nodeType": "289", "messageId": "297", "endLine": 8, "endColumn": 19}, {"ruleId": "295", "severity": 1, "message": "335", "line": 14, "column": 10, "nodeType": "289", "messageId": "297", "endLine": 14, "endColumn": 15}, {"ruleId": "295", "severity": 1, "message": "336", "line": 2, "column": 55, "nodeType": "289", "messageId": "297", "endLine": 2, "endColumn": 59}, "react-hooks/exhaustive-deps", "React Hook useEffect contains a call to 'setKeyword'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [keywordParam] as a second argument to the useEffect Hook.", "Identifier", ["337"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "unusedVar", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["338"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["339"], "Expected '!==' and instead saw '!='.", "'setSearchParams' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate', 'redirect', and 'userInfo'. Either include them or remove the dependency array.", ["340"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "341", "text": "342"}, "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["343"], "'useEffect' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'loadProduct'. Either include them or remove the dependency array.", ["344"], "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["345"], "no-const-assign", "'redirect' is constant.", "const", ["346"], ["347"], "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["348"], "'id' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'logout'. Either include them or remove the dependency array.", ["349"], "React Hook useEffect has missing dependencies: 'brandParam', 'categoryParam', and 'loadProducts'. Either include them or remove the dependency array.", ["350"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", "'Form' is defined but never used.", {"desc": "351", "fix": "352"}, {"desc": "353", "fix": "354"}, {"desc": "353", "fix": "355"}, {"desc": "356", "fix": "357"}, [447, 447], " rel=\"noreferrer\"", {"desc": "358", "fix": "359"}, {"desc": "360", "fix": "361"}, {"desc": "362", "fix": "363"}, {"desc": "356", "fix": "364"}, {"desc": "362", "fix": "365"}, {"desc": "366", "fix": "367"}, {"desc": "368", "fix": "369"}, {"desc": "370", "fix": "371"}, "Add dependencies array: [keywordParam]", {"range": "372", "text": "373"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "374", "text": "375"}, {"range": "376", "text": "375"}, "Update the dependencies array to be: [navigate, redirect, userInfo]", {"range": "377", "text": "378"}, "Update the dependencies array to be: [loadProducts]", {"range": "379", "text": "380"}, "Update the dependencies array to be: [id, loadProduct]", {"range": "381", "text": "382"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "383", "text": "384"}, {"range": "385", "text": "378"}, {"range": "386", "text": "384"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "387", "text": "388"}, "Update the dependencies array to be: [id, logout]", {"range": "389", "text": "390"}, "Update the dependencies array to be: [brandParam, categoryParam, loadProducts]", {"range": "391", "text": "392"}, [1722, 1722], ", [keywordParam]", [2796, 2798], "[authTokens, refresh]", [3172, 3184], [956, 958], "[navigate, redirect, userInfo]", [782, 784], "[loadProducts]", [1011, 1013], "[id, loadProduct]", [1168, 1170], "[navigate, userInfo]", [833, 835], [858, 860], [491, 493], "[logout, navigate, userInfo]", [1286, 1288], "[id, logout]", [1431, 1433], "[brandParam, categoryParam, loadProducts]"]