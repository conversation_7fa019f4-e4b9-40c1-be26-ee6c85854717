.admin-header {
  background: #fff;
  border-bottom: 1px solid #e9ecef;
  padding: 0;
  margin-left: 280px;
  position: fixed;
  top: 0;
  right: 0;
  left: 280px;
  z-index: 999;
  height: 70px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 70px;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.sidebar-toggle {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  background: #f8f9fa;
  color: #333;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo i {
  font-size: 24px;
}

.logo-text {
  font-size: 24px;
  font-weight: 700;
  color: #333;
}

.header-center {
  flex: 1;
  text-align: center;
}

.page-title {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 400;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 10px;
}

.nav-item-icon {
  position: relative;
}

.nav-icon {
  display: flex !important;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: #666 !important;
  text-decoration: none !important;
  transition: all 0.3s ease;
  position: relative;
}

.nav-icon:hover {
  background: #f8f9fa;
  color: #333 !important;
}

.notification-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-dropdown {
  display: flex !important;
  align-items: center;
  gap: 8px;
  padding: 8px 12px !important;
  border: none !important;
  background: none !important;
  color: #333 !important;
  text-decoration: none !important;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.user-dropdown:hover {
  background: #f8f9fa !important;
}

.user-dropdown::after {
  display: none;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid #e9ecef;
}

.user-name {
  font-weight: 500;
  font-size: 14px;
}

.user-menu {
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  padding: 8px 0;
  min-width: 180px;
}

.user-menu .dropdown-item {
  padding: 10px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #333;
  transition: all 0.3s ease;
}

.user-menu .dropdown-item:hover {
  background: #f8f9fa;
}

.user-menu .dropdown-item i {
  width: 16px;
  text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
  .admin-header {
    margin-left: 0;
    left: 0;
  }
  
  .header-left .logo {
    display: none;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .user-name {
    display: none;
  }
}
