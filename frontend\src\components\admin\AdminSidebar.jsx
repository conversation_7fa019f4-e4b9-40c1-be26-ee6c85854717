import React, { useState } from 'react';
import { Nav, Collapse } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';
import './AdminSidebar.css';

const AdminSidebar = () => {
  const location = useLocation();
  const [dashboardOpen, setDashboardOpen] = useState(true);
  const [elementsOpen, setElementsOpen] = useState(false);
  const [additionalPagesOpen, setAdditionalPagesOpen] = useState(false);

  const isActive = (path) => location.pathname === path;

  return (
    <div className="admin-sidebar">
      <div className="sidebar-header">
        <div className="user-info">
          <div className="user-avatar">
            <img src="/api/placeholder/40/40" alt="Admin" />
          </div>
          <div className="user-details">
            <h6><PERSON></h6>
            <span className="status online">● Online</span>
          </div>
        </div>
      </div>

      <div className="sidebar-content">
        <div className="sidebar-section">
          <h6 className="section-title">General</h6>
          
          <Nav className="flex-column">
            <Nav.Item>
              <Nav.Link 
                as="div" 
                className={`sidebar-link ${dashboardOpen ? 'active' : ''}`}
                onClick={() => setDashboardOpen(!dashboardOpen)}
              >
                <i className="fas fa-tachometer-alt"></i>
                Dashboard
                <i className={`fas fa-chevron-${dashboardOpen ? 'down' : 'right'} ms-auto`}></i>
              </Nav.Link>
              <Collapse in={dashboardOpen}>
                <div className="submenu">
                  <Nav.Link 
                    as={Link} 
                    to="/admin" 
                    className={`submenu-link ${isActive('/admin') ? 'active' : ''}`}
                  >
                    Overview
                  </Nav.Link>
                  <Nav.Link 
                    as={Link} 
                    to="/admin/analytics" 
                    className={`submenu-link ${isActive('/admin/analytics') ? 'active' : ''}`}
                  >
                    Analytics
                  </Nav.Link>
                </div>
              </Collapse>
            </Nav.Item>

            <Nav.Item>
              <Nav.Link 
                as={Link} 
                to="/admin/widgets" 
                className={`sidebar-link ${isActive('/admin/widgets') ? 'active' : ''}`}
              >
                <i className="fas fa-th"></i>
                Widgets
              </Nav.Link>
            </Nav.Item>

            <Nav.Item>
              <Nav.Link 
                as="div" 
                className={`sidebar-link ${elementsOpen ? 'active' : ''}`}
                onClick={() => setElementsOpen(!elementsOpen)}
              >
                <i className="fas fa-cube"></i>
                Elements
                <i className={`fas fa-chevron-${elementsOpen ? 'down' : 'right'} ms-auto`}></i>
              </Nav.Link>
              <Collapse in={elementsOpen}>
                <div className="submenu">
                  <Nav.Link 
                    as={Link} 
                    to="/admin/products" 
                    className={`submenu-link ${isActive('/admin/products') ? 'active' : ''}`}
                  >
                    Products
                  </Nav.Link>
                  <Nav.Link 
                    as={Link} 
                    to="/admin/categories" 
                    className={`submenu-link ${isActive('/admin/categories') ? 'active' : ''}`}
                  >
                    Categories
                  </Nav.Link>
                  <Nav.Link 
                    as={Link} 
                    to="/admin/brands" 
                    className={`submenu-link ${isActive('/admin/brands') ? 'active' : ''}`}
                  >
                    Brands
                  </Nav.Link>
                </div>
              </Collapse>
            </Nav.Item>

            <Nav.Item>
              <Nav.Link 
                as={Link} 
                to="/admin/tables" 
                className={`sidebar-link ${isActive('/admin/tables') ? 'active' : ''}`}
              >
                <i className="fas fa-table"></i>
                Tables
              </Nav.Link>
            </Nav.Item>

            <Nav.Item>
              <Nav.Link 
                as={Link} 
                to="/admin/orders" 
                className={`sidebar-link ${isActive('/admin/orders') ? 'active' : ''}`}
              >
                <i className="fas fa-shopping-cart"></i>
                Orders
              </Nav.Link>
            </Nav.Item>

            <Nav.Item>
              <Nav.Link 
                as={Link} 
                to="/admin/pricing" 
                className={`sidebar-link ${isActive('/admin/pricing') ? 'active' : ''}`}
              >
                <i className="fas fa-dollar-sign"></i>
                Pricing Tables
              </Nav.Link>
            </Nav.Item>

            <Nav.Item>
              <Nav.Link 
                as={Link} 
                to="/admin/contact" 
                className={`sidebar-link ${isActive('/admin/contact') ? 'active' : ''}`}
              >
                <i className="fas fa-envelope"></i>
                Contact
              </Nav.Link>
            </Nav.Item>

            <Nav.Item>
              <Nav.Link 
                as="div" 
                className={`sidebar-link ${additionalPagesOpen ? 'active' : ''}`}
                onClick={() => setAdditionalPagesOpen(!additionalPagesOpen)}
              >
                <i className="fas fa-plus"></i>
                Additional Pages
                <i className={`fas fa-chevron-${additionalPagesOpen ? 'down' : 'right'} ms-auto`}></i>
              </Nav.Link>
              <Collapse in={additionalPagesOpen}>
                <div className="submenu">
                  <Nav.Link 
                    as={Link} 
                    to="/admin/users" 
                    className={`submenu-link ${isActive('/admin/users') ? 'active' : ''}`}
                  >
                    Users
                  </Nav.Link>
                  <Nav.Link 
                    as={Link} 
                    to="/admin/reviews" 
                    className={`submenu-link ${isActive('/admin/reviews') ? 'active' : ''}`}
                  >
                    Reviews
                  </Nav.Link>
                </div>
              </Collapse>
            </Nav.Item>

            <Nav.Item>
              <Nav.Link 
                as={Link} 
                to="/admin/map" 
                className={`sidebar-link ${isActive('/admin/map') ? 'active' : ''}`}
              >
                <i className="fas fa-map"></i>
                Map
              </Nav.Link>
            </Nav.Item>

            <Nav.Item>
              <Nav.Link 
                as={Link} 
                to="/admin/charts" 
                className={`sidebar-link ${isActive('/admin/charts') ? 'active' : ''}`}
              >
                <i className="fas fa-chart-bar"></i>
                Charts
              </Nav.Link>
            </Nav.Item>
          </Nav>
        </div>
      </div>

      <div className="sidebar-footer">
        <div className="weather-widget">
          <i className="fas fa-sun"></i>
          <span>Hot weather</span>
        </div>
      </div>
    </div>
  );
};

export default AdminSidebar;
