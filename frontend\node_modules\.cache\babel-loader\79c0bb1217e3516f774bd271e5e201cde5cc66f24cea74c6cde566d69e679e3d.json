{"ast": null, "code": "import * as React from 'react';\n\n// TODO: check\n\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'NavbarContext';\nexport default context;", "map": {"version": 3, "names": ["React", "context", "createContext", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/NavbarContext.js"], "sourcesContent": ["import * as React from 'react';\n\n// TODO: check\n\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'NavbarContext';\nexport default context;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;;AAEA,MAAMC,OAAO,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACtDD,OAAO,CAACE,WAAW,GAAG,eAAe;AACrC,eAAeF,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}