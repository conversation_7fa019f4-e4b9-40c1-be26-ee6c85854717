{"ast": null, "code": "const _excluded = [\"children\"];\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nimport { useContext, useRef } from 'react';\nimport * as React from 'react';\nimport useCallbackRef from '@restart/hooks/useCallbackRef';\nimport DropdownContext from './DropdownContext';\nimport usePopper from './usePopper';\nimport useClickOutside from './useClickOutside';\nimport mergeOptionsWithPopperConfig from './mergeOptionsWithPopperConfig';\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst noop = () => {};\n\n/**\n * @memberOf Dropdown\n * @param {object}  options\n * @param {boolean} options.flip Automatically adjust the menu `drop` position based on viewport edge detection\n * @param {[number, number]} options.offset Define an offset distance between the Menu and the Toggle\n * @param {boolean} options.show Display the menu manually, ignored in the context of a `Dropdown`\n * @param {boolean} options.usePopper opt in/out of using PopperJS to position menus. When disabled you must position it yourself.\n * @param {string}  options.rootCloseEvent The pointer event to listen for when determining \"clicks outside\" the menu for triggering a close.\n * @param {object}  options.popperConfig Options passed to the [`usePopper`](/api/usePopper) hook.\n */\nexport function useDropdownMenu() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const context = useContext(DropdownContext);\n  const [arrowElement, attachArrowRef] = useCallbackRef();\n  const hasShownRef = useRef(false);\n  const {\n    flip,\n    offset,\n    rootCloseEvent,\n    fixed = false,\n    placement: placementOverride,\n    popperConfig = {},\n    enableEventListeners = true,\n    usePopper: shouldUsePopper = !!context\n  } = options;\n  const show = (context == null ? void 0 : context.show) == null ? !!options.show : context.show;\n  if (show && !hasShownRef.current) {\n    hasShownRef.current = true;\n  }\n  const handleClose = e => {\n    context == null ? void 0 : context.toggle(false, e);\n  };\n  const {\n    placement,\n    setMenu,\n    menuElement,\n    toggleElement\n  } = context || {};\n  const popper = usePopper(toggleElement, menuElement, mergeOptionsWithPopperConfig({\n    placement: placementOverride || placement || 'bottom-start',\n    enabled: shouldUsePopper,\n    enableEvents: enableEventListeners == null ? show : enableEventListeners,\n    offset,\n    flip,\n    fixed,\n    arrowElement,\n    popperConfig\n  }));\n  const menuProps = Object.assign({\n    ref: setMenu || noop,\n    'aria-labelledby': toggleElement == null ? void 0 : toggleElement.id\n  }, popper.attributes.popper, {\n    style: popper.styles.popper\n  });\n  const metadata = {\n    show,\n    placement,\n    hasShown: hasShownRef.current,\n    toggle: context == null ? void 0 : context.toggle,\n    popper: shouldUsePopper ? popper : null,\n    arrowProps: shouldUsePopper ? Object.assign({\n      ref: attachArrowRef\n    }, popper.attributes.arrow, {\n      style: popper.styles.arrow\n    }) : {}\n  };\n  useClickOutside(menuElement, handleClose, {\n    clickTrigger: rootCloseEvent,\n    disabled: !show\n  });\n  return [menuProps, metadata];\n}\nconst defaultProps = {\n  usePopper: true\n};\n/**\n * Also exported as `<Dropdown.Menu>` from `Dropdown`.\n *\n * @displayName DropdownMenu\n * @memberOf Dropdown\n */\nfunction DropdownMenu(_ref) {\n  let {\n      children\n    } = _ref,\n    options = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [props, meta] = useDropdownMenu(options);\n  return /*#__PURE__*/_jsx(_Fragment, {\n    children: children(props, meta)\n  });\n}\nDropdownMenu.displayName = 'DropdownMenu';\nDropdownMenu.defaultProps = defaultProps;\n\n/** @component */\nexport default DropdownMenu;", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "source", "excluded", "target", "sourceKeys", "Object", "keys", "key", "i", "length", "indexOf", "useContext", "useRef", "React", "useCallbackRef", "DropdownContext", "usePopper", "useClickOutside", "mergeOptionsWithPopperConfig", "Fragment", "_Fragment", "jsx", "_jsx", "noop", "useDropdownMenu", "options", "arguments", "undefined", "context", "arrowElement", "attachArrowRef", "hasShownRef", "flip", "offset", "rootCloseEvent", "fixed", "placement", "placementOverride", "popperConfig", "enableEventListeners", "shouldUsePopper", "show", "current", "handleClose", "e", "toggle", "setMenu", "menuElement", "toggleElement", "popper", "enabled", "enableEvents", "menuProps", "assign", "ref", "id", "attributes", "style", "styles", "metadata", "hasShown", "arrowProps", "arrow", "clickTrigger", "disabled", "defaultProps", "DropdownMenu", "_ref", "children", "props", "meta", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/ui/esm/DropdownMenu.js"], "sourcesContent": ["const _excluded = [\"children\"];\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nimport { useContext, useRef } from 'react';\nimport * as React from 'react';\nimport useCallbackRef from '@restart/hooks/useCallbackRef';\nimport DropdownContext from './DropdownContext';\nimport usePopper from './usePopper';\nimport useClickOutside from './useClickOutside';\nimport mergeOptionsWithPopperConfig from './mergeOptionsWithPopperConfig';\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst noop = () => {};\n\n/**\n * @memberOf Dropdown\n * @param {object}  options\n * @param {boolean} options.flip Automatically adjust the menu `drop` position based on viewport edge detection\n * @param {[number, number]} options.offset Define an offset distance between the Menu and the Toggle\n * @param {boolean} options.show Display the menu manually, ignored in the context of a `Dropdown`\n * @param {boolean} options.usePopper opt in/out of using PopperJS to position menus. When disabled you must position it yourself.\n * @param {string}  options.rootCloseEvent The pointer event to listen for when determining \"clicks outside\" the menu for triggering a close.\n * @param {object}  options.popperConfig Options passed to the [`usePopper`](/api/usePopper) hook.\n */\nexport function useDropdownMenu(options = {}) {\n  const context = useContext(DropdownContext);\n  const [arrowElement, attachArrowRef] = useCallbackRef();\n  const hasShownRef = useRef(false);\n  const {\n    flip,\n    offset,\n    rootCloseEvent,\n    fixed = false,\n    placement: placementOverride,\n    popperConfig = {},\n    enableEventListeners = true,\n    usePopper: shouldUsePopper = !!context\n  } = options;\n  const show = (context == null ? void 0 : context.show) == null ? !!options.show : context.show;\n  if (show && !hasShownRef.current) {\n    hasShownRef.current = true;\n  }\n  const handleClose = e => {\n    context == null ? void 0 : context.toggle(false, e);\n  };\n  const {\n    placement,\n    setMenu,\n    menuElement,\n    toggleElement\n  } = context || {};\n  const popper = usePopper(toggleElement, menuElement, mergeOptionsWithPopperConfig({\n    placement: placementOverride || placement || 'bottom-start',\n    enabled: shouldUsePopper,\n    enableEvents: enableEventListeners == null ? show : enableEventListeners,\n    offset,\n    flip,\n    fixed,\n    arrowElement,\n    popperConfig\n  }));\n  const menuProps = Object.assign({\n    ref: setMenu || noop,\n    'aria-labelledby': toggleElement == null ? void 0 : toggleElement.id\n  }, popper.attributes.popper, {\n    style: popper.styles.popper\n  });\n  const metadata = {\n    show,\n    placement,\n    hasShown: hasShownRef.current,\n    toggle: context == null ? void 0 : context.toggle,\n    popper: shouldUsePopper ? popper : null,\n    arrowProps: shouldUsePopper ? Object.assign({\n      ref: attachArrowRef\n    }, popper.attributes.arrow, {\n      style: popper.styles.arrow\n    }) : {}\n  };\n  useClickOutside(menuElement, handleClose, {\n    clickTrigger: rootCloseEvent,\n    disabled: !show\n  });\n  return [menuProps, metadata];\n}\nconst defaultProps = {\n  usePopper: true\n};\n/**\n * Also exported as `<Dropdown.Menu>` from `Dropdown`.\n *\n * @displayName DropdownMenu\n * @memberOf Dropdown\n */\nfunction DropdownMenu(_ref) {\n  let {\n      children\n    } = _ref,\n    options = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [props, meta] = useDropdownMenu(options);\n  return /*#__PURE__*/_jsx(_Fragment, {\n    children: children(props, meta)\n  });\n}\nDropdownMenu.displayName = 'DropdownMenu';\nDropdownMenu.defaultProps = defaultProps;\n\n/** @component */\nexport default DropdownMenu;"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,UAAU,CAAC;AAC9B,SAASC,6BAA6BA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,IAAIC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC;EAAE,IAAIM,GAAG,EAAEC,CAAC;EAAE,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IAAED,GAAG,GAAGH,UAAU,CAACI,CAAC,CAAC;IAAE,IAAIN,QAAQ,CAACQ,OAAO,CAACH,GAAG,CAAC,IAAI,CAAC,EAAE;IAAUJ,MAAM,CAACI,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC;EAAE;EAAE,OAAOJ,MAAM;AAAE;AAClT,SAASQ,UAAU,EAAEC,MAAM,QAAQ,OAAO;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,4BAA4B,MAAM,gCAAgC;AACzE,SAASC,QAAQ,IAAIC,SAAS,QAAQ,mBAAmB;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAAA,EAAe;EAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAjB,MAAA,QAAAiB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAC1C,MAAME,OAAO,GAAGjB,UAAU,CAACI,eAAe,CAAC;EAC3C,MAAM,CAACc,YAAY,EAAEC,cAAc,CAAC,GAAGhB,cAAc,EAAE;EACvD,MAAMiB,WAAW,GAAGnB,MAAM,CAAC,KAAK,CAAC;EACjC,MAAM;IACJoB,IAAI;IACJC,MAAM;IACNC,cAAc;IACdC,KAAK,GAAG,KAAK;IACbC,SAAS,EAAEC,iBAAiB;IAC5BC,YAAY,GAAG,CAAC,CAAC;IACjBC,oBAAoB,GAAG,IAAI;IAC3BvB,SAAS,EAAEwB,eAAe,GAAG,CAAC,CAACZ;EACjC,CAAC,GAAGH,OAAO;EACX,MAAMgB,IAAI,GAAG,CAACb,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACa,IAAI,KAAK,IAAI,GAAG,CAAC,CAAChB,OAAO,CAACgB,IAAI,GAAGb,OAAO,CAACa,IAAI;EAC9F,IAAIA,IAAI,IAAI,CAACV,WAAW,CAACW,OAAO,EAAE;IAChCX,WAAW,CAACW,OAAO,GAAG,IAAI;EAC5B;EACA,MAAMC,WAAW,GAAGC,CAAC,IAAI;IACvBhB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiB,MAAM,CAAC,KAAK,EAAED,CAAC,CAAC;EACrD,CAAC;EACD,MAAM;IACJR,SAAS;IACTU,OAAO;IACPC,WAAW;IACXC;EACF,CAAC,GAAGpB,OAAO,IAAI,CAAC,CAAC;EACjB,MAAMqB,MAAM,GAAGjC,SAAS,CAACgC,aAAa,EAAED,WAAW,EAAE7B,4BAA4B,CAAC;IAChFkB,SAAS,EAAEC,iBAAiB,IAAID,SAAS,IAAI,cAAc;IAC3Dc,OAAO,EAAEV,eAAe;IACxBW,YAAY,EAAEZ,oBAAoB,IAAI,IAAI,GAAGE,IAAI,GAAGF,oBAAoB;IACxEN,MAAM;IACND,IAAI;IACJG,KAAK;IACLN,YAAY;IACZS;EACF,CAAC,CAAC,CAAC;EACH,MAAMc,SAAS,GAAG/C,MAAM,CAACgD,MAAM,CAAC;IAC9BC,GAAG,EAAER,OAAO,IAAIvB,IAAI;IACpB,iBAAiB,EAAEyB,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO;EACpE,CAAC,EAAEN,MAAM,CAACO,UAAU,CAACP,MAAM,EAAE;IAC3BQ,KAAK,EAAER,MAAM,CAACS,MAAM,CAACT;EACvB,CAAC,CAAC;EACF,MAAMU,QAAQ,GAAG;IACflB,IAAI;IACJL,SAAS;IACTwB,QAAQ,EAAE7B,WAAW,CAACW,OAAO;IAC7BG,MAAM,EAAEjB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiB,MAAM;IACjDI,MAAM,EAAET,eAAe,GAAGS,MAAM,GAAG,IAAI;IACvCY,UAAU,EAAErB,eAAe,GAAGnC,MAAM,CAACgD,MAAM,CAAC;MAC1CC,GAAG,EAAExB;IACP,CAAC,EAAEmB,MAAM,CAACO,UAAU,CAACM,KAAK,EAAE;MAC1BL,KAAK,EAAER,MAAM,CAACS,MAAM,CAACI;IACvB,CAAC,CAAC,GAAG,CAAC;EACR,CAAC;EACD7C,eAAe,CAAC8B,WAAW,EAAEJ,WAAW,EAAE;IACxCoB,YAAY,EAAE7B,cAAc;IAC5B8B,QAAQ,EAAE,CAACvB;EACb,CAAC,CAAC;EACF,OAAO,CAACW,SAAS,EAAEO,QAAQ,CAAC;AAC9B;AACA,MAAMM,YAAY,GAAG;EACnBjD,SAAS,EAAE;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAASkD,YAAYA,CAACC,IAAI,EAAE;EAC1B,IAAI;MACAC;IACF,CAAC,GAAGD,IAAI;IACR1C,OAAO,GAAGzB,6BAA6B,CAACmE,IAAI,EAAEpE,SAAS,CAAC;EAC1D,MAAM,CAACsE,KAAK,EAAEC,IAAI,CAAC,GAAG9C,eAAe,CAACC,OAAO,CAAC;EAC9C,OAAO,aAAaH,IAAI,CAACF,SAAS,EAAE;IAClCgD,QAAQ,EAAEA,QAAQ,CAACC,KAAK,EAAEC,IAAI;EAChC,CAAC,CAAC;AACJ;AACAJ,YAAY,CAACK,WAAW,GAAG,cAAc;AACzCL,YAAY,CAACD,YAAY,GAAGA,YAAY;;AAExC;AACA,eAAeC,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}