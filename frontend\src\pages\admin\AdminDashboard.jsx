import React, { useState, useEffect } from 'react';
import { Row, Col, Card } from 'react-bootstrap';
import AdminLayout from '../../components/admin/AdminLayout';
import httpService from '../../services/httpService';
import './AdminDashboard.css';

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalOrders: 0,
    totalProducts: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      // Fetch various stats from your APIs
      const [ordersRes, productsRes] = await Promise.all([
        httpService.get('/api/orders/'),
        httpService.get('/api/products/')
      ]);

      setStats({
        totalUsers: 2500, // Mock data - you can implement user count API
        totalOrders: ordersRes.data.length || 0,
        totalProducts: productsRes.data.length || 0,
        totalRevenue: 123.50 // Mock data - calculate from orders
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    }
  };

  return (
    <AdminLayout>
      <div className="admin-dashboard">
        {/* Stats Cards */}
        <Row className="mb-4">
          <Col lg={3} md={6} className="mb-3">
            <Card className="stat-card stat-card-orange">
              <Card.Body>
                <div className="stat-content">
                  <div className="stat-icon">
                    <i className="fas fa-users"></i>
                  </div>
                  <div className="stat-info">
                    <h3>{stats.totalUsers}</h3>
                    <p>Welcome</p>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-3">
            <Card className="stat-card stat-card-blue">
              <Card.Body>
                <div className="stat-content">
                  <div className="stat-icon">
                    <i className="fas fa-clock"></i>
                  </div>
                  <div className="stat-info">
                    <h3>{stats.totalRevenue}</h3>
                    <p>Average Time</p>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-3">
            <Card className="stat-card stat-card-green">
              <Card.Body>
                <div className="stat-content">
                  <div className="stat-icon">
                    <i className="fas fa-download"></i>
                  </div>
                  <div className="stat-info">
                    <h3>{stats.totalProducts}</h3>
                    <p>Collections</p>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-3">
            <Card className="stat-card stat-card-pink">
              <Card.Body>
                <div className="stat-content">
                  <div className="stat-icon">
                    <i className="fas fa-comments"></i>
                  </div>
                  <div className="stat-info">
                    <h3>{stats.totalOrders}</h3>
                    <p>Comments</p>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Social Media Stats */}
        <Row className="mb-4">
          <Col lg={3} md={6} className="mb-3">
            <Card className="social-card facebook-card">
              <Card.Body>
                <div className="social-content">
                  <div className="social-icon">
                    <i className="fab fa-facebook-f"></i>
                  </div>
                  <div className="social-stats">
                    <div className="social-numbers">
                      <span className="big-number">35k</span>
                      <span className="small-number">128</span>
                    </div>
                    <div className="social-labels">
                      <span>Friends</span>
                      <span>Feeds</span>
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-3">
            <Card className="social-card twitter-card">
              <Card.Body>
                <div className="social-content">
                  <div className="social-icon">
                    <i className="fab fa-twitter"></i>
                  </div>
                  <div className="social-stats">
                    <div className="social-numbers">
                      <span className="big-number">584k</span>
                      <span className="small-number">978</span>
                    </div>
                    <div className="social-labels">
                      <span>Followers</span>
                      <span>Tweets</span>
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-3">
            <Card className="social-card linkedin-card">
              <Card.Body>
                <div className="social-content">
                  <div className="social-icon">
                    <i className="fab fa-linkedin-in"></i>
                  </div>
                  <div className="social-stats">
                    <div className="social-numbers">
                      <span className="big-number">758+</span>
                      <span className="small-number">365</span>
                    </div>
                    <div className="social-labels">
                      <span>Contacts</span>
                      <span>Feeds</span>
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-3">
            <Card className="social-card google-card">
              <Card.Body>
                <div className="social-content">
                  <div className="social-icon">
                    <i className="fab fa-google-plus-g"></i>
                  </div>
                  <div className="social-stats">
                    <div className="social-numbers">
                      <span className="big-number">450</span>
                      <span className="small-number">57</span>
                    </div>
                    <div className="social-labels">
                      <span>Followers</span>
                      <span>Circles</span>
                    </div>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Chart Section */}
        <Row>
          <Col lg={12}>
            <Card className="chart-card">
              <Card.Header>
                <h5>Extra Area Chart</h5>
              </Card.Header>
              <Card.Body>
                <div className="chart-placeholder">
                  <div className="chart-legend">
                    <span className="legend-item">
                      <span className="legend-color" style={{backgroundColor: '#ff9800'}}></span>
                      Dataset 1
                    </span>
                    <span className="legend-item">
                      <span className="legend-color" style={{backgroundColor: '#4caf50'}}></span>
                      Dataset 2
                    </span>
                    <span className="legend-item">
                      <span className="legend-color" style={{backgroundColor: '#f44336'}}></span>
                      Dataset 3
                    </span>
                  </div>
                  <div className="chart-area">
                    <p>Chart will be rendered here</p>
                    <small>You can integrate Chart.js or any other charting library</small>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
