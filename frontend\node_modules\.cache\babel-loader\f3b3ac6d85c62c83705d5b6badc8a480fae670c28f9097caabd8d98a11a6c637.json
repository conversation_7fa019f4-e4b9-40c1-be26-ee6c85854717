{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormCheckLabel = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    htmlFor,\n    ...props\n  } = _ref;\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-check-label');\n  return /*#__PURE__*/_jsx(\"label\", {\n    ...props,\n    ref: ref,\n    htmlFor: htmlFor || controlId,\n    className: classNames(className, bsPrefix)\n  });\n});\nFormCheckLabel.displayName = 'FormCheckLabel';\nexport default FormCheckLabel;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "FormContext", "useBootstrapPrefix", "jsx", "_jsx", "FormCheckLabel", "forwardRef", "_ref", "ref", "bsPrefix", "className", "htmlFor", "props", "controlId", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/FormCheckLabel.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormCheckLabel = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  htmlFor,\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-check-label');\n  return /*#__PURE__*/_jsx(\"label\", {\n    ...props,\n    ref: ref,\n    htmlFor: htmlFor || controlId,\n    className: classNames(className, bsPrefix)\n  });\n});\nFormCheckLabel.displayName = 'FormCheckLabel';\nexport default FormCheckLabel;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAAC,IAAA,EAKlDC,GAAG,KAAK;EAAA,IAL2C;IACpDC,QAAQ;IACRC,SAAS;IACTC,OAAO;IACP,GAAGC;EACL,CAAC,GAAAL,IAAA;EACC,MAAM;IACJM;EACF,CAAC,GAAGb,UAAU,CAACC,WAAW,CAAC;EAC3BQ,QAAQ,GAAGP,kBAAkB,CAACO,QAAQ,EAAE,kBAAkB,CAAC;EAC3D,OAAO,aAAaL,IAAI,CAAC,OAAO,EAAE;IAChC,GAAGQ,KAAK;IACRJ,GAAG,EAAEA,GAAG;IACRG,OAAO,EAAEA,OAAO,IAAIE,SAAS;IAC7BH,SAAS,EAAEZ,UAAU,CAACY,SAAS,EAAED,QAAQ;EAC3C,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,cAAc,CAACS,WAAW,GAAG,gBAAgB;AAC7C,eAAeT,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}