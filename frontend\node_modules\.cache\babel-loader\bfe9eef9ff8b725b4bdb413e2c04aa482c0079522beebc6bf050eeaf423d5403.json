{"ast": null, "code": "import * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'InputGroupContext';\nexport default context;", "map": {"version": 3, "names": ["React", "context", "createContext", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/InputGroupContext.js"], "sourcesContent": ["import * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'InputGroupContext';\nexport default context;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,OAAO,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACtDD,OAAO,CAACE,WAAW,GAAG,mBAAmB;AACzC,eAAeF,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}