{"ast": null, "code": "/**\n * A `removeEventListener` ponyfill\n * \n * @param node the element\n * @param eventName the event name\n * @param handle the handler\n * @param options event options\n */\nfunction removeEventListener(node, eventName, handler, options) {\n  var capture = options && typeof options !== 'boolean' ? options.capture : options;\n  node.removeEventListener(eventName, handler, capture);\n  if (handler.__once) {\n    node.removeEventListener(eventName, handler.__once, capture);\n  }\n}\nexport default removeEventListener;", "map": {"version": 3, "names": ["removeEventListener", "node", "eventName", "handler", "options", "capture", "__once"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/dom-helpers/esm/removeEventListener.js"], "sourcesContent": ["/**\n * A `removeEventListener` ponyfill\n * \n * @param node the element\n * @param eventName the event name\n * @param handle the handler\n * @param options event options\n */\nfunction removeEventListener(node, eventName, handler, options) {\n  var capture = options && typeof options !== 'boolean' ? options.capture : options;\n  node.removeEventListener(eventName, handler, capture);\n\n  if (handler.__once) {\n    node.removeEventListener(eventName, handler.__once, capture);\n  }\n}\n\nexport default removeEventListener;"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAmBA,CAACC,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC9D,IAAIC,OAAO,GAAGD,OAAO,IAAI,OAAOA,OAAO,KAAK,SAAS,GAAGA,OAAO,CAACC,OAAO,GAAGD,OAAO;EACjFH,IAAI,CAACD,mBAAmB,CAACE,SAAS,EAAEC,OAAO,EAAEE,OAAO,CAAC;EAErD,IAAIF,OAAO,CAACG,MAAM,EAAE;IAClBL,IAAI,CAACD,mBAAmB,CAACE,SAAS,EAAEC,OAAO,CAACG,MAAM,EAAED,OAAO,CAAC;EAC9D;AACF;AAEA,eAAeL,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}