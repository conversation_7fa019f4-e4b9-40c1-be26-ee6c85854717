{"ast": null, "code": "import * as React from 'react';\nimport invariant from 'invariant';\nimport { useUncontrolled } from 'uncontrollable';\nimport chainFunction from './createChainedFunction';\nimport { map } from './ElementChildren';\nimport ButtonGroup from './ButtonGroup';\nimport ToggleButton from './ToggleButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  type: 'radio',\n  vertical: false\n};\nconst ToggleButtonGroup = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    children,\n    type,\n    name,\n    value,\n    onChange,\n    ...controlledProps\n  } = useUncontrolled(props, {\n    value: 'onChange'\n  });\n  const getValues = () => value == null ? [] : [].concat(value);\n  const handleToggle = (inputVal, event) => {\n    if (!onChange) {\n      return;\n    }\n    const values = getValues();\n    const isActive = values.indexOf(inputVal) !== -1;\n    if (type === 'radio') {\n      if (!isActive) onChange(inputVal, event);\n      return;\n    }\n    if (isActive) {\n      onChange(values.filter(n => n !== inputVal), event);\n    } else {\n      onChange([...values, inputVal], event);\n    }\n  };\n  !(type !== 'radio' || !!name) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A `name` is required to group the toggle buttons when the `type` ' + 'is set to \"radio\"') : invariant(false) : void 0;\n  return /*#__PURE__*/_jsx(ButtonGroup, {\n    ...controlledProps,\n    ref: ref,\n    children: map(children, child => {\n      const values = getValues();\n      const {\n        value: childVal,\n        onChange: childOnChange\n      } = child.props;\n      const handler = e => handleToggle(childVal, e);\n      return /*#__PURE__*/React.cloneElement(child, {\n        type,\n        name: child.name || name,\n        checked: values.indexOf(childVal) !== -1,\n        onChange: chainFunction(childOnChange, handler)\n      });\n    })\n  });\n});\nToggleButtonGroup.defaultProps = defaultProps;\nexport default Object.assign(ToggleButtonGroup, {\n  Button: ToggleButton\n});", "map": {"version": 3, "names": ["React", "invariant", "useUncontrolled", "chainFunction", "map", "ButtonGroup", "ToggleButton", "jsx", "_jsx", "defaultProps", "type", "vertical", "ToggleButtonGroup", "forwardRef", "props", "ref", "children", "name", "value", "onChange", "controlledProps", "getV<PERSON>ues", "concat", "handleToggle", "inputVal", "event", "values", "isActive", "indexOf", "filter", "n", "process", "env", "NODE_ENV", "child", "childVal", "child<PERSON>n<PERSON><PERSON><PERSON>", "handler", "e", "cloneElement", "checked", "Object", "assign", "<PERSON><PERSON>"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/ToggleButtonGroup.js"], "sourcesContent": ["import * as React from 'react';\nimport invariant from 'invariant';\nimport { useUncontrolled } from 'uncontrollable';\nimport chainFunction from './createChainedFunction';\nimport { map } from './ElementChildren';\nimport ButtonGroup from './ButtonGroup';\nimport ToggleButton from './ToggleButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  type: 'radio',\n  vertical: false\n};\nconst ToggleButtonGroup = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    children,\n    type,\n    name,\n    value,\n    onChange,\n    ...controlledProps\n  } = useUncontrolled(props, {\n    value: 'onChange'\n  });\n  const getValues = () => value == null ? [] : [].concat(value);\n  const handleToggle = (inputVal, event) => {\n    if (!onChange) {\n      return;\n    }\n    const values = getValues();\n    const isActive = values.indexOf(inputVal) !== -1;\n    if (type === 'radio') {\n      if (!isActive) onChange(inputVal, event);\n      return;\n    }\n    if (isActive) {\n      onChange(values.filter(n => n !== inputVal), event);\n    } else {\n      onChange([...values, inputVal], event);\n    }\n  };\n  !(type !== 'radio' || !!name) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A `name` is required to group the toggle buttons when the `type` ' + 'is set to \"radio\"') : invariant(false) : void 0;\n  return /*#__PURE__*/_jsx(ButtonGroup, {\n    ...controlledProps,\n    ref: ref,\n    children: map(children, child => {\n      const values = getValues();\n      const {\n        value: childVal,\n        onChange: childOnChange\n      } = child.props;\n      const handler = e => handleToggle(childVal, e);\n      return /*#__PURE__*/React.cloneElement(child, {\n        type,\n        name: child.name || name,\n        checked: values.indexOf(childVal) !== -1,\n        onChange: chainFunction(childOnChange, handler)\n      });\n    })\n  });\n});\nToggleButtonGroup.defaultProps = defaultProps;\nexport default Object.assign(ToggleButtonGroup, {\n  Button: ToggleButton\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,GAAG,QAAQ,mBAAmB;AACvC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE;AACZ,CAAC;AACD,MAAMC,iBAAiB,GAAG,aAAaZ,KAAK,CAACa,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACtE,MAAM;IACJC,QAAQ;IACRN,IAAI;IACJO,IAAI;IACJC,KAAK;IACLC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAGlB,eAAe,CAACY,KAAK,EAAE;IACzBI,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMG,SAAS,GAAGA,CAAA,KAAMH,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAACI,MAAM,CAACJ,KAAK,CAAC;EAC7D,MAAMK,YAAY,GAAGA,CAACC,QAAQ,EAAEC,KAAK,KAAK;IACxC,IAAI,CAACN,QAAQ,EAAE;MACb;IACF;IACA,MAAMO,MAAM,GAAGL,SAAS,EAAE;IAC1B,MAAMM,QAAQ,GAAGD,MAAM,CAACE,OAAO,CAACJ,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,IAAId,IAAI,KAAK,OAAO,EAAE;MACpB,IAAI,CAACiB,QAAQ,EAAER,QAAQ,CAACK,QAAQ,EAAEC,KAAK,CAAC;MACxC;IACF;IACA,IAAIE,QAAQ,EAAE;MACZR,QAAQ,CAACO,MAAM,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKN,QAAQ,CAAC,EAAEC,KAAK,CAAC;IACrD,CAAC,MAAM;MACLN,QAAQ,CAAC,CAAC,GAAGO,MAAM,EAAEF,QAAQ,CAAC,EAAEC,KAAK,CAAC;IACxC;EACF,CAAC;EACD,EAAEf,IAAI,KAAK,OAAO,IAAI,CAAC,CAACO,IAAI,CAAC,GAAGc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhC,SAAS,CAAC,KAAK,EAAE,mEAAmE,GAAG,mBAAmB,CAAC,GAAGA,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EAC/M,OAAO,aAAaO,IAAI,CAACH,WAAW,EAAE;IACpC,GAAGe,eAAe;IAClBL,GAAG,EAAEA,GAAG;IACRC,QAAQ,EAAEZ,GAAG,CAACY,QAAQ,EAAEkB,KAAK,IAAI;MAC/B,MAAMR,MAAM,GAAGL,SAAS,EAAE;MAC1B,MAAM;QACJH,KAAK,EAAEiB,QAAQ;QACfhB,QAAQ,EAAEiB;MACZ,CAAC,GAAGF,KAAK,CAACpB,KAAK;MACf,MAAMuB,OAAO,GAAGC,CAAC,IAAIf,YAAY,CAACY,QAAQ,EAAEG,CAAC,CAAC;MAC9C,OAAO,aAAatC,KAAK,CAACuC,YAAY,CAACL,KAAK,EAAE;QAC5CxB,IAAI;QACJO,IAAI,EAAEiB,KAAK,CAACjB,IAAI,IAAIA,IAAI;QACxBuB,OAAO,EAAEd,MAAM,CAACE,OAAO,CAACO,QAAQ,CAAC,KAAK,CAAC,CAAC;QACxChB,QAAQ,EAAEhB,aAAa,CAACiC,aAAa,EAAEC,OAAO;MAChD,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFzB,iBAAiB,CAACH,YAAY,GAAGA,YAAY;AAC7C,eAAegC,MAAM,CAACC,MAAM,CAAC9B,iBAAiB,EAAE;EAC9C+B,MAAM,EAAErC;AACV,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}