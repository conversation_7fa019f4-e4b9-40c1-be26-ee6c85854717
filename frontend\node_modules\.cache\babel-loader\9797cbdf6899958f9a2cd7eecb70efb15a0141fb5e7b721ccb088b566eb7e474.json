{"ast": null, "code": "import { useMemo } from 'react';\nvar toFnRef = function toFnRef(ref) {\n  return !ref || typeof ref === 'function' ? ref : function (value) {\n    ref.current = value;\n  };\n};\nexport function mergeRefs(refA, refB) {\n  var a = toFnRef(refA);\n  var b = toFnRef(refB);\n  return function (value) {\n    if (a) a(value);\n    if (b) b(value);\n  };\n}\n/**\n * Create and returns a single callback ref composed from two other Refs.\n *\n * ```tsx\n * const Button = React.forwardRef((props, ref) => {\n *   const [element, attachRef] = useCallbackRef<HTMLButtonElement>();\n *   const mergedRef = useMergedRefs(ref, attachRef);\n *\n *   return <button ref={mergedRef} {...props}/>\n * })\n * ```\n *\n * @param refA A Callback or mutable Ref\n * @param refB A Callback or mutable Ref\n * @category refs\n */\n\nfunction useMergedRefs(refA, refB) {\n  return useMemo(function () {\n    return mergeRefs(refA, refB);\n  }, [refA, refB]);\n}\nexport default useMergedRefs;", "map": {"version": 3, "names": ["useMemo", "toFnRef", "ref", "value", "current", "mergeRefs", "refA", "refB", "a", "b", "useMergedRefs"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useMergedRefs.js"], "sourcesContent": ["import { useMemo } from 'react';\n\nvar toFnRef = function toFnRef(ref) {\n  return !ref || typeof ref === 'function' ? ref : function (value) {\n    ref.current = value;\n  };\n};\n\nexport function mergeRefs(refA, refB) {\n  var a = toFnRef(refA);\n  var b = toFnRef(refB);\n  return function (value) {\n    if (a) a(value);\n    if (b) b(value);\n  };\n}\n/**\n * Create and returns a single callback ref composed from two other Refs.\n *\n * ```tsx\n * const Button = React.forwardRef((props, ref) => {\n *   const [element, attachRef] = useCallbackRef<HTMLButtonElement>();\n *   const mergedRef = useMergedRefs(ref, attachRef);\n *\n *   return <button ref={mergedRef} {...props}/>\n * })\n * ```\n *\n * @param refA A Callback or mutable Ref\n * @param refB A Callback or mutable Ref\n * @category refs\n */\n\nfunction useMergedRefs(refA, refB) {\n  return useMemo(function () {\n    return mergeRefs(refA, refB);\n  }, [refA, refB]);\n}\n\nexport default useMergedRefs;"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAE/B,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;EAClC,OAAO,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,UAAU,GAAGA,GAAG,GAAG,UAAUC,KAAK,EAAE;IAChED,GAAG,CAACE,OAAO,GAAGD,KAAK;EACrB,CAAC;AACH,CAAC;AAED,OAAO,SAASE,SAASA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACpC,IAAIC,CAAC,GAAGP,OAAO,CAACK,IAAI,CAAC;EACrB,IAAIG,CAAC,GAAGR,OAAO,CAACM,IAAI,CAAC;EACrB,OAAO,UAAUJ,KAAK,EAAE;IACtB,IAAIK,CAAC,EAAEA,CAAC,CAACL,KAAK,CAAC;IACf,IAAIM,CAAC,EAAEA,CAAC,CAACN,KAAK,CAAC;EACjB,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASO,aAAaA,CAACJ,IAAI,EAAEC,IAAI,EAAE;EACjC,OAAOP,OAAO,CAAC,YAAY;IACzB,OAAOK,SAAS,CAACC,IAAI,EAAEC,IAAI,CAAC;EAC9B,CAAC,EAAE,CAACD,IAAI,EAAEC,IAAI,CAAC,CAAC;AAClB;AAEA,eAAeG,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}