{"ast": null, "code": "import createWithBsPrefix from './createWithBsPrefix';\nexport default createWithBsPrefix('offcanvas-body');", "map": {"version": 3, "names": ["createWithBsPrefix"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/OffcanvasBody.js"], "sourcesContent": ["import createWithBsPrefix from './createWithBsPrefix';\nexport default createWithBsPrefix('offcanvas-body');"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,sBAAsB;AACrD,eAAeA,kBAAkB,CAAC,gBAAgB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}