{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\categoryCard.jsx\";\nimport React from \"react\";\nimport { Card } from \"react-bootstrap\";\nimport { Link } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CategoryCard(_ref) {\n  let {\n    category\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"my-3 p-3 rounded\",\n    children: [/*#__PURE__*/_jsxDEV(Link, {\n      to: `/search?keyword=&brand=&category=${category.id}`,\n      children: /*#__PURE__*/_jsxDEV(Card.Img, {\n        src: category.image,\n        alt: category.title,\n        style: {\n          objectFit: \"contain\",\n          minHeight: \"4rem\"\n        },\n        onClick: () => {\n          window.scrollTo(0, 0);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: `/search?keyword=&brand=&category=${category.id}`,\n        className: \"text-decoration-none\",\n        onClick: () => {\n          window.scrollTo(0, 0);\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Title, {\n          as: \"div\",\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: category.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = CategoryCard;\nexport default CategoryCard;\nvar _c;\n$RefreshReg$(_c, \"CategoryCard\");", "map": {"version": 3, "names": ["React", "Card", "Link", "jsxDEV", "_jsxDEV", "CategoryCard", "_ref", "category", "className", "children", "to", "id", "Img", "src", "image", "alt", "title", "style", "objectFit", "minHeight", "onClick", "window", "scrollTo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "Title", "as", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/categoryCard.jsx"], "sourcesContent": ["import React from \"react\";\nimport { Card } from \"react-bootstrap\";\nimport { Link } from \"react-router-dom\";\n\nfunction CategoryCard({ category }) {\n  return (\n    <Card className=\"my-3 p-3 rounded\">\n      <Link to={`/search?keyword=&brand=&category=${category.id}`}>\n        <Card.Img\n          src={category.image}\n          alt={category.title}\n          style={{ objectFit: \"contain\", minHeight: \"4rem\" }}\n          onClick={() => {\n            window.scrollTo(0, 0);\n          }}\n        />\n      </Link>\n      <Card.Body>\n        <Link\n          to={`/search?keyword=&brand=&category=${category.id}`}\n          className=\"text-decoration-none\"\n          onClick={() => {\n            window.scrollTo(0, 0);\n          }}\n        >\n          <Card.Title as=\"div\">\n            <strong>{category.title}</strong>\n          </Card.Title>\n        </Link>\n        {/* <Card.Text as=\"p\">{category.description}</Card.Text> */}\n      </Card.Body>\n    </Card>\n  );\n}\n\nexport default CategoryCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,YAAYA,CAAAC,IAAA,EAAe;EAAA,IAAd;IAAEC;EAAS,CAAC,GAAAD,IAAA;EAChC,oBACEF,OAAA,CAACH,IAAI;IAACO,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAChCL,OAAA,CAACF,IAAI;MAACQ,EAAE,EAAG,oCAAmCH,QAAQ,CAACI,EAAG,EAAE;MAAAF,QAAA,eAC1DL,OAAA,CAACH,IAAI,CAACW,GAAG;QACPC,GAAG,EAAEN,QAAQ,CAACO,KAAM;QACpBC,GAAG,EAAER,QAAQ,CAACS,KAAM;QACpBC,KAAK,EAAE;UAAEC,SAAS,EAAE,SAAS;UAAEC,SAAS,EAAE;QAAO,CAAE;QACnDC,OAAO,EAAEA,CAAA,KAAM;UACbC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACvB;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACG,eACPtB,OAAA,CAACH,IAAI,CAAC0B,IAAI;MAAAlB,QAAA,eACRL,OAAA,CAACF,IAAI;QACHQ,EAAE,EAAG,oCAAmCH,QAAQ,CAACI,EAAG,EAAE;QACtDH,SAAS,EAAC,sBAAsB;QAChCY,OAAO,EAAEA,CAAA,KAAM;UACbC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACvB,CAAE;QAAAb,QAAA,eAEFL,OAAA,CAACH,IAAI,CAAC2B,KAAK;UAACC,EAAE,EAAC,KAAK;UAAApB,QAAA,eAClBL,OAAA;YAAAK,QAAA,EAASF,QAAQ,CAACS;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAU;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACtB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAEG;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACP;AAEX;AAACI,EAAA,GA7BQzB,YAAY;AA+BrB,eAAeA,YAAY;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}