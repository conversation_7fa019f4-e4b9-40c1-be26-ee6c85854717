{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext } from 'react';\nimport { Nav } from 'react-bootstrap';\nimport { Link, useLocation } from 'react-router-dom';\nimport UserContext from '../../context/userContext';\nimport './AdminSidebar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminSidebar = () => {\n  _s();\n  const location = useLocation();\n  const {\n    userInfo\n  } = useContext(UserContext);\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-sidebar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-avatar\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/api/placeholder/40/40\",\n            alt: \"Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"John David\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"status online\",\n            children: \"\\u25CF Online\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"section-title\",\n          children: \"General\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav, {\n          className: \"flex-column\",\n          children: [/*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: \"div\",\n              className: `sidebar-link ${dashboardOpen ? 'active' : ''}`,\n              onClick: () => setDashboardOpen(!dashboardOpen),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-tachometer-alt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this), \"Dashboard\", /*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas fa-chevron-${dashboardOpen ? 'down' : 'right'} ms-auto`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n              in: dashboardOpen,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"submenu\",\n                children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin\",\n                  className: `submenu-link ${isActive('/admin') ? 'active' : ''}`,\n                  children: \"Overview\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 44,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin/analytics\",\n                  className: `submenu-link ${isActive('/admin/analytics') ? 'active' : ''}`,\n                  children: \"Analytics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/widgets\",\n              className: `sidebar-link ${isActive('/admin/widgets') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-th\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), \"Widgets\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: \"div\",\n              className: `sidebar-link ${elementsOpen ? 'active' : ''}`,\n              onClick: () => setElementsOpen(!elementsOpen),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-cube\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this), \"Elements\", /*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas fa-chevron-${elementsOpen ? 'down' : 'right'} ms-auto`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n              in: elementsOpen,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"submenu\",\n                children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin/products\",\n                  className: `submenu-link ${isActive('/admin/products') ? 'active' : ''}`,\n                  children: \"Products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin/categories\",\n                  className: `submenu-link ${isActive('/admin/categories') ? 'active' : ''}`,\n                  children: \"Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin/brands\",\n                  className: `submenu-link ${isActive('/admin/brands') ? 'active' : ''}`,\n                  children: \"Brands\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/tables\",\n              className: `sidebar-link ${isActive('/admin/tables') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-table\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), \"Tables\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/orders\",\n              className: `sidebar-link ${isActive('/admin/orders') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-shopping-cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), \"Orders\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/pricing\",\n              className: `sidebar-link ${isActive('/admin/pricing') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-dollar-sign\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), \"Pricing Tables\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/contact\",\n              className: `sidebar-link ${isActive('/admin/contact') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-envelope\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), \"Contact\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: \"div\",\n              className: `sidebar-link ${additionalPagesOpen ? 'active' : ''}`,\n              onClick: () => setAdditionalPagesOpen(!additionalPagesOpen),\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-plus\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), \"Additional Pages\", /*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas fa-chevron-${additionalPagesOpen ? 'down' : 'right'} ms-auto`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n              in: additionalPagesOpen,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"submenu\",\n                children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin/users\",\n                  className: `submenu-link ${isActive('/admin/users') ? 'active' : ''}`,\n                  children: \"Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n                  as: Link,\n                  to: \"/admin/reviews\",\n                  className: `submenu-link ${isActive('/admin/reviews') ? 'active' : ''}`,\n                  children: \"Reviews\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/map\",\n              className: `sidebar-link ${isActive('/admin/map') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-map\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), \"Map\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n            children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n              as: Link,\n              to: \"/admin/charts\",\n              className: `sidebar-link ${isActive('/admin/charts') ? 'active' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-chart-bar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), \"Charts\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"weather-widget\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-sun\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Hot weather\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminSidebar, \"YQyYZC3UyLgsc+2Op1GDSeJ424Q=\", false, function () {\n  return [useLocation];\n});\n_c = AdminSidebar;\nexport default AdminSidebar;\nvar _c;\n$RefreshReg$(_c, \"AdminSidebar\");", "map": {"version": 3, "names": ["React", "useContext", "Nav", "Link", "useLocation", "UserContext", "jsxDEV", "_jsxDEV", "AdminSidebar", "_s", "location", "userInfo", "isActive", "path", "pathname", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON>", "as", "dashboardOpen", "onClick", "setDashboardOpen", "Collapse", "in", "to", "elementsOpen", "setElementsOpen", "additionalPagesOpen", "setAdditionalPagesOpen", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/admin/AdminSidebar.jsx"], "sourcesContent": ["import React, { useContext } from 'react';\nimport { Nav } from 'react-bootstrap';\nimport { Link, useLocation } from 'react-router-dom';\nimport UserContext from '../../context/userContext';\nimport './AdminSidebar.css';\n\nconst AdminSidebar = () => {\n  const location = useLocation();\n  const { userInfo } = useContext(UserContext);\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <div className=\"admin-sidebar\">\n      <div className=\"sidebar-header\">\n        <div className=\"user-info\">\n          <div className=\"user-avatar\">\n            <img src=\"/api/placeholder/40/40\" alt=\"Admin\" />\n          </div>\n          <div className=\"user-details\">\n            <h6><PERSON> David</h6>\n            <span className=\"status online\">● Online</span>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"sidebar-content\">\n        <div className=\"sidebar-section\">\n          <h6 className=\"section-title\">General</h6>\n          \n          <Nav className=\"flex-column\">\n            <Nav.Item>\n              <Nav.Link \n                as=\"div\" \n                className={`sidebar-link ${dashboardOpen ? 'active' : ''}`}\n                onClick={() => setDashboardOpen(!dashboardOpen)}\n              >\n                <i className=\"fas fa-tachometer-alt\"></i>\n                Dashboard\n                <i className={`fas fa-chevron-${dashboardOpen ? 'down' : 'right'} ms-auto`}></i>\n              </Nav.Link>\n              <Collapse in={dashboardOpen}>\n                <div className=\"submenu\">\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin\" \n                    className={`submenu-link ${isActive('/admin') ? 'active' : ''}`}\n                  >\n                    Overview\n                  </Nav.Link>\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin/analytics\" \n                    className={`submenu-link ${isActive('/admin/analytics') ? 'active' : ''}`}\n                  >\n                    Analytics\n                  </Nav.Link>\n                </div>\n              </Collapse>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/widgets\" \n                className={`sidebar-link ${isActive('/admin/widgets') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-th\"></i>\n                Widgets\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as=\"div\" \n                className={`sidebar-link ${elementsOpen ? 'active' : ''}`}\n                onClick={() => setElementsOpen(!elementsOpen)}\n              >\n                <i className=\"fas fa-cube\"></i>\n                Elements\n                <i className={`fas fa-chevron-${elementsOpen ? 'down' : 'right'} ms-auto`}></i>\n              </Nav.Link>\n              <Collapse in={elementsOpen}>\n                <div className=\"submenu\">\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin/products\" \n                    className={`submenu-link ${isActive('/admin/products') ? 'active' : ''}`}\n                  >\n                    Products\n                  </Nav.Link>\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin/categories\" \n                    className={`submenu-link ${isActive('/admin/categories') ? 'active' : ''}`}\n                  >\n                    Categories\n                  </Nav.Link>\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin/brands\" \n                    className={`submenu-link ${isActive('/admin/brands') ? 'active' : ''}`}\n                  >\n                    Brands\n                  </Nav.Link>\n                </div>\n              </Collapse>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/tables\" \n                className={`sidebar-link ${isActive('/admin/tables') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-table\"></i>\n                Tables\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/orders\" \n                className={`sidebar-link ${isActive('/admin/orders') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-shopping-cart\"></i>\n                Orders\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/pricing\" \n                className={`sidebar-link ${isActive('/admin/pricing') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-dollar-sign\"></i>\n                Pricing Tables\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/contact\" \n                className={`sidebar-link ${isActive('/admin/contact') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-envelope\"></i>\n                Contact\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as=\"div\" \n                className={`sidebar-link ${additionalPagesOpen ? 'active' : ''}`}\n                onClick={() => setAdditionalPagesOpen(!additionalPagesOpen)}\n              >\n                <i className=\"fas fa-plus\"></i>\n                Additional Pages\n                <i className={`fas fa-chevron-${additionalPagesOpen ? 'down' : 'right'} ms-auto`}></i>\n              </Nav.Link>\n              <Collapse in={additionalPagesOpen}>\n                <div className=\"submenu\">\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin/users\" \n                    className={`submenu-link ${isActive('/admin/users') ? 'active' : ''}`}\n                  >\n                    Users\n                  </Nav.Link>\n                  <Nav.Link \n                    as={Link} \n                    to=\"/admin/reviews\" \n                    className={`submenu-link ${isActive('/admin/reviews') ? 'active' : ''}`}\n                  >\n                    Reviews\n                  </Nav.Link>\n                </div>\n              </Collapse>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/map\" \n                className={`sidebar-link ${isActive('/admin/map') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-map\"></i>\n                Map\n              </Nav.Link>\n            </Nav.Item>\n\n            <Nav.Item>\n              <Nav.Link \n                as={Link} \n                to=\"/admin/charts\" \n                className={`sidebar-link ${isActive('/admin/charts') ? 'active' : ''}`}\n              >\n                <i className=\"fas fa-chart-bar\"></i>\n                Charts\n              </Nav.Link>\n            </Nav.Item>\n          </Nav>\n        </div>\n      </div>\n\n      <div className=\"sidebar-footer\">\n        <div className=\"weather-widget\">\n          <i className=\"fas fa-sun\"></i>\n          <span>Hot weather</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGN,WAAW,EAAE;EAC9B,MAAM;IAAEO;EAAS,CAAC,GAAGV,UAAU,CAACI,WAAW,CAAC;EAE5C,MAAMO,QAAQ,GAAIC,IAAI,IAAKH,QAAQ,CAACI,QAAQ,KAAKD,IAAI;EAErD,oBACEN,OAAA;IAAKQ,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BT,OAAA;MAAKQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BT,OAAA;QAAKQ,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBT,OAAA;UAAKQ,SAAS,EAAC,aAAa;UAAAC,QAAA,eAC1BT,OAAA;YAAKU,GAAG,EAAC,wBAAwB;YAACC,GAAG,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAG;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAC5C,eACNf,OAAA;UAAKQ,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BT,OAAA;YAAAS,QAAA,EAAI;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eACnBf,OAAA;YAAMQ,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAO;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAC3C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF,eAENf,OAAA;MAAKQ,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BT,OAAA;QAAKQ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BT,OAAA;UAAIQ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK,eAE1Cf,OAAA,CAACL,GAAG;UAACa,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BT,OAAA,CAACL,GAAG,CAACqB,IAAI;YAAAP,QAAA,gBACPT,OAAA,CAACL,GAAG,CAACC,IAAI;cACPqB,EAAE,EAAC,KAAK;cACRT,SAAS,EAAG,gBAAeU,aAAa,GAAG,QAAQ,GAAG,EAAG,EAAE;cAC3DC,OAAO,EAAEA,CAAA,KAAMC,gBAAgB,CAAC,CAACF,aAAa,CAAE;cAAAT,QAAA,gBAEhDT,OAAA;gBAAGQ,SAAS,EAAC;cAAuB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,aAEzC,eAAAf,OAAA;gBAAGQ,SAAS,EAAG,kBAAiBU,aAAa,GAAG,MAAM,GAAG,OAAQ;cAAU;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACvE,eACXf,OAAA,CAACqB,QAAQ;cAACC,EAAE,EAAEJ,aAAc;cAAAT,QAAA,eAC1BT,OAAA;gBAAKQ,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtBT,OAAA,CAACL,GAAG,CAACC,IAAI;kBACPqB,EAAE,EAAErB,IAAK;kBACT2B,EAAE,EAAC,QAAQ;kBACXf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EACjE;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW,eACXf,OAAA,CAACL,GAAG,CAACC,IAAI;kBACPqB,EAAE,EAAErB,IAAK;kBACT2B,EAAE,EAAC,kBAAkB;kBACrBf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,kBAAkB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EAC3E;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXf,OAAA,CAACL,GAAG,CAACqB,IAAI;YAAAP,QAAA,eACPT,OAAA,CAACL,GAAG,CAACC,IAAI;cACPqB,EAAE,EAAErB,IAAK;cACT2B,EAAE,EAAC,gBAAgB;cACnBf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,gBAAgB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAExET,OAAA;gBAAGQ,SAAS,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,WAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXf,OAAA,CAACL,GAAG,CAACqB,IAAI;YAAAP,QAAA,gBACPT,OAAA,CAACL,GAAG,CAACC,IAAI;cACPqB,EAAE,EAAC,KAAK;cACRT,SAAS,EAAG,gBAAegB,YAAY,GAAG,QAAQ,GAAG,EAAG,EAAE;cAC1DL,OAAO,EAAEA,CAAA,KAAMM,eAAe,CAAC,CAACD,YAAY,CAAE;cAAAf,QAAA,gBAE9CT,OAAA;gBAAGQ,SAAS,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,YAE/B,eAAAf,OAAA;gBAAGQ,SAAS,EAAG,kBAAiBgB,YAAY,GAAG,MAAM,GAAG,OAAQ;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACtE,eACXf,OAAA,CAACqB,QAAQ;cAACC,EAAE,EAAEE,YAAa;cAAAf,QAAA,eACzBT,OAAA;gBAAKQ,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtBT,OAAA,CAACL,GAAG,CAACC,IAAI;kBACPqB,EAAE,EAAErB,IAAK;kBACT2B,EAAE,EAAC,iBAAiB;kBACpBf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,iBAAiB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EAC1E;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW,eACXf,OAAA,CAACL,GAAG,CAACC,IAAI;kBACPqB,EAAE,EAAErB,IAAK;kBACT2B,EAAE,EAAC,mBAAmB;kBACtBf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,mBAAmB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EAC5E;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW,eACXf,OAAA,CAACL,GAAG,CAACC,IAAI;kBACPqB,EAAE,EAAErB,IAAK;kBACT2B,EAAE,EAAC,eAAe;kBAClBf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EACxE;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXf,OAAA,CAACL,GAAG,CAACqB,IAAI;YAAAP,QAAA,eACPT,OAAA,CAACL,GAAG,CAACC,IAAI;cACPqB,EAAE,EAAErB,IAAK;cACT2B,EAAE,EAAC,eAAe;cAClBf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAEvET,OAAA;gBAAGQ,SAAS,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,UAElC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXf,OAAA,CAACL,GAAG,CAACqB,IAAI;YAAAP,QAAA,eACPT,OAAA,CAACL,GAAG,CAACC,IAAI;cACPqB,EAAE,EAAErB,IAAK;cACT2B,EAAE,EAAC,eAAe;cAClBf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAEvET,OAAA;gBAAGQ,SAAS,EAAC;cAAsB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,UAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXf,OAAA,CAACL,GAAG,CAACqB,IAAI;YAAAP,QAAA,eACPT,OAAA,CAACL,GAAG,CAACC,IAAI;cACPqB,EAAE,EAAErB,IAAK;cACT2B,EAAE,EAAC,gBAAgB;cACnBf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,gBAAgB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAExET,OAAA;gBAAGQ,SAAS,EAAC;cAAoB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,kBAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXf,OAAA,CAACL,GAAG,CAACqB,IAAI;YAAAP,QAAA,eACPT,OAAA,CAACL,GAAG,CAACC,IAAI;cACPqB,EAAE,EAAErB,IAAK;cACT2B,EAAE,EAAC,gBAAgB;cACnBf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,gBAAgB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAExET,OAAA;gBAAGQ,SAAS,EAAC;cAAiB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,WAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXf,OAAA,CAACL,GAAG,CAACqB,IAAI;YAAAP,QAAA,gBACPT,OAAA,CAACL,GAAG,CAACC,IAAI;cACPqB,EAAE,EAAC,KAAK;cACRT,SAAS,EAAG,gBAAekB,mBAAmB,GAAG,QAAQ,GAAG,EAAG,EAAE;cACjEP,OAAO,EAAEA,CAAA,KAAMQ,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;cAAAjB,QAAA,gBAE5DT,OAAA;gBAAGQ,SAAS,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,oBAE/B,eAAAf,OAAA;gBAAGQ,SAAS,EAAG,kBAAiBkB,mBAAmB,GAAG,MAAM,GAAG,OAAQ;cAAU;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAC7E,eACXf,OAAA,CAACqB,QAAQ;cAACC,EAAE,EAAEI,mBAAoB;cAAAjB,QAAA,eAChCT,OAAA;gBAAKQ,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtBT,OAAA,CAACL,GAAG,CAACC,IAAI;kBACPqB,EAAE,EAAErB,IAAK;kBACT2B,EAAE,EAAC,cAAc;kBACjBf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,cAAc,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EACvE;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW,eACXf,OAAA,CAACL,GAAG,CAACC,IAAI;kBACPqB,EAAE,EAAErB,IAAK;kBACT2B,EAAE,EAAC,gBAAgB;kBACnBf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,gBAAgB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;kBAAAI,QAAA,EACzE;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAW;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACP;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXf,OAAA,CAACL,GAAG,CAACqB,IAAI;YAAAP,QAAA,eACPT,OAAA,CAACL,GAAG,CAACC,IAAI;cACPqB,EAAE,EAAErB,IAAK;cACT2B,EAAE,EAAC,YAAY;cACff,SAAS,EAAG,gBAAeH,QAAQ,CAAC,YAAY,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAEpET,OAAA;gBAAGQ,SAAS,EAAC;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,OAEhC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF,eAEXf,OAAA,CAACL,GAAG,CAACqB,IAAI;YAAAP,QAAA,eACPT,OAAA,CAACL,GAAG,CAACC,IAAI;cACPqB,EAAE,EAAErB,IAAK;cACT2B,EAAE,EAAC,eAAe;cAClBf,SAAS,EAAG,gBAAeH,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAI,QAAA,gBAEvET,OAAA;gBAAGQ,SAAS,EAAC;cAAkB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,UAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF,eAENf,OAAA;MAAKQ,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BT,OAAA;QAAKQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BT,OAAA;UAAGQ,SAAS,EAAC;QAAY;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK,eAC9Bf,OAAA;UAAAS,QAAA,EAAM;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAO;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACpB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACF;AAEV,CAAC;AAACb,EAAA,CAlNID,YAAY;EAAA,QACCJ,WAAW;AAAA;AAAA+B,EAAA,GADxB3B,YAAY;AAoNlB,eAAeA,YAAY;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}