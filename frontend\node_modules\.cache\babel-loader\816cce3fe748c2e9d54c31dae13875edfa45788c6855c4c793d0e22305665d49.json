{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext } from 'react';\nimport { Nav } from 'react-bootstrap';\nimport { Link, useLocation } from 'react-router-dom';\nimport UserContext from '../../context/userContext';\nimport './AdminSidebar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminSidebar = () => {\n  _s();\n  var _userInfo$username;\n  const location = useLocation();\n  const {\n    userInfo\n  } = useContext(UserContext);\n  const isActive = path => location.pathname === path;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-sidebar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-avatar\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-user-shield\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"admin-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: (userInfo === null || userInfo === void 0 ? void 0 : (_userInfo$username = userInfo.username) === null || _userInfo$username === void 0 ? void 0 : _userInfo$username.toUpperCase()) || 'ADMIN'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            children: \"Administrator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Nav, {\n      className: \"flex-column sidebar-nav\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"nav-section-title\",\n          children: \"MANAGEMENT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n          children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/admin\",\n            className: `sidebar-link ${isActive('/admin') ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-tachometer-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), \"Dashboard\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n          children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/admin/products\",\n            className: `sidebar-link ${isActive('/admin/products') ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-box\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), \"Products\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n          children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/admin/categories\",\n            className: `sidebar-link ${isActive('/admin/categories') ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-tags\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), \"Categories\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n          children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/admin/brands\",\n            className: `sidebar-link ${isActive('/admin/brands') ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-trademark\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), \"Brands\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n          children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/admin/orders\",\n            className: `sidebar-link ${isActive('/admin/orders') ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-shopping-cart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), \"Orders\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n          children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/admin/users\",\n            className: `sidebar-link ${isActive('/admin/users') ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), \"Users\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n          children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/admin/reviews\",\n            className: `sidebar-link ${isActive('/admin/reviews') ? 'active' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-star\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), \"Reviews\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminSidebar, \"YQyYZC3UyLgsc+2Op1GDSeJ424Q=\", false, function () {\n  return [useLocation];\n});\n_c = AdminSidebar;\nexport default AdminSidebar;\nvar _c;\n$RefreshReg$(_c, \"AdminSidebar\");", "map": {"version": 3, "names": ["React", "useContext", "Nav", "Link", "useLocation", "UserContext", "jsxDEV", "_jsxDEV", "AdminSidebar", "_s", "_userInfo$username", "location", "userInfo", "isActive", "path", "pathname", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "username", "toUpperCase", "<PERSON><PERSON>", "as", "to", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/admin/AdminSidebar.jsx"], "sourcesContent": ["import React, { useContext } from 'react';\nimport { Nav } from 'react-bootstrap';\nimport { Link, useLocation } from 'react-router-dom';\nimport UserContext from '../../context/userContext';\nimport './AdminSidebar.css';\n\nconst AdminSidebar = () => {\n  const location = useLocation();\n  const { userInfo } = useContext(UserContext);\n\n  const isActive = (path) => location.pathname === path;\n\n  return (\n    <div className=\"admin-sidebar\">\n      <div className=\"sidebar-header\">\n        <div className=\"admin-info\">\n          <div className=\"admin-avatar\">\n            <i className=\"fas fa-user-shield\"></i>\n          </div>\n          <div className=\"admin-details\">\n            <h6>{userInfo?.username?.toUpperCase() || 'ADMIN'}</h6>\n            <small>Administrator</small>\n          </div>\n        </div>\n      </div>\n\n      <Nav className=\"flex-column sidebar-nav\">\n        <div className=\"nav-section\">\n          <small className=\"nav-section-title\">MANAGEMENT</small>\n          \n          <Nav.Item>\n            <Nav.Link \n              as={Link} \n              to=\"/admin\" \n              className={`sidebar-link ${isActive('/admin') ? 'active' : ''}`}\n            >\n              <i className=\"fas fa-tachometer-alt\"></i>\n              Dashboard\n            </Nav.Link>\n          </Nav.Item>\n\n          <Nav.Item>\n            <Nav.Link \n              as={Link} \n              to=\"/admin/products\" \n              className={`sidebar-link ${isActive('/admin/products') ? 'active' : ''}`}\n            >\n              <i className=\"fas fa-box\"></i>\n              Products\n            </Nav.Link>\n          </Nav.Item>\n\n          <Nav.Item>\n            <Nav.Link \n              as={Link} \n              to=\"/admin/categories\" \n              className={`sidebar-link ${isActive('/admin/categories') ? 'active' : ''}`}\n            >\n              <i className=\"fas fa-tags\"></i>\n              Categories\n            </Nav.Link>\n          </Nav.Item>\n\n          <Nav.Item>\n            <Nav.Link \n              as={Link} \n              to=\"/admin/brands\" \n              className={`sidebar-link ${isActive('/admin/brands') ? 'active' : ''}`}\n            >\n              <i className=\"fas fa-trademark\"></i>\n              Brands\n            </Nav.Link>\n          </Nav.Item>\n\n          <Nav.Item>\n            <Nav.Link \n              as={Link} \n              to=\"/admin/orders\" \n              className={`sidebar-link ${isActive('/admin/orders') ? 'active' : ''}`}\n            >\n              <i className=\"fas fa-shopping-cart\"></i>\n              Orders\n            </Nav.Link>\n          </Nav.Item>\n\n          <Nav.Item>\n            <Nav.Link \n              as={Link} \n              to=\"/admin/users\" \n              className={`sidebar-link ${isActive('/admin/users') ? 'active' : ''}`}\n            >\n              <i className=\"fas fa-users\"></i>\n              Users\n            </Nav.Link>\n          </Nav.Item>\n\n          <Nav.Item>\n            <Nav.Link \n              as={Link} \n              to=\"/admin/reviews\" \n              className={`sidebar-link ${isActive('/admin/reviews') ? 'active' : ''}`}\n            >\n              <i className=\"fas fa-star\"></i>\n              Reviews\n            </Nav.Link>\n          </Nav.Item>\n        </div>\n      </Nav>\n    </div>\n  );\n};\n\nexport default AdminSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,kBAAA;EACzB,MAAMC,QAAQ,GAAGP,WAAW,EAAE;EAC9B,MAAM;IAAEQ;EAAS,CAAC,GAAGX,UAAU,CAACI,WAAW,CAAC;EAE5C,MAAMQ,QAAQ,GAAIC,IAAI,IAAKH,QAAQ,CAACI,QAAQ,KAAKD,IAAI;EAErD,oBACEP,OAAA;IAAKS,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BV,OAAA;MAAKS,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BV,OAAA;QAAKS,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBV,OAAA;UAAKS,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BV,OAAA;YAAGS,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAK;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAClC,eACNd,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BV,OAAA;YAAAU,QAAA,EAAK,CAAAL,QAAQ,aAARA,QAAQ,wBAAAF,kBAAA,GAARE,QAAQ,CAAEU,QAAQ,cAAAZ,kBAAA,uBAAlBA,kBAAA,CAAoBa,WAAW,EAAE,KAAI;UAAO;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAM,eACvDd,OAAA;YAAAU,QAAA,EAAO;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAQ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACxB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF,eAENd,OAAA,CAACL,GAAG;MAACc,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCV,OAAA;QAAKS,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BV,OAAA;UAAOS,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAQ,eAEvDd,OAAA,CAACL,GAAG,CAACsB,IAAI;UAAAP,QAAA,eACPV,OAAA,CAACL,GAAG,CAACC,IAAI;YACPsB,EAAE,EAAEtB,IAAK;YACTuB,EAAE,EAAC,QAAQ;YACXV,SAAS,EAAG,gBAAeH,QAAQ,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;YAAAI,QAAA,gBAEhEV,OAAA;cAAGS,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAK,aAE3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAW;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACF,eAEXd,OAAA,CAACL,GAAG,CAACsB,IAAI;UAAAP,QAAA,eACPV,OAAA,CAACL,GAAG,CAACC,IAAI;YACPsB,EAAE,EAAEtB,IAAK;YACTuB,EAAE,EAAC,iBAAiB;YACpBV,SAAS,EAAG,gBAAeH,QAAQ,CAAC,iBAAiB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;YAAAI,QAAA,gBAEzEV,OAAA;cAAGS,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAK,YAEhC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAW;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACF,eAEXd,OAAA,CAACL,GAAG,CAACsB,IAAI;UAAAP,QAAA,eACPV,OAAA,CAACL,GAAG,CAACC,IAAI;YACPsB,EAAE,EAAEtB,IAAK;YACTuB,EAAE,EAAC,mBAAmB;YACtBV,SAAS,EAAG,gBAAeH,QAAQ,CAAC,mBAAmB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;YAAAI,QAAA,gBAE3EV,OAAA;cAAGS,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAK,cAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAW;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACF,eAEXd,OAAA,CAACL,GAAG,CAACsB,IAAI;UAAAP,QAAA,eACPV,OAAA,CAACL,GAAG,CAACC,IAAI;YACPsB,EAAE,EAAEtB,IAAK;YACTuB,EAAE,EAAC,eAAe;YAClBV,SAAS,EAAG,gBAAeH,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;YAAAI,QAAA,gBAEvEV,OAAA;cAAGS,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAK,UAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAW;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACF,eAEXd,OAAA,CAACL,GAAG,CAACsB,IAAI;UAAAP,QAAA,eACPV,OAAA,CAACL,GAAG,CAACC,IAAI;YACPsB,EAAE,EAAEtB,IAAK;YACTuB,EAAE,EAAC,eAAe;YAClBV,SAAS,EAAG,gBAAeH,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;YAAAI,QAAA,gBAEvEV,OAAA;cAAGS,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAK,UAE1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAW;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACF,eAEXd,OAAA,CAACL,GAAG,CAACsB,IAAI;UAAAP,QAAA,eACPV,OAAA,CAACL,GAAG,CAACC,IAAI;YACPsB,EAAE,EAAEtB,IAAK;YACTuB,EAAE,EAAC,cAAc;YACjBV,SAAS,EAAG,gBAAeH,QAAQ,CAAC,cAAc,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;YAAAI,QAAA,gBAEtEV,OAAA;cAAGS,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAK,SAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAW;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACF,eAEXd,OAAA,CAACL,GAAG,CAACsB,IAAI;UAAAP,QAAA,eACPV,OAAA,CAACL,GAAG,CAACC,IAAI;YACPsB,EAAE,EAAEtB,IAAK;YACTuB,EAAE,EAAC,gBAAgB;YACnBV,SAAS,EAAG,gBAAeH,QAAQ,CAAC,gBAAgB,CAAC,GAAG,QAAQ,GAAG,EAAG,EAAE;YAAAI,QAAA,gBAExEV,OAAA;cAAGS,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAK,WAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAW;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACF;AAEV,CAAC;AAACZ,EAAA,CAxGID,YAAY;EAAA,QACCJ,WAAW;AAAA;AAAAuB,EAAA,GADxBnB,YAAY;AA0GlB,eAAeA,YAAY;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}