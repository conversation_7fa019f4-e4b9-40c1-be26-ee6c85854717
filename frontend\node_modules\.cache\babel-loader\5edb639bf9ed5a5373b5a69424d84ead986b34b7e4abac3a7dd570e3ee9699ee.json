{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Button from './Button';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst noop = () => undefined;\nconst ToggleButton = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    name,\n    className,\n    checked,\n    type,\n    onChange,\n    value,\n    disabled,\n    id,\n    inputRef,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'btn-check');\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [/*#__PURE__*/_jsx(\"input\", {\n      className: bsPrefix,\n      name: name,\n      type: type,\n      value: value,\n      ref: inputRef,\n      autoComplete: \"off\",\n      checked: !!checked,\n      disabled: !!disabled,\n      onChange: onChange || noop,\n      id: id\n    }), /*#__PURE__*/_jsx(Button, {\n      ...props,\n      ref: ref,\n      className: classNames(className, disabled && 'disabled'),\n      type: undefined,\n      role: undefined,\n      as: \"label\",\n      htmlFor: id\n    })]\n  });\n});\nToggleButton.displayName = 'ToggleButton';\nexport default ToggleButton;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "<PERSON><PERSON>", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "noop", "undefined", "ToggleButton", "forwardRef", "_ref", "ref", "bsPrefix", "name", "className", "checked", "type", "onChange", "value", "disabled", "id", "inputRef", "props", "children", "autoComplete", "role", "as", "htmlFor", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/ToggleButton.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Button from './Button';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst noop = () => undefined;\nconst ToggleButton = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  name,\n  className,\n  checked,\n  type,\n  onChange,\n  value,\n  disabled,\n  id,\n  inputRef,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'btn-check');\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [/*#__PURE__*/_jsx(\"input\", {\n      className: bsPrefix,\n      name: name,\n      type: type,\n      value: value,\n      ref: inputRef,\n      autoComplete: \"off\",\n      checked: !!checked,\n      disabled: !!disabled,\n      onChange: onChange || noop,\n      id: id\n    }), /*#__PURE__*/_jsx(Button, {\n      ...props,\n      ref: ref,\n      className: classNames(className, disabled && 'disabled'),\n      type: undefined,\n      role: undefined,\n      as: \"label\",\n      htmlFor: id\n    })]\n  });\n});\nToggleButton.displayName = 'ToggleButton';\nexport default ToggleButton;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQ,IAAIC,SAAS,QAAQ,mBAAmB;AACzD,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,IAAI,GAAGA,CAAA,KAAMC,SAAS;AAC5B,MAAMC,YAAY,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,CAAAC,IAAA,EAYhDC,GAAG,KAAK;EAAA,IAZyC;IAClDC,QAAQ;IACRC,IAAI;IACJC,SAAS;IACTC,OAAO;IACPC,IAAI;IACJC,QAAQ;IACRC,KAAK;IACLC,QAAQ;IACRC,EAAE;IACFC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAAZ,IAAA;EACCE,QAAQ,GAAGd,kBAAkB,CAACc,QAAQ,EAAE,WAAW,CAAC;EACpD,OAAO,aAAaP,KAAK,CAACF,SAAS,EAAE;IACnCoB,QAAQ,EAAE,CAAC,aAAatB,IAAI,CAAC,OAAO,EAAE;MACpCa,SAAS,EAAEF,QAAQ;MACnBC,IAAI,EAAEA,IAAI;MACVG,IAAI,EAAEA,IAAI;MACVE,KAAK,EAAEA,KAAK;MACZP,GAAG,EAAEU,QAAQ;MACbG,YAAY,EAAE,KAAK;MACnBT,OAAO,EAAE,CAAC,CAACA,OAAO;MAClBI,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBF,QAAQ,EAAEA,QAAQ,IAAIX,IAAI;MAC1Bc,EAAE,EAAEA;IACN,CAAC,CAAC,EAAE,aAAanB,IAAI,CAACF,MAAM,EAAE;MAC5B,GAAGuB,KAAK;MACRX,GAAG,EAAEA,GAAG;MACRG,SAAS,EAAElB,UAAU,CAACkB,SAAS,EAAEK,QAAQ,IAAI,UAAU,CAAC;MACxDH,IAAI,EAAET,SAAS;MACfkB,IAAI,EAAElB,SAAS;MACfmB,EAAE,EAAE,OAAO;MACXC,OAAO,EAAEP;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFZ,YAAY,CAACoB,WAAW,GAAG,cAAc;AACzC,eAAepB,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}