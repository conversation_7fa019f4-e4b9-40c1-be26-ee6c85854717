{"ast": null, "code": "import classNames from 'classnames';\nimport camelize from 'dom-helpers/camelize';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst pascalCase = str => str[0].toUpperCase() + camelize(str).slice(1);\n// TODO: emstricten & fix the typing here! `createWithBsPrefix<TElementType>...`\nexport default function createWithBsPrefix(prefix) {\n  let {\n    displayName = pascalCase(prefix),\n    Component,\n    defaultProps\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const BsComponent = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n    let {\n      className,\n      bsPrefix,\n      as: Tag = Component || 'div',\n      ...props\n    } = _ref;\n    const resolvedPrefix = useBootstrapPrefix(bsPrefix, prefix);\n    return /*#__PURE__*/_jsx(Tag, {\n      ref: ref,\n      className: classNames(className, resolvedPrefix),\n      ...props\n    });\n  });\n  BsComponent.defaultProps = defaultProps;\n  BsComponent.displayName = displayName;\n  return BsComponent;\n}", "map": {"version": 3, "names": ["classNames", "camelize", "React", "useBootstrapPrefix", "jsx", "_jsx", "pascalCase", "str", "toUpperCase", "slice", "createWithBsPrefix", "prefix", "displayName", "Component", "defaultProps", "arguments", "length", "undefined", "BsComponent", "forwardRef", "_ref", "ref", "className", "bsPrefix", "as", "Tag", "props", "resolvedPrefix"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/createWithBsPrefix.js"], "sourcesContent": ["import classNames from 'classnames';\nimport camelize from 'dom-helpers/camelize';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst pascalCase = str => str[0].toUpperCase() + camelize(str).slice(1);\n// TODO: emstricten & fix the typing here! `createWithBsPrefix<TElementType>...`\nexport default function createWithBsPrefix(prefix, {\n  displayName = pascalCase(prefix),\n  Component,\n  defaultProps\n} = {}) {\n  const BsComponent = /*#__PURE__*/React.forwardRef(({\n    className,\n    bsPrefix,\n    as: Tag = Component || 'div',\n    ...props\n  }, ref) => {\n    const resolvedPrefix = useBootstrapPrefix(bsPrefix, prefix);\n    return /*#__PURE__*/_jsx(Tag, {\n      ref: ref,\n      className: classNames(className, resolvedPrefix),\n      ...props\n    });\n  });\n  BsComponent.defaultProps = defaultProps;\n  BsComponent.displayName = displayName;\n  return BsComponent;\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAGC,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGP,QAAQ,CAACM,GAAG,CAAC,CAACE,KAAK,CAAC,CAAC,CAAC;AACvE;AACA,eAAe,SAASC,kBAAkBA,CAACC,MAAM,EAIzC;EAAA,IAJ2C;IACjDC,WAAW,GAAGN,UAAU,CAACK,MAAM,CAAC;IAChCE,SAAS;IACTC;EACF,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACJ,MAAMG,WAAW,GAAG,aAAahB,KAAK,CAACiB,UAAU,CAAC,CAAAC,IAAA,EAK/CC,GAAG,KAAK;IAAA,IALwC;MACjDC,SAAS;MACTC,QAAQ;MACRC,EAAE,EAAEC,GAAG,GAAGZ,SAAS,IAAI,KAAK;MAC5B,GAAGa;IACL,CAAC,GAAAN,IAAA;IACC,MAAMO,cAAc,GAAGxB,kBAAkB,CAACoB,QAAQ,EAAEZ,MAAM,CAAC;IAC3D,OAAO,aAAaN,IAAI,CAACoB,GAAG,EAAE;MAC5BJ,GAAG,EAAEA,GAAG;MACRC,SAAS,EAAEtB,UAAU,CAACsB,SAAS,EAAEK,cAAc,CAAC;MAChD,GAAGD;IACL,CAAC,CAAC;EACJ,CAAC,CAAC;EACFR,WAAW,CAACJ,YAAY,GAAGA,YAAY;EACvCI,WAAW,CAACN,WAAW,GAAGA,WAAW;EACrC,OAAOM,WAAW;AACpB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}