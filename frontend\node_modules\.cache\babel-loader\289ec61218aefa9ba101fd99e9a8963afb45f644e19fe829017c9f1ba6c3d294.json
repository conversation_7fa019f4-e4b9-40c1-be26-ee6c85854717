{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\searchBox.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Form, Button } from \"react-bootstrap\";\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SearchBox(_ref) {\n  _s();\n  let {\n    keyword,\n    setKeyword\n  } = _ref;\n  const [keywordText, setKeywordText] = useState(keyword);\n  const navigate = useNavigate();\n  const queryParams = new URLSearchParams(window.location.search);\n  const brandParam = queryParams.get(\"brand\") ? Number(queryParams.get(\"brand\")) : 0;\n  const categoryParam = queryParams.get(\"category\") ? Number(queryParams.get(\"category\")) : 0;\n  const handleSubmit = e => {\n    e.preventDefault();\n    navigate(`/search?keyword=${keywordText}&brand=${brandParam}&category=${categoryParam}`);\n    setKeyword(keywordText);\n  };\n  return /*#__PURE__*/_jsxDEV(Form, {\n    onSubmit: handleSubmit,\n    style: {\n      display: \"flex\"\n    },\n    className: \"p-1\",\n    children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n      type: \"text\",\n      placeholder: \"Enter product name...\",\n      value: keywordText,\n      onChange: e => {\n        setKeywordText(e.currentTarget.value);\n      },\n      className: \"mx-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      type: \"submit\",\n      variant: \"outline-success\",\n      className: \"p-2\",\n      children: \"Submit\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n}\n_s(SearchBox, \"zTcO3TE4lf0HlL24i5XJi/eY8z0=\", false, function () {\n  return [useNavigate];\n});\n_c = SearchBox;\nexport default SearchBox;\nvar _c;\n$RefreshReg$(_c, \"SearchBox\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Form", "<PERSON><PERSON>", "useNavigate", "jsxDEV", "_jsxDEV", "SearchBox", "_ref", "_s", "keyword", "setKeyword", "keywordText", "setKeywordText", "navigate", "queryParams", "URLSearchParams", "window", "location", "search", "brandParam", "get", "Number", "categoryParam", "handleSubmit", "e", "preventDefault", "onSubmit", "style", "display", "className", "children", "Control", "type", "placeholder", "value", "onChange", "currentTarget", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/searchBox.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { Form, Button } from \"react-bootstrap\";\nimport { useNavigate } from 'react-router-dom';\n\nfunction SearchBox({keyword,setKeyword}) {\n  const [keywordText, setKeywordText] = useState(keyword);\n  const navigate = useNavigate();\n\n  const queryParams = new URLSearchParams(window.location.search);\n  const brandParam = queryParams.get(\"brand\")\n    ? Number(queryParams.get(\"brand\"))\n    : 0;\n  const categoryParam = queryParams.get(\"category\")\n    ? Number(queryParams.get(\"category\"))\n    : 0;\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    navigate(`/search?keyword=${keywordText}&brand=${brandParam}&category=${categoryParam}`);\n    setKeyword(keywordText);\n  };\n\n  return (\n    <Form onSubmit={handleSubmit} style={{ display: \"flex\" }} className=\"p-1\">\n      <Form.Control\n        type=\"text\"\n        placeholder=\"Enter product name...\"\n        value={keywordText}\n        onChange={(e) => {\n          setKeywordText(e.currentTarget.value);\n        }}\n        className=\"mx-2\"\n      ></Form.Control>\n      <Button type=\"submit\" variant=\"outline-success\" className=\"p-2\">\n        Submit\n      </Button>\n    </Form>\n  );\n}\n\nexport default SearchBox;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,MAAM,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,SAASA,CAAAC,IAAA,EAAuB;EAAAC,EAAA;EAAA,IAAtB;IAACC,OAAO;IAACC;EAAU,CAAC,GAAAH,IAAA;EACrC,MAAM,CAACI,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAACS,OAAO,CAAC;EACvD,MAAMI,QAAQ,GAAGV,WAAW,EAAE;EAE9B,MAAMW,WAAW,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;EAC/D,MAAMC,UAAU,GAAGL,WAAW,CAACM,GAAG,CAAC,OAAO,CAAC,GACvCC,MAAM,CAACP,WAAW,CAACM,GAAG,CAAC,OAAO,CAAC,CAAC,GAChC,CAAC;EACL,MAAME,aAAa,GAAGR,WAAW,CAACM,GAAG,CAAC,UAAU,CAAC,GAC7CC,MAAM,CAACP,WAAW,CAACM,GAAG,CAAC,UAAU,CAAC,CAAC,GACnC,CAAC;EAEL,MAAMG,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,EAAE;IAClBZ,QAAQ,CAAE,mBAAkBF,WAAY,UAASQ,UAAW,aAAYG,aAAc,EAAC,CAAC;IACxFZ,UAAU,CAACC,WAAW,CAAC;EACzB,CAAC;EAED,oBACEN,OAAA,CAACJ,IAAI;IAACyB,QAAQ,EAAEH,YAAa;IAACI,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAACC,SAAS,EAAC,KAAK;IAAAC,QAAA,gBACvEzB,OAAA,CAACJ,IAAI,CAAC8B,OAAO;MACXC,IAAI,EAAC,MAAM;MACXC,WAAW,EAAC,uBAAuB;MACnCC,KAAK,EAAEvB,WAAY;MACnBwB,QAAQ,EAAGX,CAAC,IAAK;QACfZ,cAAc,CAACY,CAAC,CAACY,aAAa,CAACF,KAAK,CAAC;MACvC,CAAE;MACFL,SAAS,EAAC;IAAM;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF,eAChBnC,OAAA,CAACH,MAAM;MAAC8B,IAAI,EAAC,QAAQ;MAACS,OAAO,EAAC,iBAAiB;MAACZ,SAAS,EAAC,KAAK;MAAAC,QAAA,EAAC;IAEhE;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAS;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACJ;AAEX;AAAChC,EAAA,CAlCQF,SAAS;EAAA,QAECH,WAAW;AAAA;AAAAuC,EAAA,GAFrBpC,SAAS;AAoClB,eAAeA,SAAS;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}