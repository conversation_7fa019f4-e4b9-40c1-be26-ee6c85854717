{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  active: false,\n  linkProps: {}\n};\nconst BreadcrumbItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    active,\n    children,\n    className,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'li',\n    linkAs: LinkComponent = Anchor,\n    linkProps,\n    href,\n    title,\n    target,\n    ...props\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'breadcrumb-item');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(prefix, className, {\n      active\n    }),\n    \"aria-current\": active ? 'page' : undefined,\n    children: active ? children : /*#__PURE__*/_jsx(LinkComponent, {\n      ...linkProps,\n      href: href,\n      title: title,\n      target: target,\n      children: children\n    })\n  });\n});\nBreadcrumbItem.displayName = 'BreadcrumbItem';\nBreadcrumbItem.defaultProps = defaultProps;\nexport default BreadcrumbItem;", "map": {"version": 3, "names": ["classNames", "React", "<PERSON><PERSON>", "useBootstrapPrefix", "jsx", "_jsx", "defaultProps", "active", "linkProps", "BreadcrumbItem", "forwardRef", "_ref", "ref", "bsPrefix", "children", "className", "as", "Component", "linkAs", "LinkComponent", "href", "title", "target", "props", "prefix", "undefined", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/BreadcrumbItem.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  active: false,\n  linkProps: {}\n};\nconst BreadcrumbItem = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  active,\n  children,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'li',\n  linkAs: LinkComponent = Anchor,\n  linkProps,\n  href,\n  title,\n  target,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'breadcrumb-item');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(prefix, className, {\n      active\n    }),\n    \"aria-current\": active ? 'page' : undefined,\n    children: active ? children : /*#__PURE__*/_jsx(LinkComponent, {\n      ...linkProps,\n      href: href,\n      title: title,\n      target: target,\n      children: children\n    })\n  });\n});\nBreadcrumbItem.displayName = 'BreadcrumbItem';\nBreadcrumbItem.defaultProps = defaultProps;\nexport default BreadcrumbItem;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG;EACnBC,MAAM,EAAE,KAAK;EACbC,SAAS,EAAE,CAAC;AACd,CAAC;AACD,MAAMC,cAAc,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAAC,IAAA,EAalDC,GAAG,KAAK;EAAA,IAb2C;IACpDC,QAAQ;IACRN,MAAM;IACNO,QAAQ;IACRC,SAAS;IACT;IACAC,EAAE,EAAEC,SAAS,GAAG,IAAI;IACpBC,MAAM,EAAEC,aAAa,GAAGjB,MAAM;IAC9BM,SAAS;IACTY,IAAI;IACJC,KAAK;IACLC,MAAM;IACN,GAAGC;EACL,CAAC,GAAAZ,IAAA;EACC,MAAMa,MAAM,GAAGrB,kBAAkB,CAACU,QAAQ,EAAE,iBAAiB,CAAC;EAC9D,OAAO,aAAaR,IAAI,CAACY,SAAS,EAAE;IAClCL,GAAG,EAAEA,GAAG;IACR,GAAGW,KAAK;IACRR,SAAS,EAAEf,UAAU,CAACwB,MAAM,EAAET,SAAS,EAAE;MACvCR;IACF,CAAC,CAAC;IACF,cAAc,EAAEA,MAAM,GAAG,MAAM,GAAGkB,SAAS;IAC3CX,QAAQ,EAAEP,MAAM,GAAGO,QAAQ,GAAG,aAAaT,IAAI,CAACc,aAAa,EAAE;MAC7D,GAAGX,SAAS;MACZY,IAAI,EAAEA,IAAI;MACVC,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA,MAAM;MACdR,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFL,cAAc,CAACiB,WAAW,GAAG,gBAAgB;AAC7CjB,cAAc,CAACH,YAAY,GAAGA,YAAY;AAC1C,eAAeG,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}