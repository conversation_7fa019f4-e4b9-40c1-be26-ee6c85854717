{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst propTypes = {\n  /**\n   * Specify whether the feedback is for valid or invalid fields\n   *\n   * @type {('valid'|'invalid')}\n   */\n  type: PropTypes.string,\n  /** Display feedback as a tooltip. */\n  tooltip: PropTypes.bool,\n  as: PropTypes.elementType\n};\nconst Feedback = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(_ref, ref) => {\n  let {\n    as: Component = 'div',\n    className,\n    type = 'valid',\n    tooltip = false,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    className: classNames(className, `${type}-${tooltip ? 'tooltip' : 'feedback'}`)\n  });\n});\nFeedback.displayName = 'Feedback';\nFeedback.propTypes = propTypes;\nexport default Feedback;", "map": {"version": 3, "names": ["classNames", "React", "PropTypes", "jsx", "_jsx", "propTypes", "type", "string", "tooltip", "bool", "as", "elementType", "<PERSON><PERSON><PERSON>", "forwardRef", "_ref", "ref", "Component", "className", "props", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Feedback.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst propTypes = {\n  /**\n   * Specify whether the feedback is for valid or invalid fields\n   *\n   * @type {('valid'|'invalid')}\n   */\n  type: PropTypes.string,\n  /** Display feedback as a tooltip. */\n  tooltip: PropTypes.bool,\n  as: PropTypes.elementType\n};\nconst Feedback = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  as: Component = 'div',\n  className,\n  type = 'valid',\n  tooltip = false,\n  ...props\n}, ref) => /*#__PURE__*/_jsx(Component, {\n  ...props,\n  ref: ref,\n  className: classNames(className, `${type}-${tooltip ? 'tooltip' : 'feedback'}`)\n}));\nFeedback.displayName = 'Feedback';\nFeedback.propTypes = propTypes;\nexport default Feedback;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG;EAChB;AACF;AACA;AACA;AACA;EACEC,IAAI,EAAEJ,SAAS,CAACK,MAAM;EACtB;EACAC,OAAO,EAAEN,SAAS,CAACO,IAAI;EACvBC,EAAE,EAAER,SAAS,CAACS;AAChB,CAAC;AACD,MAAMC,QAAQ,GAAG,aAAaX,KAAK,CAACY,UAAU;AAC9C;AACA,CAAAC,IAAA,EAMGC,GAAG;EAAA,IANL;IACCL,EAAE,EAAEM,SAAS,GAAG,KAAK;IACrBC,SAAS;IACTX,IAAI,GAAG,OAAO;IACdE,OAAO,GAAG,KAAK;IACf,GAAGU;EACL,CAAC,GAAAJ,IAAA;EAAA,OAAU,aAAaV,IAAI,CAACY,SAAS,EAAE;IACtC,GAAGE,KAAK;IACRH,GAAG,EAAEA,GAAG;IACRE,SAAS,EAAEjB,UAAU,CAACiB,SAAS,EAAG,GAAEX,IAAK,IAAGE,OAAO,GAAG,SAAS,GAAG,UAAW,EAAC;EAChF,CAAC,CAAC;AAAA,EAAC;AACHI,QAAQ,CAACO,WAAW,GAAG,UAAU;AACjCP,QAAQ,CAACP,SAAS,GAAGA,SAAS;AAC9B,eAAeO,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}