{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AbstractModalHeader from './AbstractModalHeader';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  closeLabel: 'Close',\n  closeButton: false\n};\nconst ModalHeader = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'modal-header');\n  return /*#__PURE__*/_jsx(AbstractModalHeader, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsPrefix)\n  });\n});\nModalHeader.displayName = 'ModalHeader';\nModalHeader.defaultProps = defaultProps;\nexport default ModalHeader;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "AbstractModalHeader", "jsx", "_jsx", "defaultProps", "<PERSON><PERSON><PERSON><PERSON>", "closeButton", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "_ref", "ref", "bsPrefix", "className", "props", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/ModalHeader.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AbstractModalHeader from './AbstractModalHeader';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  closeLabel: 'Close',\n  closeButton: false\n};\nconst ModalHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'modal-header');\n  return /*#__PURE__*/_jsx(AbstractModalHeader, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsPrefix)\n  });\n});\nModalHeader.displayName = 'ModalHeader';\nModalHeader.defaultProps = defaultProps;\nexport default ModalHeader;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG;EACnBC,UAAU,EAAE,OAAO;EACnBC,WAAW,EAAE;AACf,CAAC;AACD,MAAMC,WAAW,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAAC,IAAA,EAI/CC,GAAG,KAAK;EAAA,IAJwC;IACjDC,QAAQ;IACRC,SAAS;IACT,GAAGC;EACL,CAAC,GAAAJ,IAAA;EACCE,QAAQ,GAAGX,kBAAkB,CAACW,QAAQ,EAAE,cAAc,CAAC;EACvD,OAAO,aAAaR,IAAI,CAACF,mBAAmB,EAAE;IAC5CS,GAAG,EAAEA,GAAG;IACR,GAAGG,KAAK;IACRD,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAED,QAAQ;EAC3C,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,WAAW,CAACO,WAAW,GAAG,aAAa;AACvCP,WAAW,CAACH,YAAY,GAAGA,YAAY;AACvC,eAAeG,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}