{"ast": null, "code": "import PropTypes from 'prop-types';\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst propTypes = {\n  /** An accessible label indicating the relevant information about the Close Button. */\n  'aria-label': PropTypes.string,\n  /** A callback fired after the Close Button is clicked. */\n  onClick: PropTypes.func,\n  /**\n   * Render different color variant for the button.\n   *\n   * Omitting this will render the default dark color.\n   */\n  variant: PropTypes.oneOf(['white'])\n};\nconst defaultProps = {\n  'aria-label': 'Close'\n};\nconst CloseButton = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    className,\n    variant,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/_jsx(\"button\", {\n    ref: ref,\n    type: \"button\",\n    className: classNames('btn-close', variant && `btn-close-${variant}`, className),\n    ...props\n  });\n});\nCloseButton.displayName = 'CloseButton';\nCloseButton.propTypes = propTypes;\nCloseButton.defaultProps = defaultProps;\nexport default CloseButton;", "map": {"version": 3, "names": ["PropTypes", "React", "classNames", "jsx", "_jsx", "propTypes", "string", "onClick", "func", "variant", "oneOf", "defaultProps", "CloseButton", "forwardRef", "_ref", "ref", "className", "props", "type", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/CloseButton.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst propTypes = {\n  /** An accessible label indicating the relevant information about the Close Button. */\n  'aria-label': PropTypes.string,\n  /** A callback fired after the Close Button is clicked. */\n  onClick: PropTypes.func,\n  /**\n   * Render different color variant for the button.\n   *\n   * Omitting this will render the default dark color.\n   */\n  variant: PropTypes.oneOf(['white'])\n};\nconst defaultProps = {\n  'aria-label': 'Close'\n};\nconst CloseButton = /*#__PURE__*/React.forwardRef(({\n  className,\n  variant,\n  ...props\n}, ref) => /*#__PURE__*/_jsx(\"button\", {\n  ref: ref,\n  type: \"button\",\n  className: classNames('btn-close', variant && `btn-close-${variant}`, className),\n  ...props\n}));\nCloseButton.displayName = 'CloseButton';\nCloseButton.propTypes = propTypes;\nCloseButton.defaultProps = defaultProps;\nexport default CloseButton;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG;EAChB;EACA,YAAY,EAAEL,SAAS,CAACM,MAAM;EAC9B;EACAC,OAAO,EAAEP,SAAS,CAACQ,IAAI;EACvB;AACF;AACA;AACA;AACA;EACEC,OAAO,EAAET,SAAS,CAACU,KAAK,CAAC,CAAC,OAAO,CAAC;AACpC,CAAC;AACD,MAAMC,YAAY,GAAG;EACnB,YAAY,EAAE;AAChB,CAAC;AACD,MAAMC,WAAW,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,CAAAC,IAAA,EAI/CC,GAAG;EAAA,IAJ6C;IACjDC,SAAS;IACTP,OAAO;IACP,GAAGQ;EACL,CAAC,GAAAH,IAAA;EAAA,OAAU,aAAaV,IAAI,CAAC,QAAQ,EAAE;IACrCW,GAAG,EAAEA,GAAG;IACRG,IAAI,EAAE,QAAQ;IACdF,SAAS,EAAEd,UAAU,CAAC,WAAW,EAAEO,OAAO,IAAK,aAAYA,OAAQ,EAAC,EAAEO,SAAS,CAAC;IAChF,GAAGC;EACL,CAAC,CAAC;AAAA,EAAC;AACHL,WAAW,CAACO,WAAW,GAAG,aAAa;AACvCP,WAAW,CAACP,SAAS,GAAGA,SAAS;AACjCO,WAAW,CAACD,YAAY,GAAGA,YAAY;AACvC,eAAeC,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}