{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AbstractModalHeader from './AbstractModalHeader';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  closeLabel: 'Close',\n  closeButton: false\n};\nconst OffcanvasHeader = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas-header');\n  return /*#__PURE__*/_jsx(AbstractModalHeader, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsPrefix)\n  });\n});\nOffcanvasHeader.displayName = 'OffcanvasHeader';\nOffcanvasHeader.defaultProps = defaultProps;\nexport default OffcanvasHeader;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "AbstractModalHeader", "jsx", "_jsx", "defaultProps", "<PERSON><PERSON><PERSON><PERSON>", "closeButton", "OffcanvasHeader", "forwardRef", "_ref", "ref", "bsPrefix", "className", "props", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/OffcanvasHeader.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AbstractModalHeader from './AbstractModalHeader';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  closeLabel: 'Close',\n  closeButton: false\n};\nconst OffcanvasHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'offcanvas-header');\n  return /*#__PURE__*/_jsx(AbstractModalHeader, {\n    ref: ref,\n    ...props,\n    className: classNames(className, bsPrefix)\n  });\n});\nOffcanvasHeader.displayName = 'OffcanvasHeader';\nOffcanvasHeader.defaultProps = defaultProps;\nexport default OffcanvasHeader;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG;EACnBC,UAAU,EAAE,OAAO;EACnBC,WAAW,EAAE;AACf,CAAC;AACD,MAAMC,eAAe,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAAC,IAAA,EAInDC,GAAG,KAAK;EAAA,IAJ4C;IACrDC,QAAQ;IACRC,SAAS;IACT,GAAGC;EACL,CAAC,GAAAJ,IAAA;EACCE,QAAQ,GAAGX,kBAAkB,CAACW,QAAQ,EAAE,kBAAkB,CAAC;EAC3D,OAAO,aAAaR,IAAI,CAACF,mBAAmB,EAAE;IAC5CS,GAAG,EAAEA,GAAG;IACR,GAAGG,KAAK;IACRD,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAED,QAAQ;EAC3C,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,eAAe,CAACO,WAAW,GAAG,iBAAiB;AAC/CP,eAAe,CAACH,YAAY,GAAGA,YAAY;AAC3C,eAAeG,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}