{"ast": null, "code": "import arrow from '@popperjs/core/lib/modifiers/arrow';\nimport computeStyles from '@popperjs/core/lib/modifiers/computeStyles';\nimport eventListeners from '@popperjs/core/lib/modifiers/eventListeners';\nimport flip from '@popperjs/core/lib/modifiers/flip';\nimport hide from '@popperjs/core/lib/modifiers/hide';\nimport offset from '@popperjs/core/lib/modifiers/offset';\nimport popperOffsets from '@popperjs/core/lib/modifiers/popperOffsets';\nimport preventOverflow from '@popperjs/core/lib/modifiers/preventOverflow';\nimport { placements } from '@popperjs/core/lib/enums';\nimport { popperGenerator } from '@popperjs/core/lib/popper-base';\n\n// For the common JS build we will turn this file into a bundle with no imports.\n// This is b/c the Popper lib is all esm files, and would break in a common js only environment\nexport const createPopper = popperGenerator({\n  defaultModifiers: [hide, popperOffsets, computeStyles, eventListeners, offset, flip, preventOverflow, arrow]\n});\nexport { placements };", "map": {"version": 3, "names": ["arrow", "computeStyles", "eventListeners", "flip", "hide", "offset", "popperOffsets", "preventOverflow", "placements", "popperGenerator", "createPopper", "defaultModifiers"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/ui/esm/popper.js"], "sourcesContent": ["import arrow from '@popperjs/core/lib/modifiers/arrow';\nimport computeStyles from '@popperjs/core/lib/modifiers/computeStyles';\nimport eventListeners from '@popperjs/core/lib/modifiers/eventListeners';\nimport flip from '@popperjs/core/lib/modifiers/flip';\nimport hide from '@popperjs/core/lib/modifiers/hide';\nimport offset from '@popperjs/core/lib/modifiers/offset';\nimport popperOffsets from '@popperjs/core/lib/modifiers/popperOffsets';\nimport preventOverflow from '@popperjs/core/lib/modifiers/preventOverflow';\nimport { placements } from '@popperjs/core/lib/enums';\nimport { popperGenerator } from '@popperjs/core/lib/popper-base';\n\n// For the common JS build we will turn this file into a bundle with no imports.\n// This is b/c the Popper lib is all esm files, and would break in a common js only environment\nexport const createPopper = popperGenerator({\n  defaultModifiers: [hide, popperOffsets, computeStyles, eventListeners, offset, flip, preventOverflow, arrow]\n});\nexport { placements };"], "mappings": "AAAA,OAAOA,KAAK,MAAM,oCAAoC;AACtD,OAAOC,aAAa,MAAM,4CAA4C;AACtE,OAAOC,cAAc,MAAM,6CAA6C;AACxE,OAAOC,IAAI,MAAM,mCAAmC;AACpD,OAAOC,IAAI,MAAM,mCAAmC;AACpD,OAAOC,MAAM,MAAM,qCAAqC;AACxD,OAAOC,aAAa,MAAM,4CAA4C;AACtE,OAAOC,eAAe,MAAM,8CAA8C;AAC1E,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,eAAe,QAAQ,gCAAgC;;AAEhE;AACA;AACA,OAAO,MAAMC,YAAY,GAAGD,eAAe,CAAC;EAC1CE,gBAAgB,EAAE,CAACP,IAAI,EAAEE,aAAa,EAAEL,aAAa,EAAEC,cAAc,EAAEG,MAAM,EAAEF,IAAI,EAAEI,eAAe,EAAEP,KAAK;AAC7G,CAAC,CAAC;AACF,SAASQ,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}