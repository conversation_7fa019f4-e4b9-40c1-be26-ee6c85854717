{"ast": null, "code": "import classNames from 'classnames';\nimport css from 'dom-helpers/css';\nimport React, { useMemo } from 'react';\nimport { ENTERED, ENTERING, EXITED, EXITING } from 'react-transition-group/Transition';\nimport transitionEndListener from './transitionEndListener';\nimport createChainedFunction from './createChainedFunction';\nimport triggerBrowserReflow from './triggerBrowserReflow';\nimport TransitionWrapper from './TransitionWrapper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst MARGINS = {\n  height: ['marginTop', 'marginBottom'],\n  width: ['marginLeft', 'marginRight']\n};\nfunction getDefaultDimensionValue(dimension, elem) {\n  const offset = `offset${dimension[0].toUpperCase()}${dimension.slice(1)}`;\n  const value = elem[offset];\n  const margins = MARGINS[dimension];\n  return value +\n  // @ts-ignore\n  parseInt(css(elem, margins[0]), 10) +\n  // @ts-ignore\n  parseInt(css(elem, margins[1]), 10);\n}\nconst collapseStyles = {\n  [EXITED]: 'collapse',\n  [EXITING]: 'collapsing',\n  [ENTERING]: 'collapsing',\n  [ENTERED]: 'collapse show'\n};\nconst defaultProps = {\n  in: false,\n  timeout: 300,\n  mountOnEnter: false,\n  unmountOnExit: false,\n  appear: false,\n  getDimensionValue: getDefaultDimensionValue\n};\nconst Collapse = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    onEnter,\n    onEntering,\n    onEntered,\n    onExit,\n    onExiting,\n    className,\n    children,\n    dimension = 'height',\n    getDimensionValue = getDefaultDimensionValue,\n    ...props\n  } = _ref;\n  /* Compute dimension */\n  const computedDimension = typeof dimension === 'function' ? dimension() : dimension;\n\n  /* -- Expanding -- */\n  const handleEnter = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = '0';\n  }, onEnter), [computedDimension, onEnter]);\n  const handleEntering = useMemo(() => createChainedFunction(elem => {\n    const scroll = `scroll${computedDimension[0].toUpperCase()}${computedDimension.slice(1)}`;\n    elem.style[computedDimension] = `${elem[scroll]}px`;\n  }, onEntering), [computedDimension, onEntering]);\n  const handleEntered = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = null;\n  }, onEntered), [computedDimension, onEntered]);\n\n  /* -- Collapsing -- */\n  const handleExit = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = `${getDimensionValue(computedDimension, elem)}px`;\n    triggerBrowserReflow(elem);\n  }, onExit), [onExit, getDimensionValue, computedDimension]);\n  const handleExiting = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = null;\n  }, onExiting), [computedDimension, onExiting]);\n  return /*#__PURE__*/_jsx(TransitionWrapper, {\n    ref: ref,\n    addEndListener: transitionEndListener,\n    ...props,\n    \"aria-expanded\": props.role ? props.in : null,\n    onEnter: handleEnter,\n    onEntering: handleEntering,\n    onEntered: handleEntered,\n    onExit: handleExit,\n    onExiting: handleExiting,\n    childRef: children.ref,\n    children: (state, innerProps) => /*#__PURE__*/React.cloneElement(children, {\n      ...innerProps,\n      className: classNames(className, children.props.className, collapseStyles[state], computedDimension === 'width' && 'collapse-horizontal')\n    })\n  });\n});\n\n// @ts-ignore\n\n// @ts-ignore\nCollapse.defaultProps = defaultProps;\nexport default Collapse;", "map": {"version": 3, "names": ["classNames", "css", "React", "useMemo", "ENTERED", "ENTERING", "EXITED", "EXITING", "transitionEndListener", "createChainedFunction", "triggerBrowserReflow", "TransitionWrapper", "jsx", "_jsx", "MARGINS", "height", "width", "getDefaultDimensionValue", "dimension", "elem", "offset", "toUpperCase", "slice", "value", "margins", "parseInt", "collapseStyles", "defaultProps", "in", "timeout", "mountOnEnter", "unmountOnExit", "appear", "getDimensionValue", "Collapse", "forwardRef", "_ref", "ref", "onEnter", "onEntering", "onEntered", "onExit", "onExiting", "className", "children", "props", "computedDimension", "handleEnter", "style", "handleEntering", "scroll", "handleEntered", "handleExit", "handleExiting", "addEndListener", "role", "childRef", "state", "innerProps", "cloneElement"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Collapse.js"], "sourcesContent": ["import classNames from 'classnames';\nimport css from 'dom-helpers/css';\nimport React, { useMemo } from 'react';\nimport { ENTERED, ENTERING, EXITED, EXITING } from 'react-transition-group/Transition';\nimport transitionEndListener from './transitionEndListener';\nimport createChainedFunction from './createChainedFunction';\nimport triggerBrowserReflow from './triggerBrowserReflow';\nimport TransitionWrapper from './TransitionWrapper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst MARGINS = {\n  height: ['marginTop', 'marginBottom'],\n  width: ['marginLeft', 'marginRight']\n};\nfunction getDefaultDimensionValue(dimension, elem) {\n  const offset = `offset${dimension[0].toUpperCase()}${dimension.slice(1)}`;\n  const value = elem[offset];\n  const margins = MARGINS[dimension];\n  return value +\n  // @ts-ignore\n  parseInt(css(elem, margins[0]), 10) +\n  // @ts-ignore\n  parseInt(css(elem, margins[1]), 10);\n}\nconst collapseStyles = {\n  [EXITED]: 'collapse',\n  [EXITING]: 'collapsing',\n  [ENTERING]: 'collapsing',\n  [ENTERED]: 'collapse show'\n};\nconst defaultProps = {\n  in: false,\n  timeout: 300,\n  mountOnEnter: false,\n  unmountOnExit: false,\n  appear: false,\n  getDimensionValue: getDefaultDimensionValue\n};\nconst Collapse = /*#__PURE__*/React.forwardRef(({\n  onEnter,\n  onEntering,\n  onEntered,\n  onExit,\n  onExiting,\n  className,\n  children,\n  dimension = 'height',\n  getDimensionValue = getDefaultDimensionValue,\n  ...props\n}, ref) => {\n  /* Compute dimension */\n  const computedDimension = typeof dimension === 'function' ? dimension() : dimension;\n\n  /* -- Expanding -- */\n  const handleEnter = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = '0';\n  }, onEnter), [computedDimension, onEnter]);\n  const handleEntering = useMemo(() => createChainedFunction(elem => {\n    const scroll = `scroll${computedDimension[0].toUpperCase()}${computedDimension.slice(1)}`;\n    elem.style[computedDimension] = `${elem[scroll]}px`;\n  }, onEntering), [computedDimension, onEntering]);\n  const handleEntered = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = null;\n  }, onEntered), [computedDimension, onEntered]);\n\n  /* -- Collapsing -- */\n  const handleExit = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = `${getDimensionValue(computedDimension, elem)}px`;\n    triggerBrowserReflow(elem);\n  }, onExit), [onExit, getDimensionValue, computedDimension]);\n  const handleExiting = useMemo(() => createChainedFunction(elem => {\n    elem.style[computedDimension] = null;\n  }, onExiting), [computedDimension, onExiting]);\n  return /*#__PURE__*/_jsx(TransitionWrapper, {\n    ref: ref,\n    addEndListener: transitionEndListener,\n    ...props,\n    \"aria-expanded\": props.role ? props.in : null,\n    onEnter: handleEnter,\n    onEntering: handleEntering,\n    onEntered: handleEntered,\n    onExit: handleExit,\n    onExiting: handleExiting,\n    childRef: children.ref,\n    children: (state, innerProps) => /*#__PURE__*/React.cloneElement(children, {\n      ...innerProps,\n      className: classNames(className, children.props.className, collapseStyles[state], computedDimension === 'width' && 'collapse-horizontal')\n    })\n  });\n});\n\n// @ts-ignore\n\n// @ts-ignore\nCollapse.defaultProps = defaultProps;\nexport default Collapse;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,SAASC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,QAAQ,mCAAmC;AACtF,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,OAAO,GAAG;EACdC,MAAM,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC;EACrCC,KAAK,EAAE,CAAC,YAAY,EAAE,aAAa;AACrC,CAAC;AACD,SAASC,wBAAwBA,CAACC,SAAS,EAAEC,IAAI,EAAE;EACjD,MAAMC,MAAM,GAAI,SAAQF,SAAS,CAAC,CAAC,CAAC,CAACG,WAAW,EAAG,GAAEH,SAAS,CAACI,KAAK,CAAC,CAAC,CAAE,EAAC;EACzE,MAAMC,KAAK,GAAGJ,IAAI,CAACC,MAAM,CAAC;EAC1B,MAAMI,OAAO,GAAGV,OAAO,CAACI,SAAS,CAAC;EAClC,OAAOK,KAAK;EACZ;EACAE,QAAQ,CAACxB,GAAG,CAACkB,IAAI,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACnC;EACAC,QAAQ,CAACxB,GAAG,CAACkB,IAAI,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AACrC;AACA,MAAME,cAAc,GAAG;EACrB,CAACpB,MAAM,GAAG,UAAU;EACpB,CAACC,OAAO,GAAG,YAAY;EACvB,CAACF,QAAQ,GAAG,YAAY;EACxB,CAACD,OAAO,GAAG;AACb,CAAC;AACD,MAAMuB,YAAY,GAAG;EACnBC,EAAE,EAAE,KAAK;EACTC,OAAO,EAAE,GAAG;EACZC,YAAY,EAAE,KAAK;EACnBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE,KAAK;EACbC,iBAAiB,EAAEhB;AACrB,CAAC;AACD,MAAMiB,QAAQ,GAAG,aAAahC,KAAK,CAACiC,UAAU,CAAC,CAAAC,IAAA,EAW5CC,GAAG,KAAK;EAAA,IAXqC;IAC9CC,OAAO;IACPC,UAAU;IACVC,SAAS;IACTC,MAAM;IACNC,SAAS;IACTC,SAAS;IACTC,QAAQ;IACR1B,SAAS,GAAG,QAAQ;IACpBe,iBAAiB,GAAGhB,wBAAwB;IAC5C,GAAG4B;EACL,CAAC,GAAAT,IAAA;EACC;EACA,MAAMU,iBAAiB,GAAG,OAAO5B,SAAS,KAAK,UAAU,GAAGA,SAAS,EAAE,GAAGA,SAAS;;EAEnF;EACA,MAAM6B,WAAW,GAAG5C,OAAO,CAAC,MAAMM,qBAAqB,CAACU,IAAI,IAAI;IAC9DA,IAAI,CAAC6B,KAAK,CAACF,iBAAiB,CAAC,GAAG,GAAG;EACrC,CAAC,EAAER,OAAO,CAAC,EAAE,CAACQ,iBAAiB,EAAER,OAAO,CAAC,CAAC;EAC1C,MAAMW,cAAc,GAAG9C,OAAO,CAAC,MAAMM,qBAAqB,CAACU,IAAI,IAAI;IACjE,MAAM+B,MAAM,GAAI,SAAQJ,iBAAiB,CAAC,CAAC,CAAC,CAACzB,WAAW,EAAG,GAAEyB,iBAAiB,CAACxB,KAAK,CAAC,CAAC,CAAE,EAAC;IACzFH,IAAI,CAAC6B,KAAK,CAACF,iBAAiB,CAAC,GAAI,GAAE3B,IAAI,CAAC+B,MAAM,CAAE,IAAG;EACrD,CAAC,EAAEX,UAAU,CAAC,EAAE,CAACO,iBAAiB,EAAEP,UAAU,CAAC,CAAC;EAChD,MAAMY,aAAa,GAAGhD,OAAO,CAAC,MAAMM,qBAAqB,CAACU,IAAI,IAAI;IAChEA,IAAI,CAAC6B,KAAK,CAACF,iBAAiB,CAAC,GAAG,IAAI;EACtC,CAAC,EAAEN,SAAS,CAAC,EAAE,CAACM,iBAAiB,EAAEN,SAAS,CAAC,CAAC;;EAE9C;EACA,MAAMY,UAAU,GAAGjD,OAAO,CAAC,MAAMM,qBAAqB,CAACU,IAAI,IAAI;IAC7DA,IAAI,CAAC6B,KAAK,CAACF,iBAAiB,CAAC,GAAI,GAAEb,iBAAiB,CAACa,iBAAiB,EAAE3B,IAAI,CAAE,IAAG;IACjFT,oBAAoB,CAACS,IAAI,CAAC;EAC5B,CAAC,EAAEsB,MAAM,CAAC,EAAE,CAACA,MAAM,EAAER,iBAAiB,EAAEa,iBAAiB,CAAC,CAAC;EAC3D,MAAMO,aAAa,GAAGlD,OAAO,CAAC,MAAMM,qBAAqB,CAACU,IAAI,IAAI;IAChEA,IAAI,CAAC6B,KAAK,CAACF,iBAAiB,CAAC,GAAG,IAAI;EACtC,CAAC,EAAEJ,SAAS,CAAC,EAAE,CAACI,iBAAiB,EAAEJ,SAAS,CAAC,CAAC;EAC9C,OAAO,aAAa7B,IAAI,CAACF,iBAAiB,EAAE;IAC1C0B,GAAG,EAAEA,GAAG;IACRiB,cAAc,EAAE9C,qBAAqB;IACrC,GAAGqC,KAAK;IACR,eAAe,EAAEA,KAAK,CAACU,IAAI,GAAGV,KAAK,CAACjB,EAAE,GAAG,IAAI;IAC7CU,OAAO,EAAES,WAAW;IACpBR,UAAU,EAAEU,cAAc;IAC1BT,SAAS,EAAEW,aAAa;IACxBV,MAAM,EAAEW,UAAU;IAClBV,SAAS,EAAEW,aAAa;IACxBG,QAAQ,EAAEZ,QAAQ,CAACP,GAAG;IACtBO,QAAQ,EAAEA,CAACa,KAAK,EAAEC,UAAU,KAAK,aAAaxD,KAAK,CAACyD,YAAY,CAACf,QAAQ,EAAE;MACzE,GAAGc,UAAU;MACbf,SAAS,EAAE3C,UAAU,CAAC2C,SAAS,EAAEC,QAAQ,CAACC,KAAK,CAACF,SAAS,EAAEjB,cAAc,CAAC+B,KAAK,CAAC,EAAEX,iBAAiB,KAAK,OAAO,IAAI,qBAAqB;IAC1I,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;;AAEA;AACAZ,QAAQ,CAACP,YAAY,GAAGA,YAAY;AACpC,eAAeO,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}