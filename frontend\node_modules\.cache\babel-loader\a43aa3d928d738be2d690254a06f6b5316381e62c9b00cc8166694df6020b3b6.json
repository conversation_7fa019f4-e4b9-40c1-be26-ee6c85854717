{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport divWithClassName from './divWithClassName';\nimport createWithBsPrefix from './createWithBsPrefix';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = createWithBsPrefix('alert-heading', {\n  Component: DivStyledAsH4\n});\nconst AlertLink = createWithBsPrefix('alert-link', {\n  Component: Anchor\n});\nconst defaultProps = {\n  variant: 'primary',\n  show: true,\n  transition: Fade,\n  closeLabel: 'Close alert'\n};\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show,\n    closeLabel,\n    closeVariant,\n    className,\n    children,\n    variant,\n    onClose,\n    dismissible,\n    transition,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nAlert.defaultProps = defaultProps;\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});", "map": {"version": 3, "names": ["classNames", "React", "useUncontrolled", "useEventCallback", "<PERSON><PERSON>", "useBootstrapPrefix", "Fade", "CloseButton", "divWithClassName", "createWithBsPrefix", "jsx", "_jsx", "jsxs", "_jsxs", "DivStyledAsH4", "displayName", "AlertHeading", "Component", "AlertLink", "defaultProps", "variant", "show", "transition", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "forwardRef", "uncontrolledProps", "ref", "bsPrefix", "closeVariant", "className", "children", "onClose", "dismissible", "props", "prefix", "handleClose", "e", "Transition", "alert", "role", "undefined", "onClick", "unmountOnExit", "in", "Object", "assign", "Link", "Heading"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Alert.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport Anchor from '@restart/ui/Anchor';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport divWithClassName from './divWithClassName';\nimport createWithBsPrefix from './createWithBsPrefix';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nDivStyledAsH4.displayName = 'DivStyledAsH4';\nconst AlertHeading = createWithBsPrefix('alert-heading', {\n  Component: DivStyledAsH4\n});\nconst AlertLink = createWithBsPrefix('alert-link', {\n  Component: Anchor\n});\nconst defaultProps = {\n  variant: 'primary',\n  show: true,\n  transition: Fade,\n  closeLabel: 'Close alert'\n};\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show,\n    closeLabel,\n    closeVariant,\n    className,\n    children,\n    variant,\n    onClose,\n    dismissible,\n    transition,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nAlert.defaultProps = defaultProps;\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,aAAa,GAAGN,gBAAgB,CAAC,IAAI,CAAC;AAC5CM,aAAa,CAACC,WAAW,GAAG,eAAe;AAC3C,MAAMC,YAAY,GAAGP,kBAAkB,CAAC,eAAe,EAAE;EACvDQ,SAAS,EAAEH;AACb,CAAC,CAAC;AACF,MAAMI,SAAS,GAAGT,kBAAkB,CAAC,YAAY,EAAE;EACjDQ,SAAS,EAAEb;AACb,CAAC,CAAC;AACF,MAAMe,YAAY,GAAG;EACnBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,IAAI;EACVC,UAAU,EAAEhB,IAAI;EAChBiB,UAAU,EAAE;AACd,CAAC;AACD,MAAMC,KAAK,GAAG,aAAavB,KAAK,CAACwB,UAAU,CAAC,CAACC,iBAAiB,EAAEC,GAAG,KAAK;EACtE,MAAM;IACJC,QAAQ;IACRP,IAAI;IACJE,UAAU;IACVM,YAAY;IACZC,SAAS;IACTC,QAAQ;IACRX,OAAO;IACPY,OAAO;IACPC,WAAW;IACXX,UAAU;IACV,GAAGY;EACL,CAAC,GAAGhC,eAAe,CAACwB,iBAAiB,EAAE;IACrCL,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMc,MAAM,GAAG9B,kBAAkB,CAACuB,QAAQ,EAAE,OAAO,CAAC;EACpD,MAAMQ,WAAW,GAAGjC,gBAAgB,CAACkC,CAAC,IAAI;IACxC,IAAIL,OAAO,EAAE;MACXA,OAAO,CAAC,KAAK,EAAEK,CAAC,CAAC;IACnB;EACF,CAAC,CAAC;EACF,MAAMC,UAAU,GAAGhB,UAAU,KAAK,IAAI,GAAGhB,IAAI,GAAGgB,UAAU;EAC1D,MAAMiB,KAAK,GAAG,aAAa1B,KAAK,CAAC,KAAK,EAAE;IACtC2B,IAAI,EAAE,OAAO;IACb,IAAI,CAACF,UAAU,GAAGJ,KAAK,GAAGO,SAAS,CAAC;IACpCd,GAAG,EAAEA,GAAG;IACRG,SAAS,EAAE9B,UAAU,CAAC8B,SAAS,EAAEK,MAAM,EAAEf,OAAO,IAAK,GAAEe,MAAO,IAAGf,OAAQ,EAAC,EAAEa,WAAW,IAAK,GAAEE,MAAO,cAAa,CAAC;IACnHJ,QAAQ,EAAE,CAACE,WAAW,IAAI,aAAatB,IAAI,CAACJ,WAAW,EAAE;MACvDmC,OAAO,EAAEN,WAAW;MACpB,YAAY,EAAEb,UAAU;MACxBH,OAAO,EAAES;IACX,CAAC,CAAC,EAAEE,QAAQ;EACd,CAAC,CAAC;EACF,IAAI,CAACO,UAAU,EAAE,OAAOjB,IAAI,GAAGkB,KAAK,GAAG,IAAI;EAC3C,OAAO,aAAa5B,IAAI,CAAC2B,UAAU,EAAE;IACnCK,aAAa,EAAE,IAAI;IACnB,GAAGT,KAAK;IACRP,GAAG,EAAEc,SAAS;IACdG,EAAE,EAAEvB,IAAI;IACRU,QAAQ,EAAEQ;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFf,KAAK,CAACT,WAAW,GAAG,OAAO;AAC3BS,KAAK,CAACL,YAAY,GAAGA,YAAY;AACjC,eAAe0B,MAAM,CAACC,MAAM,CAACtB,KAAK,EAAE;EAClCuB,IAAI,EAAE7B,SAAS;EACf8B,OAAO,EAAEhC;AACX,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}