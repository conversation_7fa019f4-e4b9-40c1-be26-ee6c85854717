{"ast": null, "code": "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n    y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n  var popper = _ref2.popper,\n    popperRect = _ref2.popperRect,\n    placement = _ref2.placement,\n    variation = _ref2.variation,\n    offsets = _ref2.offsets,\n    position = _ref2.position,\n    gpuAcceleration = _ref2.gpuAcceleration,\n    adaptive = _ref2.adaptive,\n    roundOffsets = _ref2.roundOffsets,\n    isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n    x = _offsets$x === void 0 ? 0 : _offsets$x,\n    _offsets$y = offsets.y,\n    y = _offsets$y === void 0 ? 0 : _offsets$y;\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n    offsetParent = offsetParent;\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height :\n      // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width :\n      // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n  x = _ref4.x;\n  y = _ref4.y;\n  if (gpuAcceleration) {\n    var _Object$assign;\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n    options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n    gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n    _options$adaptive = options.adaptive,\n    adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n    _options$roundOffsets = options.roundOffsets,\n    roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "map": {"version": 3, "names": ["top", "left", "right", "bottom", "end", "getOffsetParent", "getWindow", "getDocumentElement", "getComputedStyle", "getBasePlacement", "getVariation", "round", "unsetSides", "roundOffsetsByDPR", "_ref", "x", "y", "win", "window", "dpr", "devicePixelRatio", "mapToStyles", "_ref2", "_Object$assign2", "popper", "popperRect", "placement", "variation", "offsets", "position", "gpuAcceleration", "adaptive", "roundOffsets", "isFixed", "_offsets$x", "_offsets$y", "_ref3", "hasX", "hasOwnProperty", "hasY", "sideX", "sideY", "offsetParent", "heightProp", "widthProp", "offsetY", "visualViewport", "height", "offsetX", "width", "commonStyles", "Object", "assign", "_ref4", "_Object$assign", "transform", "computeStyles", "_ref5", "state", "options", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "process", "env", "NODE_ENV", "transitionProperty", "elements", "some", "property", "indexOf", "console", "warn", "join", "rects", "strategy", "modifiersData", "popperOffsets", "styles", "arrow", "attributes", "name", "enabled", "phase", "fn", "data"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@popperjs/core/lib/modifiers/computeStyles.js"], "sourcesContent": ["import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,GAAG,QAAQ,aAAa;AAC3D,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,YAAY,MAAM,0BAA0B;AACnD,SAASC,KAAK,QAAQ,kBAAkB,CAAC,CAAC;;AAE1C,IAAIC,UAAU,GAAG;EACfZ,GAAG,EAAE,MAAM;EACXE,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,MAAM;EACdF,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;AACH;AACA;;AAEA,SAASY,iBAAiBA,CAACC,IAAI,EAAE;EAC/B,IAAIC,CAAC,GAAGD,IAAI,CAACC,CAAC;IACVC,CAAC,GAAGF,IAAI,CAACE,CAAC;EACd,IAAIC,GAAG,GAAGC,MAAM;EAChB,IAAIC,GAAG,GAAGF,GAAG,CAACG,gBAAgB,IAAI,CAAC;EACnC,OAAO;IACLL,CAAC,EAAEJ,KAAK,CAACI,CAAC,GAAGI,GAAG,CAAC,GAAGA,GAAG,IAAI,CAAC;IAC5BH,CAAC,EAAEL,KAAK,CAACK,CAAC,GAAGG,GAAG,CAAC,GAAGA,GAAG,IAAI;EAC7B,CAAC;AACH;AAEA,OAAO,SAASE,WAAWA,CAACC,KAAK,EAAE;EACjC,IAAIC,eAAe;EAEnB,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACrBC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,OAAO,GAAGN,KAAK,CAACM,OAAO;IACvBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,eAAe,GAAGR,KAAK,CAACQ,eAAe;IACvCC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,YAAY,GAAGV,KAAK,CAACU,YAAY;IACjCC,OAAO,GAAGX,KAAK,CAACW,OAAO;EAC3B,IAAIC,UAAU,GAAGN,OAAO,CAACb,CAAC;IACtBA,CAAC,GAAGmB,UAAU,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,UAAU;IAC1CC,UAAU,GAAGP,OAAO,CAACZ,CAAC;IACtBA,CAAC,GAAGmB,UAAU,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,UAAU;EAE9C,IAAIC,KAAK,GAAG,OAAOJ,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC;IAC5DjB,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA;EACL,CAAC,CAAC,GAAG;IACHD,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA;EACL,CAAC;EAEDD,CAAC,GAAGqB,KAAK,CAACrB,CAAC;EACXC,CAAC,GAAGoB,KAAK,CAACpB,CAAC;EACX,IAAIqB,IAAI,GAAGT,OAAO,CAACU,cAAc,CAAC,GAAG,CAAC;EACtC,IAAIC,IAAI,GAAGX,OAAO,CAACU,cAAc,CAAC,GAAG,CAAC;EACtC,IAAIE,KAAK,GAAGvC,IAAI;EAChB,IAAIwC,KAAK,GAAGzC,GAAG;EACf,IAAIiB,GAAG,GAAGC,MAAM;EAEhB,IAAIa,QAAQ,EAAE;IACZ,IAAIW,YAAY,GAAGrC,eAAe,CAACmB,MAAM,CAAC;IAC1C,IAAImB,UAAU,GAAG,cAAc;IAC/B,IAAIC,SAAS,GAAG,aAAa;IAE7B,IAAIF,YAAY,KAAKpC,SAAS,CAACkB,MAAM,CAAC,EAAE;MACtCkB,YAAY,GAAGnC,kBAAkB,CAACiB,MAAM,CAAC;MAEzC,IAAIhB,gBAAgB,CAACkC,YAAY,CAAC,CAACb,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,UAAU,EAAE;QACnFc,UAAU,GAAG,cAAc;QAC3BC,SAAS,GAAG,aAAa;MAC3B;IACF,CAAC,CAAC;;IAGFF,YAAY,GAAGA,YAAY;IAE3B,IAAIhB,SAAS,KAAK1B,GAAG,IAAI,CAAC0B,SAAS,KAAKzB,IAAI,IAAIyB,SAAS,KAAKxB,KAAK,KAAKyB,SAAS,KAAKvB,GAAG,EAAE;MACzFqC,KAAK,GAAGtC,MAAM;MACd,IAAI0C,OAAO,GAAGZ,OAAO,IAAIS,YAAY,KAAKzB,GAAG,IAAIA,GAAG,CAAC6B,cAAc,GAAG7B,GAAG,CAAC6B,cAAc,CAACC,MAAM;MAAG;MAClGL,YAAY,CAACC,UAAU,CAAC;MACxB3B,CAAC,IAAI6B,OAAO,GAAGpB,UAAU,CAACsB,MAAM;MAChC/B,CAAC,IAAIc,eAAe,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B;IAEA,IAAIJ,SAAS,KAAKzB,IAAI,IAAI,CAACyB,SAAS,KAAK1B,GAAG,IAAI0B,SAAS,KAAKvB,MAAM,KAAKwB,SAAS,KAAKvB,GAAG,EAAE;MAC1FoC,KAAK,GAAGtC,KAAK;MACb,IAAI8C,OAAO,GAAGf,OAAO,IAAIS,YAAY,KAAKzB,GAAG,IAAIA,GAAG,CAAC6B,cAAc,GAAG7B,GAAG,CAAC6B,cAAc,CAACG,KAAK;MAAG;MACjGP,YAAY,CAACE,SAAS,CAAC;MACvB7B,CAAC,IAAIiC,OAAO,GAAGvB,UAAU,CAACwB,KAAK;MAC/BlC,CAAC,IAAIe,eAAe,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B;EACF;EAEA,IAAIoB,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC;IAC/BvB,QAAQ,EAAEA;EACZ,CAAC,EAAEE,QAAQ,IAAInB,UAAU,CAAC;EAE1B,IAAIyC,KAAK,GAAGrB,YAAY,KAAK,IAAI,GAAGnB,iBAAiB,CAAC;IACpDE,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA;EACL,CAAC,CAAC,GAAG;IACHD,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA;EACL,CAAC;EAEDD,CAAC,GAAGsC,KAAK,CAACtC,CAAC;EACXC,CAAC,GAAGqC,KAAK,CAACrC,CAAC;EAEX,IAAIc,eAAe,EAAE;IACnB,IAAIwB,cAAc;IAElB,OAAOH,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,YAAY,GAAGI,cAAc,GAAG,CAAC,CAAC,EAAEA,cAAc,CAACb,KAAK,CAAC,GAAGF,IAAI,GAAG,GAAG,GAAG,EAAE,EAAEe,cAAc,CAACd,KAAK,CAAC,GAAGH,IAAI,GAAG,GAAG,GAAG,EAAE,EAAEiB,cAAc,CAACC,SAAS,GAAG,CAACtC,GAAG,CAACG,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG,YAAY,GAAGL,CAAC,GAAG,MAAM,GAAGC,CAAC,GAAG,KAAK,GAAG,cAAc,GAAGD,CAAC,GAAG,MAAM,GAAGC,CAAC,GAAG,QAAQ,EAAEsC,cAAc,EAAE;EACnT;EAEA,OAAOH,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,YAAY,GAAG3B,eAAe,GAAG,CAAC,CAAC,EAAEA,eAAe,CAACkB,KAAK,CAAC,GAAGF,IAAI,GAAGvB,CAAC,GAAG,IAAI,GAAG,EAAE,EAAEO,eAAe,CAACiB,KAAK,CAAC,GAAGH,IAAI,GAAGtB,CAAC,GAAG,IAAI,GAAG,EAAE,EAAEQ,eAAe,CAACgC,SAAS,GAAG,EAAE,EAAEhC,eAAe,EAAE;AAC/M;AAEA,SAASiC,aAAaA,CAACC,KAAK,EAAE;EAC5B,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IACnBC,OAAO,GAAGF,KAAK,CAACE,OAAO;EAC3B,IAAIC,qBAAqB,GAAGD,OAAO,CAAC7B,eAAe;IAC/CA,eAAe,GAAG8B,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IACjFC,iBAAiB,GAAGF,OAAO,CAAC5B,QAAQ;IACpCA,QAAQ,GAAG8B,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IAClEC,qBAAqB,GAAGH,OAAO,CAAC3B,YAAY;IAC5CA,YAAY,GAAG8B,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;EAElF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIC,kBAAkB,GAAG1D,gBAAgB,CAACkD,KAAK,CAACS,QAAQ,CAAC3C,MAAM,CAAC,CAAC0C,kBAAkB,IAAI,EAAE;IAEzF,IAAInC,QAAQ,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAACqC,IAAI,CAAC,UAAUC,QAAQ,EAAE;MACvF,OAAOH,kBAAkB,CAACI,OAAO,CAACD,QAAQ,CAAC,IAAI,CAAC;IAClD,CAAC,CAAC,EAAE;MACFE,OAAO,CAACC,IAAI,CAAC,CAAC,mEAAmE,EAAE,gEAAgE,EAAE,MAAM,EAAE,oEAAoE,EAAE,iEAAiE,EAAE,oEAAoE,EAAE,0CAA0C,EAAE,MAAM,EAAE,oEAAoE,EAAE,qEAAqE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzjB;EACF;EAEA,IAAIvB,YAAY,GAAG;IACjBxB,SAAS,EAAEjB,gBAAgB,CAACiD,KAAK,CAAChC,SAAS,CAAC;IAC5CC,SAAS,EAAEjB,YAAY,CAACgD,KAAK,CAAChC,SAAS,CAAC;IACxCF,MAAM,EAAEkC,KAAK,CAACS,QAAQ,CAAC3C,MAAM;IAC7BC,UAAU,EAAEiC,KAAK,CAACgB,KAAK,CAAClD,MAAM;IAC9BM,eAAe,EAAEA,eAAe;IAChCG,OAAO,EAAEyB,KAAK,CAACC,OAAO,CAACgB,QAAQ,KAAK;EACtC,CAAC;EAED,IAAIjB,KAAK,CAACkB,aAAa,CAACC,aAAa,IAAI,IAAI,EAAE;IAC7CnB,KAAK,CAACoB,MAAM,CAACtD,MAAM,GAAG2B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEM,KAAK,CAACoB,MAAM,CAACtD,MAAM,EAAEH,WAAW,CAAC8B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,YAAY,EAAE;MACvGtB,OAAO,EAAE8B,KAAK,CAACkB,aAAa,CAACC,aAAa;MAC1ChD,QAAQ,EAAE6B,KAAK,CAACC,OAAO,CAACgB,QAAQ;MAChC5C,QAAQ,EAAEA,QAAQ;MAClBC,YAAY,EAAEA;IAChB,CAAC,CAAC,CAAC,CAAC;EACN;EAEA,IAAI0B,KAAK,CAACkB,aAAa,CAACG,KAAK,IAAI,IAAI,EAAE;IACrCrB,KAAK,CAACoB,MAAM,CAACC,KAAK,GAAG5B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEM,KAAK,CAACoB,MAAM,CAACC,KAAK,EAAE1D,WAAW,CAAC8B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,YAAY,EAAE;MACrGtB,OAAO,EAAE8B,KAAK,CAACkB,aAAa,CAACG,KAAK;MAClClD,QAAQ,EAAE,UAAU;MACpBE,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAEA;IAChB,CAAC,CAAC,CAAC,CAAC;EACN;EAEA0B,KAAK,CAACsB,UAAU,CAACxD,MAAM,GAAG2B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEM,KAAK,CAACsB,UAAU,CAACxD,MAAM,EAAE;IACnE,uBAAuB,EAAEkC,KAAK,CAAChC;EACjC,CAAC,CAAC;AACJ,CAAC,CAAC;;AAGF,eAAe;EACbuD,IAAI,EAAE,eAAe;EACrBC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,aAAa;EACpBC,EAAE,EAAE5B,aAAa;EACjB6B,IAAI,EAAE,CAAC;AACT,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}