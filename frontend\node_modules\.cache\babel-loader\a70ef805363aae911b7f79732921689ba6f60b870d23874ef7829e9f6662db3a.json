{"ast": null, "code": "import { useCallback } from 'react';\nimport useCommittedRef from './useCommittedRef';\nexport default function useEventCallback(fn) {\n  var ref = useCommittedRef(fn);\n  return useCallback(function () {\n    return ref.current && ref.current.apply(ref, arguments);\n  }, [ref]);\n}", "map": {"version": 3, "names": ["useCallback", "useCommittedRef", "useEventCallback", "fn", "ref", "current", "apply", "arguments"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useEventCallback.js"], "sourcesContent": ["import { useCallback } from 'react';\nimport useCommittedRef from './useCommittedRef';\nexport default function useEventCallback(fn) {\n  var ref = useCommittedRef(fn);\n  return useCallback(function () {\n    return ref.current && ref.current.apply(ref, arguments);\n  }, [ref]);\n}"], "mappings": "AAAA,SAASA,WAAW,QAAQ,OAAO;AACnC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,eAAe,SAASC,gBAAgBA,CAACC,EAAE,EAAE;EAC3C,IAAIC,GAAG,GAAGH,eAAe,CAACE,EAAE,CAAC;EAC7B,OAAOH,WAAW,CAAC,YAAY;IAC7B,OAAOI,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAACC,KAAK,CAACF,GAAG,EAAEG,SAAS,CAAC;EACzD,CAAC,EAAE,CAACH,GAAG,CAAC,CAAC;AACX"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}