{"ast": null, "code": "function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { useCallback, useState } from 'react';\n\n/**\n * Mimics a React class component's state model, of having a single unified\n * `state` object and an updater that merges updates into the existing state, as\n * opposed to replacing it.\n *\n * ```js\n * const [state, setState] = useMergeState({ name: '<PERSON>', age: 24 })\n *\n * setState({ name: '<PERSON>' }) // { name: '<PERSON>', age: 24 }\n *\n * setState(state => ({ age: state.age + 10 })) // { name: '<PERSON>', age: 34 }\n * ```\n *\n * @param initialState The initial state object\n */\nexport default function useMergeState(initialState) {\n  var _useState = useState(initialState),\n    state = _useState[0],\n    setState = _useState[1];\n  var updater = useCallback(function (update) {\n    if (update === null) return;\n    if (typeof update === 'function') {\n      setState(function (state) {\n        var nextState = update(state);\n        return nextState == null ? state : _extends({}, state, nextState);\n      });\n    } else {\n      setState(function (state) {\n        return _extends({}, state, update);\n      });\n    }\n  }, [setState]);\n  return [state, updater];\n}", "map": {"version": 3, "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "useCallback", "useState", "useMergeState", "initialState", "_useState", "state", "setState", "updater", "update", "nextState"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useMergeState.js"], "sourcesContent": ["function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nimport { useCallback, useState } from 'react';\n\n/**\n * Mimics a React class component's state model, of having a single unified\n * `state` object and an updater that merges updates into the existing state, as\n * opposed to replacing it.\n *\n * ```js\n * const [state, setState] = useMergeState({ name: '<PERSON>', age: 24 })\n *\n * setState({ name: '<PERSON>' }) // { name: '<PERSON>', age: 24 }\n *\n * setState(state => ({ age: state.age + 10 })) // { name: '<PERSON>', age: 34 }\n * ```\n *\n * @param initialState The initial state object\n */\nexport default function useMergeState(initialState) {\n  var _useState = useState(initialState),\n      state = _useState[0],\n      setState = _useState[1];\n\n  var updater = useCallback(function (update) {\n    if (update === null) return;\n\n    if (typeof update === 'function') {\n      setState(function (state) {\n        var nextState = update(state);\n        return nextState == null ? state : _extends({}, state, nextState);\n      });\n    } else {\n      setState(function (state) {\n        return _extends({}, state, update);\n      });\n    }\n  }, [setState]);\n  return [state, updater];\n}"], "mappings": "AAAA,SAASA,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOH,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;AAAE;AAE5T,SAASQ,WAAW,EAAEC,QAAQ,QAAQ,OAAO;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,YAAY,EAAE;EAClD,IAAIC,SAAS,GAAGH,QAAQ,CAACE,YAAY,CAAC;IAClCE,KAAK,GAAGD,SAAS,CAAC,CAAC,CAAC;IACpBE,QAAQ,GAAGF,SAAS,CAAC,CAAC,CAAC;EAE3B,IAAIG,OAAO,GAAGP,WAAW,CAAC,UAAUQ,MAAM,EAAE;IAC1C,IAAIA,MAAM,KAAK,IAAI,EAAE;IAErB,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;MAChCF,QAAQ,CAAC,UAAUD,KAAK,EAAE;QACxB,IAAII,SAAS,GAAGD,MAAM,CAACH,KAAK,CAAC;QAC7B,OAAOI,SAAS,IAAI,IAAI,GAAGJ,KAAK,GAAGlB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAEI,SAAS,CAAC;MACnE,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,QAAQ,CAAC,UAAUD,KAAK,EAAE;QACxB,OAAOlB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAEG,MAAM,CAAC;MACpC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;EACd,OAAO,CAACD,KAAK,EAAEE,OAAO,CAAC;AACzB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}