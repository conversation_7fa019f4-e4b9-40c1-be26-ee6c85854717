{"ast": null, "code": "export default function format(str) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  return [].concat(args).reduce(function (p, c) {\n    return p.replace(/%s/, c);\n  }, str);\n}", "map": {"version": 3, "names": ["format", "str", "_len", "arguments", "length", "args", "Array", "_key", "concat", "reduce", "p", "c", "replace"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@popperjs/core/lib/utils/format.js"], "sourcesContent": ["export default function format(str) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n\n  return [].concat(args).reduce(function (p, c) {\n    return p.replace(/%s/, c);\n  }, str);\n}"], "mappings": "AAAA,eAAe,SAASA,MAAMA,CAACC,GAAG,EAAE;EAClC,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAClC;EAEA,OAAO,EAAE,CAACC,MAAM,CAACH,IAAI,CAAC,CAACI,MAAM,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAC5C,OAAOD,CAAC,CAACE,OAAO,CAAC,IAAI,EAAED,CAAC,CAAC;EAC3B,CAAC,EAAEV,GAAG,CA<PERSON>;AACT"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}