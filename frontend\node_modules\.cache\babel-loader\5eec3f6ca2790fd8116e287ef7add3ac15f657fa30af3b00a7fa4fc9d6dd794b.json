{"ast": null, "code": "import format from \"./format.js\";\nimport { modifierPhases } from \"../enums.js\";\nvar INVALID_MODIFIER_ERROR = 'Popper: modifier \"%s\" provided an invalid %s property, expected %s but got %s';\nvar MISSING_DEPENDENCY_ERROR = 'Popper: modifier \"%s\" requires \"%s\", but \"%s\" modifier is not available';\nvar VALID_PROPERTIES = ['name', 'enabled', 'phase', 'fn', 'effect', 'requires', 'options'];\nexport default function validateModifiers(modifiers) {\n  modifiers.forEach(function (modifier) {\n    [].concat(Object.keys(modifier), VALID_PROPERTIES) // IE11-compatible replacement for `new Set(iterable)`\n    .filter(function (value, index, self) {\n      return self.indexOf(value) === index;\n    }).forEach(function (key) {\n      switch (key) {\n        case 'name':\n          if (typeof modifier.name !== 'string') {\n            console.error(format(INVALID_MODIFIER_ERROR, String(modifier.name), '\"name\"', '\"string\"', \"\\\"\" + String(modifier.name) + \"\\\"\"));\n          }\n          break;\n        case 'enabled':\n          if (typeof modifier.enabled !== 'boolean') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"enabled\"', '\"boolean\"', \"\\\"\" + String(modifier.enabled) + \"\\\"\"));\n          }\n          break;\n        case 'phase':\n          if (modifierPhases.indexOf(modifier.phase) < 0) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"phase\"', \"either \" + modifierPhases.join(', '), \"\\\"\" + String(modifier.phase) + \"\\\"\"));\n          }\n          break;\n        case 'fn':\n          if (typeof modifier.fn !== 'function') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"fn\"', '\"function\"', \"\\\"\" + String(modifier.fn) + \"\\\"\"));\n          }\n          break;\n        case 'effect':\n          if (modifier.effect != null && typeof modifier.effect !== 'function') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"effect\"', '\"function\"', \"\\\"\" + String(modifier.fn) + \"\\\"\"));\n          }\n          break;\n        case 'requires':\n          if (modifier.requires != null && !Array.isArray(modifier.requires)) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"requires\"', '\"array\"', \"\\\"\" + String(modifier.requires) + \"\\\"\"));\n          }\n          break;\n        case 'requiresIfExists':\n          if (!Array.isArray(modifier.requiresIfExists)) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"requiresIfExists\"', '\"array\"', \"\\\"\" + String(modifier.requiresIfExists) + \"\\\"\"));\n          }\n          break;\n        case 'options':\n        case 'data':\n          break;\n        default:\n          console.error(\"PopperJS: an invalid property has been provided to the \\\"\" + modifier.name + \"\\\" modifier, valid properties are \" + VALID_PROPERTIES.map(function (s) {\n            return \"\\\"\" + s + \"\\\"\";\n          }).join(', ') + \"; but \\\"\" + key + \"\\\" was provided.\");\n      }\n      modifier.requires && modifier.requires.forEach(function (requirement) {\n        if (modifiers.find(function (mod) {\n          return mod.name === requirement;\n        }) == null) {\n          console.error(format(MISSING_DEPENDENCY_ERROR, String(modifier.name), requirement, requirement));\n        }\n      });\n    });\n  });\n}", "map": {"version": 3, "names": ["format", "modifierPhases", "INVALID_MODIFIER_ERROR", "MISSING_DEPENDENCY_ERROR", "VALID_PROPERTIES", "validateModifiers", "modifiers", "for<PERSON>ach", "modifier", "concat", "Object", "keys", "filter", "value", "index", "self", "indexOf", "key", "name", "console", "error", "String", "enabled", "phase", "join", "fn", "effect", "requires", "Array", "isArray", "requiresIfExists", "map", "s", "requirement", "find", "mod"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@popperjs/core/lib/utils/validateModifiers.js"], "sourcesContent": ["import format from \"./format.js\";\nimport { modifierPhases } from \"../enums.js\";\nvar INVALID_MODIFIER_ERROR = 'Popper: modifier \"%s\" provided an invalid %s property, expected %s but got %s';\nvar MISSING_DEPENDENCY_ERROR = 'Popper: modifier \"%s\" requires \"%s\", but \"%s\" modifier is not available';\nvar VALID_PROPERTIES = ['name', 'enabled', 'phase', 'fn', 'effect', 'requires', 'options'];\nexport default function validateModifiers(modifiers) {\n  modifiers.forEach(function (modifier) {\n    [].concat(Object.keys(modifier), VALID_PROPERTIES) // IE11-compatible replacement for `new Set(iterable)`\n    .filter(function (value, index, self) {\n      return self.indexOf(value) === index;\n    }).forEach(function (key) {\n      switch (key) {\n        case 'name':\n          if (typeof modifier.name !== 'string') {\n            console.error(format(INVALID_MODIFIER_ERROR, String(modifier.name), '\"name\"', '\"string\"', \"\\\"\" + String(modifier.name) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'enabled':\n          if (typeof modifier.enabled !== 'boolean') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"enabled\"', '\"boolean\"', \"\\\"\" + String(modifier.enabled) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'phase':\n          if (modifierPhases.indexOf(modifier.phase) < 0) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"phase\"', \"either \" + modifierPhases.join(', '), \"\\\"\" + String(modifier.phase) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'fn':\n          if (typeof modifier.fn !== 'function') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"fn\"', '\"function\"', \"\\\"\" + String(modifier.fn) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'effect':\n          if (modifier.effect != null && typeof modifier.effect !== 'function') {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"effect\"', '\"function\"', \"\\\"\" + String(modifier.fn) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'requires':\n          if (modifier.requires != null && !Array.isArray(modifier.requires)) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"requires\"', '\"array\"', \"\\\"\" + String(modifier.requires) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'requiresIfExists':\n          if (!Array.isArray(modifier.requiresIfExists)) {\n            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '\"requiresIfExists\"', '\"array\"', \"\\\"\" + String(modifier.requiresIfExists) + \"\\\"\"));\n          }\n\n          break;\n\n        case 'options':\n        case 'data':\n          break;\n\n        default:\n          console.error(\"PopperJS: an invalid property has been provided to the \\\"\" + modifier.name + \"\\\" modifier, valid properties are \" + VALID_PROPERTIES.map(function (s) {\n            return \"\\\"\" + s + \"\\\"\";\n          }).join(', ') + \"; but \\\"\" + key + \"\\\" was provided.\");\n      }\n\n      modifier.requires && modifier.requires.forEach(function (requirement) {\n        if (modifiers.find(function (mod) {\n          return mod.name === requirement;\n        }) == null) {\n          console.error(format(MISSING_DEPENDENCY_ERROR, String(modifier.name), requirement, requirement));\n        }\n      });\n    });\n  });\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAChC,SAASC,cAAc,QAAQ,aAAa;AAC5C,IAAIC,sBAAsB,GAAG,+EAA+E;AAC5G,IAAIC,wBAAwB,GAAG,yEAAyE;AACxG,IAAIC,gBAAgB,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;AAC1F,eAAe,SAASC,iBAAiBA,CAACC,SAAS,EAAE;EACnDA,SAAS,CAACC,OAAO,CAAC,UAAUC,QAAQ,EAAE;IACpC,EAAE,CAACC,MAAM,CAACC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,EAAEJ,gBAAgB,CAAC,CAAC;IAAA,CAClDQ,MAAM,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAE;MACpC,OAAOA,IAAI,CAACC,OAAO,CAACH,KAAK,CAAC,KAAKC,KAAK;IACtC,CAAC,CAAC,CAACP,OAAO,CAAC,UAAUU,GAAG,EAAE;MACxB,QAAQA,GAAG;QACT,KAAK,MAAM;UACT,IAAI,OAAOT,QAAQ,CAACU,IAAI,KAAK,QAAQ,EAAE;YACrCC,OAAO,CAACC,KAAK,CAACpB,MAAM,CAACE,sBAAsB,EAAEmB,MAAM,CAACb,QAAQ,CAACU,IAAI,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,GAAGG,MAAM,CAACb,QAAQ,CAACU,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;UACjI;UAEA;QAEF,KAAK,SAAS;UACZ,IAAI,OAAOV,QAAQ,CAACc,OAAO,KAAK,SAAS,EAAE;YACzCH,OAAO,CAACC,KAAK,CAACpB,MAAM,CAACE,sBAAsB,EAAEM,QAAQ,CAACU,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,IAAI,GAAGG,MAAM,CAACb,QAAQ,CAACc,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;UAChI;UAEA;QAEF,KAAK,OAAO;UACV,IAAIrB,cAAc,CAACe,OAAO,CAACR,QAAQ,CAACe,KAAK,CAAC,GAAG,CAAC,EAAE;YAC9CJ,OAAO,CAACC,KAAK,CAACpB,MAAM,CAACE,sBAAsB,EAAEM,QAAQ,CAACU,IAAI,EAAE,SAAS,EAAE,SAAS,GAAGjB,cAAc,CAACuB,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,GAAGH,MAAM,CAACb,QAAQ,CAACe,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;UACtJ;UAEA;QAEF,KAAK,IAAI;UACP,IAAI,OAAOf,QAAQ,CAACiB,EAAE,KAAK,UAAU,EAAE;YACrCN,OAAO,CAACC,KAAK,CAACpB,MAAM,CAACE,sBAAsB,EAAEM,QAAQ,CAACU,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,GAAGG,MAAM,CAACb,QAAQ,CAACiB,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;UACvH;UAEA;QAEF,KAAK,QAAQ;UACX,IAAIjB,QAAQ,CAACkB,MAAM,IAAI,IAAI,IAAI,OAAOlB,QAAQ,CAACkB,MAAM,KAAK,UAAU,EAAE;YACpEP,OAAO,CAACC,KAAK,CAACpB,MAAM,CAACE,sBAAsB,EAAEM,QAAQ,CAACU,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,GAAGG,MAAM,CAACb,QAAQ,CAACiB,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;UAC3H;UAEA;QAEF,KAAK,UAAU;UACb,IAAIjB,QAAQ,CAACmB,QAAQ,IAAI,IAAI,IAAI,CAACC,KAAK,CAACC,OAAO,CAACrB,QAAQ,CAACmB,QAAQ,CAAC,EAAE;YAClER,OAAO,CAACC,KAAK,CAACpB,MAAM,CAACE,sBAAsB,EAAEM,QAAQ,CAACU,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,GAAGG,MAAM,CAACb,QAAQ,CAACmB,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;UAChI;UAEA;QAEF,KAAK,kBAAkB;UACrB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACrB,QAAQ,CAACsB,gBAAgB,CAAC,EAAE;YAC7CX,OAAO,CAACC,KAAK,CAACpB,MAAM,CAACE,sBAAsB,EAAEM,QAAQ,CAACU,IAAI,EAAE,oBAAoB,EAAE,SAAS,EAAE,IAAI,GAAGG,MAAM,CAACb,QAAQ,CAACsB,gBAAgB,CAAC,GAAG,IAAI,CAAC,CAAC;UAChJ;UAEA;QAEF,KAAK,SAAS;QACd,KAAK,MAAM;UACT;QAEF;UACEX,OAAO,CAACC,KAAK,CAAC,2DAA2D,GAAGZ,QAAQ,CAACU,IAAI,GAAG,oCAAoC,GAAGd,gBAAgB,CAAC2B,GAAG,CAAC,UAAUC,CAAC,EAAE;YACnK,OAAO,IAAI,GAAGA,CAAC,GAAG,IAAI;UACxB,CAAC,CAAC,CAACR,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU,GAAGP,GAAG,GAAG,kBAAkB,CAAC;MAAC;MAG3DT,QAAQ,CAACmB,QAAQ,IAAInB,QAAQ,CAACmB,QAAQ,CAACpB,OAAO,CAAC,UAAU0B,WAAW,EAAE;QACpE,IAAI3B,SAAS,CAAC4B,IAAI,CAAC,UAAUC,GAAG,EAAE;UAChC,OAAOA,GAAG,CAACjB,IAAI,KAAKe,WAAW;QACjC,CAAC,CAAC,IAAI,IAAI,EAAE;UACVd,OAAO,CAACC,KAAK,CAACpB,MAAM,CAACG,wBAAwB,EAAEkB,MAAM,CAACb,QAAQ,CAACU,IAAI,CAAC,EAAEe,WAAW,EAAEA,WAAW,CAAC,CAAC;QAClG;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}