{"ast": null, "code": "import * as React from 'react';\nimport usePlaceholder from './usePlaceholder';\nimport PlaceholderButton from './PlaceholderButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Placeholder = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    as: Component = 'span',\n    ...props\n  } = _ref;\n  const placeholderProps = usePlaceholder(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...placeholderProps,\n    ref: ref\n  });\n});\nPlaceholder.displayName = 'Placeholder';\nexport default Object.assign(Placeholder, {\n  Button: PlaceholderButton\n});", "map": {"version": 3, "names": ["React", "usePlaceholder", "Placeholder<PERSON><PERSON><PERSON>", "jsx", "_jsx", "Placeholder", "forwardRef", "_ref", "ref", "as", "Component", "props", "placeholderProps", "displayName", "Object", "assign", "<PERSON><PERSON>"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Placeholder.js"], "sourcesContent": ["import * as React from 'react';\nimport usePlaceholder from './usePlaceholder';\nimport PlaceholderButton from './PlaceholderButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Placeholder = /*#__PURE__*/React.forwardRef(({\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const placeholderProps = usePlaceholder(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...placeholderProps,\n    ref: ref\n  });\n});\nPlaceholder.displayName = 'Placeholder';\nexport default Object.assign(Placeholder, {\n  Button: PlaceholderButton\n});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAAC,IAAA,EAG/CC,GAAG,KAAK;EAAA,IAHwC;IACjDC,EAAE,EAAEC,SAAS,GAAG,MAAM;IACtB,GAAGC;EACL,CAAC,GAAAJ,IAAA;EACC,MAAMK,gBAAgB,GAAGX,cAAc,CAACU,KAAK,CAAC;EAC9C,OAAO,aAAaP,IAAI,CAACM,SAAS,EAAE;IAClC,GAAGE,gBAAgB;IACnBJ,GAAG,EAAEA;EACP,CAAC,CAAC;AACJ,CAAC,CAAC;AACFH,WAAW,CAACQ,WAAW,GAAG,aAAa;AACvC,eAAeC,MAAM,CAACC,MAAM,CAACV,WAAW,EAAE;EACxCW,MAAM,EAAEd;AACV,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}