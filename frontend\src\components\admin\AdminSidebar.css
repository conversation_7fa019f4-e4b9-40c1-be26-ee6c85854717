.admin-sidebar {
  width: 280px;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: fixed;
  left: 0;
  top: 0;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-details h6 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.status {
  font-size: 12px;
  opacity: 0.8;
}

.status.online {
  color: #4CAF50;
}

.sidebar-content {
  padding: 20px 0;
}

.section-title {
  padding: 0 20px 10px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.7;
  margin-bottom: 10px;
}

.sidebar-link {
  display: flex !important;
  align-items: center;
  padding: 12px 20px !important;
  color: rgba(255, 255, 255, 0.8) !important;
  text-decoration: none !important;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  background: none;
}

.sidebar-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white !important;
}

.sidebar-link.active {
  background: rgba(255, 255, 255, 0.15);
  color: white !important;
  border-right: 3px solid #fff;
}

.sidebar-link i {
  width: 20px;
  margin-right: 12px;
  text-align: center;
}

.submenu {
  background: rgba(0, 0, 0, 0.1);
  padding: 5px 0;
}

.submenu-link {
  padding: 8px 20px 8px 52px !important;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7) !important;
  text-decoration: none !important;
  display: block;
  transition: all 0.3s ease;
}

.submenu-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white !important;
}

.submenu-link.active {
  background: rgba(255, 255, 255, 0.2);
  color: white !important;
}

.sidebar-footer {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
}

.weather-widget {
  background: rgba(255, 255, 255, 0.1);
  padding: 10px;
  border-radius: 8px;
  text-align: center;
  font-size: 12px;
}

.weather-widget i {
  margin-right: 8px;
  color: #FFD700;
}

/* Scrollbar styling */
.admin-sidebar::-webkit-scrollbar {
  width: 4px;
}

.admin-sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.admin-sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 100%;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .admin-sidebar.open {
    transform: translateX(0);
  }
}
