{"ast": null, "code": "/**\n * Get the width of the vertical window scrollbar if it's visible\n */\nexport default function getBodyScrollbarWidth() {\n  let ownerDocument = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  const window = ownerDocument.defaultView;\n  return Math.abs(window.innerWidth - ownerDocument.documentElement.clientWidth);\n}", "map": {"version": 3, "names": ["getBodyScrollbarWidth", "ownerDocument", "arguments", "length", "undefined", "document", "window", "defaultView", "Math", "abs", "innerWidth", "documentElement", "clientWidth"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/ui/esm/getScrollbarWidth.js"], "sourcesContent": ["/**\n * Get the width of the vertical window scrollbar if it's visible\n */\nexport default function getBodyScrollbarWidth(ownerDocument = document) {\n  const window = ownerDocument.defaultView;\n  return Math.abs(window.innerWidth - ownerDocument.documentElement.clientWidth);\n}"], "mappings": "AAAA;AACA;AACA;AACA,eAAe,SAASA,qBAAqBA,CAAA,EAA2B;EAAA,IAA1BC,aAAa,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGG,QAAQ;EACpE,MAAMC,MAAM,GAAGL,aAAa,CAACM,WAAW;EACxC,OAAOC,IAAI,CAACC,GAAG,CAACH,MAAM,CAACI,UAAU,GAAGT,aAAa,CAACU,eAAe,CAACC,WAAW,CAAC;AAChF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}