{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminLayout.jsx\";\nimport React from 'react';\nimport AdminSidebar from './AdminSidebar';\nimport AdminHeader from './AdminHeader';\nimport './AdminLayout.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminLayout = _ref => {\n  let {\n    children\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-layout\",\n    children: [/*#__PURE__*/_jsxDEV(AdminSidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-content\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = AdminLayout;\nexport default AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");", "map": {"version": 3, "names": ["React", "AdminSidebar", "Ad<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "AdminLayout", "_ref", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/admin/AdminLayout.jsx"], "sourcesContent": ["import React from 'react';\nimport AdminSidebar from './AdminSidebar';\nimport AdminHeader from './AdminHeader';\nimport './AdminLayout.css';\n\nconst AdminLayout = ({ children }) => {\n  return (\n    <div className=\"admin-layout\">\n      <AdminSidebar />\n      <div className=\"admin-main\">\n        {/* <AdminHeader /> */}\n        <div className=\"admin-content\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminLayout;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGC,IAAA,IAAkB;EAAA,IAAjB;IAAEC;EAAS,CAAC,GAAAD,IAAA;EAC/B,oBACEF,OAAA;IAAKI,SAAS,EAAC,cAAc;IAAAD,QAAA,gBAC3BH,OAAA,CAACH,YAAY;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG,eAChBR,OAAA;MAAKI,SAAS,EAAC,YAAY;MAAAD,QAAA,eAEzBH,OAAA;QAAKI,SAAS,EAAC,eAAe;QAAAD,QAAA,EAC3BA;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACL;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACF;AAEV,CAAC;AAACC,EAAA,GAZIR,WAAW;AAcjB,eAAeA,WAAW;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}