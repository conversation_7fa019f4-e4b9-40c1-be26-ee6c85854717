{"ast": null, "code": "import * as React from 'react';\nimport { useContext } from 'react';\nimport Offcanvas from './Offcanvas';\nimport NavbarContext from './NavbarContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarOffcanvas = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const context = useContext(NavbarContext);\n  return /*#__PURE__*/_jsx(Offcanvas, {\n    ref: ref,\n    show: !!(context != null && context.expanded),\n    ...props,\n    renderStaticNode: true\n  });\n});\nNavbarOffcanvas.displayName = 'NavbarOffcanvas';\nexport default NavbarOffcanvas;", "map": {"version": 3, "names": ["React", "useContext", "<PERSON><PERSON><PERSON>", "NavbarContext", "jsx", "_jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "props", "ref", "context", "show", "expanded", "renderStaticNode", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/NavbarOffcanvas.js"], "sourcesContent": ["import * as React from 'react';\nimport { useContext } from 'react';\nimport Offcanvas from './Offcanvas';\nimport NavbarContext from './NavbarContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarOffcanvas = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const context = useContext(NavbarContext);\n  return /*#__PURE__*/_jsx(Offcanvas, {\n    ref: ref,\n    show: !!(context != null && context.expanded),\n    ...props,\n    renderStaticNode: true\n  });\n});\nNavbarOffcanvas.displayName = 'NavbarOffcanvas';\nexport default NavbarOffcanvas;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,eAAe,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACpE,MAAMC,OAAO,GAAGT,UAAU,CAACE,aAAa,CAAC;EACzC,OAAO,aAAaE,IAAI,CAACH,SAAS,EAAE;IAClCO,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAE,CAAC,EAAED,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACE,QAAQ,CAAC;IAC7C,GAAGJ,KAAK;IACRK,gBAAgB,EAAE;EACpB,CAAC,CAAC;AACJ,CAAC,CAAC;AACFP,eAAe,CAACQ,WAAW,GAAG,iBAAiB;AAC/C,eAAeR,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}