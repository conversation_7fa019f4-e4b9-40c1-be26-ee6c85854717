import React from 'react';
import { Navbar, Nav, Dropdown, Badge } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import './AdminHeader.css';

const AdminHeader = () => {
  return (
    <Navbar className="admin-header" expand="lg">
      <div className="admin-header-content">
        <div className="header-left">
          <button className="sidebar-toggle">
            <i className="fas fa-bars"></i>
          </button>
          
          <div className="logo">
            <i className="fas fa-circle" style={{ color: '#00BCD4' }}></i>
            <span className="logo-text">Pluto</span>
          </div>
        </div>

        <div className="header-center">
          <h4 className="page-title">Dashboard</h4>
        </div>

        <div className="header-right">
          <Nav className="header-nav">
            <Nav.Item className="nav-item-icon">
              <Nav.Link href="#" className="nav-icon">
                <i className="fas fa-bell"></i>
                <Badge bg="danger" className="notification-badge">3</Badge>
              </Nav.Link>
            </Nav.Item>

            <Nav.Item className="nav-item-icon">
              <Nav.Link href="#" className="nav-icon">
                <i className="fas fa-question-circle"></i>
              </Nav.Link>
            </Nav.Item>

            <Nav.Item className="nav-item-icon">
              <Nav.Link href="#" className="nav-icon">
                <i className="fas fa-envelope"></i>
                <Badge bg="danger" className="notification-badge">1</Badge>
              </Nav.Link>
            </Nav.Item>

            <Dropdown align="end">
              <Dropdown.Toggle 
                variant="link" 
                className="user-dropdown"
                id="user-dropdown"
              >
                <img 
                  src="/api/placeholder/32/32" 
                  alt="User" 
                  className="user-avatar"
                />
                <span className="user-name">John David</span>
                <i className="fas fa-chevron-down"></i>
              </Dropdown.Toggle>

              <Dropdown.Menu className="user-menu">
                <Dropdown.Item as={Link} to="/admin/profile">
                  <i className="fas fa-user"></i>
                  Profile
                </Dropdown.Item>
                <Dropdown.Item as={Link} to="/admin/settings">
                  <i className="fas fa-cog"></i>
                  Settings
                </Dropdown.Item>
                <Dropdown.Divider />
                <Dropdown.Item as={Link} to="/logout">
                  <i className="fas fa-sign-out-alt"></i>
                  Logout
                </Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>
          </Nav>
        </div>
      </div>
    </Navbar>
  );
};

export default AdminHeader;
