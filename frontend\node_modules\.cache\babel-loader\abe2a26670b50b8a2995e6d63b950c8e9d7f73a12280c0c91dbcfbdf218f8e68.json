{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useButtonProps } from '@restart/ui/Button';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  variant: 'primary',\n  active: false,\n  disabled: false\n};\nconst Button = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    as,\n    bsPrefix,\n    variant,\n    size,\n    active,\n    className,\n    ...props\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn');\n  const [buttonProps, {\n    tagName\n  }] = useButtonProps({\n    tagName: as,\n    ...props\n  });\n  const Component = tagName;\n  return /*#__PURE__*/_jsx(Component, {\n    ...buttonProps,\n    ...props,\n    ref: ref,\n    className: classNames(className, prefix, active && 'active', variant && `${prefix}-${variant}`, size && `${prefix}-${size}`, props.href && props.disabled && 'disabled')\n  });\n});\nButton.displayName = 'Button';\nButton.defaultProps = defaultProps;\nexport default Button;", "map": {"version": 3, "names": ["classNames", "React", "useButtonProps", "useBootstrapPrefix", "jsx", "_jsx", "defaultProps", "variant", "active", "disabled", "<PERSON><PERSON>", "forwardRef", "_ref", "ref", "as", "bsPrefix", "size", "className", "props", "prefix", "buttonProps", "tagName", "Component", "href", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Button.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useButtonProps } from '@restart/ui/Button';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  variant: 'primary',\n  active: false,\n  disabled: false\n};\nconst Button = /*#__PURE__*/React.forwardRef(({\n  as,\n  bsPrefix,\n  variant,\n  size,\n  active,\n  className,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn');\n  const [buttonProps, {\n    tagName\n  }] = useButtonProps({\n    tagName: as,\n    ...props\n  });\n  const Component = tagName;\n  return /*#__PURE__*/_jsx(Component, {\n    ...buttonProps,\n    ...props,\n    ref: ref,\n    className: classNames(className, prefix, active && 'active', variant && `${prefix}-${variant}`, size && `${prefix}-${size}`, props.href && props.disabled && 'disabled')\n  });\n});\nButton.displayName = 'Button';\nButton.defaultProps = defaultProps;\nexport default Button;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,KAAK;EACbC,QAAQ,EAAE;AACZ,CAAC;AACD,MAAMC,MAAM,GAAG,aAAaT,KAAK,CAACU,UAAU,CAAC,CAAAC,IAAA,EAQ1CC,GAAG,KAAK;EAAA,IARmC;IAC5CC,EAAE;IACFC,QAAQ;IACRR,OAAO;IACPS,IAAI;IACJR,MAAM;IACNS,SAAS;IACT,GAAGC;EACL,CAAC,GAAAN,IAAA;EACC,MAAMO,MAAM,GAAGhB,kBAAkB,CAACY,QAAQ,EAAE,KAAK,CAAC;EAClD,MAAM,CAACK,WAAW,EAAE;IAClBC;EACF,CAAC,CAAC,GAAGnB,cAAc,CAAC;IAClBmB,OAAO,EAAEP,EAAE;IACX,GAAGI;EACL,CAAC,CAAC;EACF,MAAMI,SAAS,GAAGD,OAAO;EACzB,OAAO,aAAahB,IAAI,CAACiB,SAAS,EAAE;IAClC,GAAGF,WAAW;IACd,GAAGF,KAAK;IACRL,GAAG,EAAEA,GAAG;IACRI,SAAS,EAAEjB,UAAU,CAACiB,SAAS,EAAEE,MAAM,EAAEX,MAAM,IAAI,QAAQ,EAAED,OAAO,IAAK,GAAEY,MAAO,IAAGZ,OAAQ,EAAC,EAAES,IAAI,IAAK,GAAEG,MAAO,IAAGH,IAAK,EAAC,EAAEE,KAAK,CAACK,IAAI,IAAIL,KAAK,CAACT,QAAQ,IAAI,UAAU;EACzK,CAAC,CAAC;AACJ,CAAC,CAAC;AACFC,MAAM,CAACc,WAAW,GAAG,QAAQ;AAC7Bd,MAAM,CAACJ,YAAY,GAAGA,YAAY;AAClC,eAAeI,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}