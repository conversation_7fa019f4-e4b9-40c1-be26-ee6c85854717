{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\pages\\\\loginPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport { Link, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { Form, Button, Row, Col } from \"react-bootstrap\";\nimport Message from \"../components/message\";\nimport UserContext from \"../context/userContext\";\nimport FormContainer from \"../components/formContainer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LoginPage(props) {\n  _s();\n  const [username, setUsername] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const {\n    userInfo,\n    login,\n    error\n  } = useContext(UserContext);\n  const navigate = useNavigate();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const redirect = searchParams.get('redirect') ? \"/\" + searchParams.get('redirect') : \"/\";\n  useEffect(() => {\n    if (userInfo && userInfo.username) {\n      // Redirect admin users to admin dashboard\n      if (userInfo.isAdmin) {\n        navigate('/admin');\n      } else {\n        navigate(redirect);\n      }\n    }\n  }, []);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const status = await login(username, password);\n    if (status) navigate(redirect);\n  };\n  return /*#__PURE__*/_jsxDEV(FormContainer, {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Sign In\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), error.login && error.login.detail && /*#__PURE__*/_jsxDEV(Message, {\n      variant: \"danger\",\n      children: /*#__PURE__*/_jsxDEV(\"h4\", {\n        children: error.login.detail\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n        controlId: \"username\",\n        className: \"my-2\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n          type: \"text\",\n          placeholder: \"Enter Username\",\n          value: username,\n          onChange: e => {\n            setUsername(e.currentTarget.value);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n          children: error.login && error.login.username && /*#__PURE__*/_jsxDEV(Message, {\n            variant: \"danger\",\n            children: error.login.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n        controlId: \"password\",\n        className: \"my-2\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n          type: \"password\",\n          placeholder: \"Enter Password\",\n          value: password,\n          onChange: e => {\n            setPassword(e.currentTarget.value);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n          children: error.login && error.login.password && /*#__PURE__*/_jsxDEV(Message, {\n            variant: \"danger\",\n            children: error.login.password\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"submit\",\n        variant: \"primary\",\n        className: \"my-2\",\n        children: \"Sign In\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"py-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [\"New Customer?\", /*#__PURE__*/_jsxDEV(Link, {\n          to: redirect ? `/register?redirect=${redirect}` : \"/register\",\n          children: \"Register\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n}\n_s(LoginPage, \"ICnc7bfm2lBJAshUJhsXEjSeIL8=\", false, function () {\n  return [useNavigate, useSearchParams];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "Link", "useNavigate", "useSearchParams", "Form", "<PERSON><PERSON>", "Row", "Col", "Message", "UserContext", "FormContainer", "jsxDEV", "_jsxDEV", "LoginPage", "props", "_s", "username", "setUsername", "password", "setPassword", "userInfo", "login", "error", "navigate", "searchParams", "setSearchParams", "redirect", "get", "isAdmin", "handleSubmit", "e", "preventDefault", "status", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "detail", "variant", "onSubmit", "Group", "controlId", "className", "Label", "Control", "type", "placeholder", "value", "onChange", "currentTarget", "Text", "to", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/pages/loginPage.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\nimport { Link, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { <PERSON>, Button, Row, Col } from \"react-bootstrap\";\nimport Message from \"../components/message\";\nimport UserContext from \"../context/userContext\";\nimport FormContainer from \"../components/formContainer\";\n\nfunction LoginPage(props) {\n  const [username, setUsername] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const { userInfo, login, error } = useContext(UserContext);\n  const navigate = useNavigate();\n  const [searchParams,setSearchParams] = useSearchParams()\n  const redirect = searchParams.get('redirect')\n    ? \"/\" + searchParams.get('redirect')\n    : \"/\";\n\n  useEffect(() => {\n    if (userInfo && userInfo.username) {\n      // Redirect admin users to admin dashboard\n      if (userInfo.isAdmin) {\n        navigate('/admin');\n      } else {\n        navigate(redirect);\n      }\n    }\n  }, []);\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    const status = await login(username, password);\n    if (status) navigate(redirect);\n  };\n\n  return (\n    <FormContainer>\n      <h1>Sign In</h1>\n      {error.login && error.login.detail && (\n        <Message variant=\"danger\">\n          <h4>{error.login.detail}</h4>\n        </Message>\n      )}\n      <Form onSubmit={handleSubmit}>\n        <Form.Group controlId=\"username\" className=\"my-2\">\n          <Form.Label>Username</Form.Label>\n          <Form.Control\n            type=\"text\"\n            placeholder=\"Enter Username\"\n            value={username}\n            onChange={(e) => {\n              setUsername(e.currentTarget.value);\n            }}\n          ></Form.Control>\n          <Form.Text>\n            {error.login && error.login.username && (\n              <Message variant=\"danger\">{error.login.username}</Message>\n            )}\n          </Form.Text>\n        </Form.Group>\n        <Form.Group controlId=\"password\" className=\"my-2\">\n          <Form.Label>Password</Form.Label>\n          <Form.Control\n            type=\"password\"\n            placeholder=\"Enter Password\"\n            value={password}\n            onChange={(e) => {\n              setPassword(e.currentTarget.value);\n            }}\n          ></Form.Control>\n          <Form.Text>\n            {error.login && error.login.password && (\n              <Message variant=\"danger\">{error.login.password}</Message>\n            )}\n          </Form.Text>\n        </Form.Group>\n        <Button type=\"submit\" variant=\"primary\" className=\"my-2\">\n          Sign In\n        </Button>\n      </Form>\n      <Row className=\"py-3\">\n        <Col>\n          New Customer?\n          <Link to={redirect ? `/register?redirect=${redirect}` : \"/register\"}>\n            Register\n          </Link>\n        </Col>\n      </Row>\n    </FormContainer>\n  );\n}\n\nexport default LoginPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,IAAI,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AACrE,SAASC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AACxD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,aAAa,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,SAASC,SAASA,CAACC,KAAK,EAAE;EAAAC,EAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM;IAAEsB,QAAQ;IAAEC,KAAK;IAAEC;EAAM,CAAC,GAAGvB,UAAU,CAACU,WAAW,CAAC;EAC1D,MAAMc,QAAQ,GAAGrB,WAAW,EAAE;EAC9B,MAAM,CAACsB,YAAY,EAACC,eAAe,CAAC,GAAGtB,eAAe,EAAE;EACxD,MAAMuB,QAAQ,GAAGF,YAAY,CAACG,GAAG,CAAC,UAAU,CAAC,GACzC,GAAG,GAAGH,YAAY,CAACG,GAAG,CAAC,UAAU,CAAC,GAClC,GAAG;EAEP3B,SAAS,CAAC,MAAM;IACd,IAAIoB,QAAQ,IAAIA,QAAQ,CAACJ,QAAQ,EAAE;MACjC;MACA,IAAII,QAAQ,CAACQ,OAAO,EAAE;QACpBL,QAAQ,CAAC,QAAQ,CAAC;MACpB,CAAC,MAAM;QACLA,QAAQ,CAACG,QAAQ,CAAC;MACpB;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,EAAE;IAClB,MAAMC,MAAM,GAAG,MAAMX,KAAK,CAACL,QAAQ,EAAEE,QAAQ,CAAC;IAC9C,IAAIc,MAAM,EAAET,QAAQ,CAACG,QAAQ,CAAC;EAChC,CAAC;EAED,oBACEd,OAAA,CAACF,aAAa;IAAAuB,QAAA,gBACZrB,OAAA;MAAAqB,QAAA,EAAI;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAK,EACff,KAAK,CAACD,KAAK,IAAIC,KAAK,CAACD,KAAK,CAACiB,MAAM,iBAChC1B,OAAA,CAACJ,OAAO;MAAC+B,OAAO,EAAC,QAAQ;MAAAN,QAAA,eACvBrB,OAAA;QAAAqB,QAAA,EAAKX,KAAK,CAACD,KAAK,CAACiB;MAAM;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAM;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAEhC,eACDzB,OAAA,CAACR,IAAI;MAACoC,QAAQ,EAAEX,YAAa;MAAAI,QAAA,gBAC3BrB,OAAA,CAACR,IAAI,CAACqC,KAAK;QAACC,SAAS,EAAC,UAAU;QAACC,SAAS,EAAC,MAAM;QAAAV,QAAA,gBAC/CrB,OAAA,CAACR,IAAI,CAACwC,KAAK;UAAAX,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACjCzB,OAAA,CAACR,IAAI,CAACyC,OAAO;UACXC,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,gBAAgB;UAC5BC,KAAK,EAAEhC,QAAS;UAChBiC,QAAQ,EAAGnB,CAAC,IAAK;YACfb,WAAW,CAACa,CAAC,CAACoB,aAAa,CAACF,KAAK,CAAC;UACpC;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACY,eAChBzB,OAAA,CAACR,IAAI,CAAC+C,IAAI;UAAAlB,QAAA,EACPX,KAAK,CAACD,KAAK,IAAIC,KAAK,CAACD,KAAK,CAACL,QAAQ,iBAClCJ,OAAA,CAACJ,OAAO;YAAC+B,OAAO,EAAC,QAAQ;YAAAN,QAAA,EAAEX,KAAK,CAACD,KAAK,CAACL;UAAQ;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAChD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACS;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD,eACbzB,OAAA,CAACR,IAAI,CAACqC,KAAK;QAACC,SAAS,EAAC,UAAU;QAACC,SAAS,EAAC,MAAM;QAAAV,QAAA,gBAC/CrB,OAAA,CAACR,IAAI,CAACwC,KAAK;UAAAX,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACjCzB,OAAA,CAACR,IAAI,CAACyC,OAAO;UACXC,IAAI,EAAC,UAAU;UACfC,WAAW,EAAC,gBAAgB;UAC5BC,KAAK,EAAE9B,QAAS;UAChB+B,QAAQ,EAAGnB,CAAC,IAAK;YACfX,WAAW,CAACW,CAAC,CAACoB,aAAa,CAACF,KAAK,CAAC;UACpC;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACY,eAChBzB,OAAA,CAACR,IAAI,CAAC+C,IAAI;UAAAlB,QAAA,EACPX,KAAK,CAACD,KAAK,IAAIC,KAAK,CAACD,KAAK,CAACH,QAAQ,iBAClCN,OAAA,CAACJ,OAAO;YAAC+B,OAAO,EAAC,QAAQ;YAAAN,QAAA,EAAEX,KAAK,CAACD,KAAK,CAACH;UAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAChD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACS;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD,eACbzB,OAAA,CAACP,MAAM;QAACyC,IAAI,EAAC,QAAQ;QAACP,OAAO,EAAC,SAAS;QAACI,SAAS,EAAC,MAAM;QAAAV,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAS;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACJ,eACPzB,OAAA,CAACN,GAAG;MAACqC,SAAS,EAAC,MAAM;MAAAV,QAAA,eACnBrB,OAAA,CAACL,GAAG;QAAA0B,QAAA,GAAC,eAEH,eAAArB,OAAA,CAACX,IAAI;UAACmD,EAAE,EAAE1B,QAAQ,GAAI,sBAAqBA,QAAS,EAAC,GAAG,WAAY;UAAAO,QAAA,EAAC;QAErE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAO;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACQ;AAEpB;AAACtB,EAAA,CAlFQF,SAAS;EAAA,QAICX,WAAW,EACWC,eAAe;AAAA;AAAAkD,EAAA,GAL/CxC,SAAS;AAoFlB,eAAeA,SAAS;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}