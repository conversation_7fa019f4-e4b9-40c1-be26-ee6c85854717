{"ast": null, "code": "import { useRef } from 'react';\n/**\n * Returns a ref that is immediately updated with the new value\n *\n * @param value The Ref value\n * @category refs\n */\n\nexport default function useUpdatedRef(value) {\n  var valueRef = useRef(value);\n  valueRef.current = value;\n  return valueRef;\n}", "map": {"version": 3, "names": ["useRef", "useUpdatedRef", "value", "valueRef", "current"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useUpdatedRef.js"], "sourcesContent": ["import { useRef } from 'react';\n/**\n * Returns a ref that is immediately updated with the new value\n *\n * @param value The Ref value\n * @category refs\n */\n\nexport default function useUpdatedRef(value) {\n  var valueRef = useRef(value);\n  valueRef.current = value;\n  return valueRef;\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC3C,IAAIC,QAAQ,GAAGH,MAAM,CAACE,KAAK,CAAC;EAC5BC,QAAQ,CAACC,OAAO,GAAGF,KAAK;EACxB,OAAOC,QAAQ;AACjB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}