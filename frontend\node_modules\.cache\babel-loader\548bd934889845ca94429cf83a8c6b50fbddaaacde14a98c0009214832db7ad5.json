{"ast": null, "code": "import * as React from 'react';\nconst ToastContext = /*#__PURE__*/React.createContext({\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClose() {}\n});\nexport default ToastContext;", "map": {"version": 3, "names": ["React", "ToastContext", "createContext", "onClose"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/ToastContext.js"], "sourcesContent": ["import * as React from 'react';\nconst ToastContext = /*#__PURE__*/React.createContext({\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  onClose() {}\n});\nexport default ToastContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,YAAY,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC;EACpD;EACAC,OAAOA,CAAA,EAAG,CAAC;AACb,CAAC,CAAC;AACF,eAAeF,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}