{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext } from 'react';\nimport { Navigate } from 'react-router-dom';\nimport UserContext from '../context/userContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = _ref => {\n  _s();\n  let {\n    children,\n    adminOnly = false\n  } = _ref;\n  const {\n    userInfo\n  } = useContext(UserContext);\n\n  // Check if user is logged in\n  if (!userInfo || !userInfo.username) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check if admin access is required\n  if (adminOnly && !userInfo.isAdmin) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"/KD1s1jRBq3X+az2/mCvD2j7GYM=\");\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "useContext", "Navigate", "UserContext", "jsxDEV", "_jsxDEV", "ProtectedRoute", "_ref", "_s", "children", "adminOnly", "userInfo", "username", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isAdmin", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/ProtectedRoute.jsx"], "sourcesContent": ["import React, { useContext } from 'react';\nimport { Navigate } from 'react-router-dom';\nimport UserContext from '../context/userContext';\n\nconst ProtectedRoute = ({ children, adminOnly = false }) => {\n  const { userInfo } = useContext(UserContext);\n\n  // Check if user is logged in\n  if (!userInfo || !userInfo.username) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // Check if admin access is required\n  if (adminOnly && !userInfo.isAdmin) {\n    return <Navigate to=\"/\" replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,WAAW,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,cAAc,GAAGC,IAAA,IAAqC;EAAAC,EAAA;EAAA,IAApC;IAAEC,QAAQ;IAAEC,SAAS,GAAG;EAAM,CAAC,GAAAH,IAAA;EACrD,MAAM;IAAEI;EAAS,CAAC,GAAGV,UAAU,CAACE,WAAW,CAAC;;EAE5C;EACA,IAAI,CAACQ,QAAQ,IAAI,CAACA,QAAQ,CAACC,QAAQ,EAAE;IACnC,oBAAOP,OAAA,CAACH,QAAQ;MAACW,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;EACzC;;EAEA;EACA,IAAIR,SAAS,IAAI,CAACC,QAAQ,CAACQ,OAAO,EAAE;IAClC,oBAAOd,OAAA,CAACH,QAAQ;MAACW,EAAE,EAAC,GAAG;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG;EACpC;EAEA,OAAOT,QAAQ;AACjB,CAAC;AAACD,EAAA,CAdIF,cAAc;AAAAc,EAAA,GAAdd,cAAc;AAgBpB,eAAeA,cAAc;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}