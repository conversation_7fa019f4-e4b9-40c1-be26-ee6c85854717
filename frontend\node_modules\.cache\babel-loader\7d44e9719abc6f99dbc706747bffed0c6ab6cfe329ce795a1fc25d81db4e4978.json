{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\message.jsx\";\nimport React from 'react';\nimport { Alert } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Message(_ref) {\n  let {\n    variant,\n    children\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(Alert, {\n    variant: variant,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 9\n  }, this);\n}\n_c = Message;\nexport default Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Message", "_ref", "variant", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/message.jsx"], "sourcesContent": ["import React from 'react';\nimport { Alert } from 'react-bootstrap';\n\nfunction Message({variant,children}) {\n    return (\n        <Alert variant={variant}>\n            {children}\n        </Alert>\n    );\n}\n\nexport default Message;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,OAAOA,CAAAC,IAAA,EAAqB;EAAA,IAApB;IAACC,OAAO;IAACC;EAAQ,CAAC,GAAAF,IAAA;EAC/B,oBACIF,OAAA,CAACF,KAAK;IAACK,OAAO,EAAEA,OAAQ;IAAAC,QAAA,EACnBA;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACL;AAEhB;AAACC,EAAA,GANQR,OAAO;AAQhB,eAAeA,OAAO;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}