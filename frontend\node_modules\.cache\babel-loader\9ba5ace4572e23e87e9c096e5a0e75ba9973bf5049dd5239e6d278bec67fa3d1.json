{"ast": null, "code": "import createWithBsPrefix from './createWithBsPrefix';\nexport default createWithBsPrefix('tab-content');", "map": {"version": 3, "names": ["createWithBsPrefix"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/TabContent.js"], "sourcesContent": ["import createWithBsPrefix from './createWithBsPrefix';\nexport default createWithBsPrefix('tab-content');"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,sBAAsB;AACrD,eAAeA,kBAAkB,CAAC,aAAa,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}