{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarBrand = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    as,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'navbar-brand');\n  const Component = as || (props.href ? 'a' : 'span');\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    className: classNames(className, bsPrefix)\n  });\n});\nNavbarBrand.displayName = 'NavbarBrand';\nexport default NavbarBrand;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "_ref", "ref", "bsPrefix", "className", "as", "props", "Component", "href", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/NavbarBrand.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarBrand = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  as,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'navbar-brand');\n  const Component = as || (props.href ? 'a' : 'span');\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    className: classNames(className, bsPrefix)\n  });\n});\nNavbarBrand.displayName = 'NavbarBrand';\nexport default NavbarBrand;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAAC,IAAA,EAK/CC,GAAG,KAAK;EAAA,IALwC;IACjDC,QAAQ;IACRC,SAAS;IACTC,EAAE;IACF,GAAGC;EACL,CAAC,GAAAL,IAAA;EACCE,QAAQ,GAAGP,kBAAkB,CAACO,QAAQ,EAAE,cAAc,CAAC;EACvD,MAAMI,SAAS,GAAGF,EAAE,KAAKC,KAAK,CAACE,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC;EACnD,OAAO,aAAaV,IAAI,CAACS,SAAS,EAAE;IAClC,GAAGD,KAAK;IACRJ,GAAG,EAAEA,GAAG;IACRE,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAED,QAAQ;EAC3C,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,WAAW,CAACU,WAAW,GAAG,aAAa;AACvC,eAAeV,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}