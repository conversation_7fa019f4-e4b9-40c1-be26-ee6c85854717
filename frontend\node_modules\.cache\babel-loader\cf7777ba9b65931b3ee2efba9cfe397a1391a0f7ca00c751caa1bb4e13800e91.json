{"ast": null, "code": "import React, { useCallback, useRef } from 'react';\nimport Transition from 'react-transition-group/Transition';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n// Normalizes Transition callbacks when nodeRef is used.\nconst TransitionWrapper = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    onEnter,\n    onEntering,\n    onEntered,\n    onExit,\n    onExiting,\n    onExited,\n    addEndListener,\n    children,\n    childRef,\n    ...props\n  } = _ref;\n  const nodeRef = useRef(null);\n  const mergedRef = useMergedRefs(nodeRef, childRef);\n  const attachRef = r => {\n    mergedRef(safeFindDOMNode(r));\n  };\n  const normalize = callback => param => {\n    if (callback && nodeRef.current) {\n      callback(nodeRef.current, param);\n    }\n  };\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  const handleEnter = useCallback(normalize(onEnter), [onEnter]);\n  const handleEntering = useCallback(normalize(onEntering), [onEntering]);\n  const handleEntered = useCallback(normalize(onEntered), [onEntered]);\n  const handleExit = useCallback(normalize(onExit), [onExit]);\n  const handleExiting = useCallback(normalize(onExiting), [onExiting]);\n  const handleExited = useCallback(normalize(onExited), [onExited]);\n  const handleAddEndListener = useCallback(normalize(addEndListener), [addEndListener]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n\n  return /*#__PURE__*/_jsx(Transition, {\n    ref: ref,\n    ...props,\n    onEnter: handleEnter,\n    onEntered: handleEntered,\n    onEntering: handleEntering,\n    onExit: handleExit,\n    onExited: handleExited,\n    onExiting: handleExiting,\n    addEndListener: handleAddEndListener,\n    nodeRef: nodeRef,\n    children: typeof children === 'function' ? (status, innerProps) => children(status, {\n      ...innerProps,\n      ref: attachRef\n    }) : /*#__PURE__*/React.cloneElement(children, {\n      ref: attachRef\n    })\n  });\n});\nexport default TransitionWrapper;", "map": {"version": 3, "names": ["React", "useCallback", "useRef", "Transition", "useMergedRefs", "safeFindDOMNode", "jsx", "_jsx", "TransitionWrapper", "forwardRef", "_ref", "ref", "onEnter", "onEntering", "onEntered", "onExit", "onExiting", "onExited", "addEndListener", "children", "childRef", "props", "nodeRef", "mergedRef", "attachRef", "r", "normalize", "callback", "param", "current", "handleEnter", "handleEntering", "handleEntered", "handleExit", "handleExiting", "handleExited", "handleAddEndListener", "status", "innerProps", "cloneElement"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/TransitionWrapper.js"], "sourcesContent": ["import React, { useCallback, useRef } from 'react';\nimport Transition from 'react-transition-group/Transition';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n// Normalizes Transition callbacks when nodeRef is used.\nconst TransitionWrapper = /*#__PURE__*/React.forwardRef(({\n  onEnter,\n  onEntering,\n  onEntered,\n  onExit,\n  onExiting,\n  onExited,\n  addEndListener,\n  children,\n  childRef,\n  ...props\n}, ref) => {\n  const nodeRef = useRef(null);\n  const mergedRef = useMergedRefs(nodeRef, childRef);\n  const attachRef = r => {\n    mergedRef(safeFindDOMNode(r));\n  };\n  const normalize = callback => param => {\n    if (callback && nodeRef.current) {\n      callback(nodeRef.current, param);\n    }\n  };\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  const handleEnter = useCallback(normalize(onEnter), [onEnter]);\n  const handleEntering = useCallback(normalize(onEntering), [onEntering]);\n  const handleEntered = useCallback(normalize(onEntered), [onEntered]);\n  const handleExit = useCallback(normalize(onExit), [onExit]);\n  const handleExiting = useCallback(normalize(onExiting), [onExiting]);\n  const handleExited = useCallback(normalize(onExited), [onExited]);\n  const handleAddEndListener = useCallback(normalize(addEndListener), [addEndListener]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n\n  return /*#__PURE__*/_jsx(Transition, {\n    ref: ref,\n    ...props,\n    onEnter: handleEnter,\n    onEntered: handleEntered,\n    onEntering: handleEntering,\n    onExit: handleExit,\n    onExited: handleExited,\n    onExiting: handleExiting,\n    addEndListener: handleAddEndListener,\n    nodeRef: nodeRef,\n    children: typeof children === 'function' ? (status, innerProps) => children(status, {\n      ...innerProps,\n      ref: attachRef\n    }) : /*#__PURE__*/React.cloneElement(children, {\n      ref: attachRef\n    })\n  });\n});\nexport default TransitionWrapper;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,mCAAmC;AAC1D,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA,MAAMC,iBAAiB,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAAC,IAAA,EAWrDC,GAAG,KAAK;EAAA,IAX8C;IACvDC,OAAO;IACPC,UAAU;IACVC,SAAS;IACTC,MAAM;IACNC,SAAS;IACTC,QAAQ;IACRC,cAAc;IACdC,QAAQ;IACRC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAAX,IAAA;EACC,MAAMY,OAAO,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMqB,SAAS,GAAGnB,aAAa,CAACkB,OAAO,EAAEF,QAAQ,CAAC;EAClD,MAAMI,SAAS,GAAGC,CAAC,IAAI;IACrBF,SAAS,CAAClB,eAAe,CAACoB,CAAC,CAAC,CAAC;EAC/B,CAAC;EACD,MAAMC,SAAS,GAAGC,QAAQ,IAAIC,KAAK,IAAI;IACrC,IAAID,QAAQ,IAAIL,OAAO,CAACO,OAAO,EAAE;MAC/BF,QAAQ,CAACL,OAAO,CAACO,OAAO,EAAED,KAAK,CAAC;IAClC;EACF,CAAC;;EAED;EACA,MAAME,WAAW,GAAG7B,WAAW,CAACyB,SAAS,CAACd,OAAO,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAC9D,MAAMmB,cAAc,GAAG9B,WAAW,CAACyB,SAAS,CAACb,UAAU,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EACvE,MAAMmB,aAAa,GAAG/B,WAAW,CAACyB,SAAS,CAACZ,SAAS,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACpE,MAAMmB,UAAU,GAAGhC,WAAW,CAACyB,SAAS,CAACX,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC3D,MAAMmB,aAAa,GAAGjC,WAAW,CAACyB,SAAS,CAACV,SAAS,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACpE,MAAMmB,YAAY,GAAGlC,WAAW,CAACyB,SAAS,CAACT,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACjE,MAAMmB,oBAAoB,GAAGnC,WAAW,CAACyB,SAAS,CAACR,cAAc,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EACrF;;EAEA,OAAO,aAAaX,IAAI,CAACJ,UAAU,EAAE;IACnCQ,GAAG,EAAEA,GAAG;IACR,GAAGU,KAAK;IACRT,OAAO,EAAEkB,WAAW;IACpBhB,SAAS,EAAEkB,aAAa;IACxBnB,UAAU,EAAEkB,cAAc;IAC1BhB,MAAM,EAAEkB,UAAU;IAClBhB,QAAQ,EAAEkB,YAAY;IACtBnB,SAAS,EAAEkB,aAAa;IACxBhB,cAAc,EAAEkB,oBAAoB;IACpCd,OAAO,EAAEA,OAAO;IAChBH,QAAQ,EAAE,OAAOA,QAAQ,KAAK,UAAU,GAAG,CAACkB,MAAM,EAAEC,UAAU,KAAKnB,QAAQ,CAACkB,MAAM,EAAE;MAClF,GAAGC,UAAU;MACb3B,GAAG,EAAEa;IACP,CAAC,CAAC,GAAG,aAAaxB,KAAK,CAACuC,YAAY,CAACpB,QAAQ,EAAE;MAC7CR,GAAG,EAAEa;IACP,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,eAAehB,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}