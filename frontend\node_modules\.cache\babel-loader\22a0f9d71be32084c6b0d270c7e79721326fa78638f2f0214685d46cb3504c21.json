{"ast": null, "code": "import axios from \"axios\";\nfunction setJwt(jwt) {\n  if (jwt == undefined) {\n    delete axios.defaults.headers.common[\"Authorization\"];\n    return;\n  }\n  axios.defaults.headers.common[\"Authorization\"] = `JWT ${jwt}`;\n}\nexport default {\n  get: axios.get,\n  post: axios.post,\n  put: axios.put,\n  patch: axios.patch,\n  delete: axios.delete,\n  setJwt\n};", "map": {"version": 3, "names": ["axios", "setJwt", "jwt", "undefined", "defaults", "headers", "common", "get", "post", "put", "patch", "delete"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/services/httpService.js"], "sourcesContent": ["import axios from \"axios\";\n\nfunction setJwt(jwt) {\n    if (jwt == undefined) {\n        delete axios.defaults.headers.common[\"Authorization\"];\n        return;\n    }\n    axios.defaults.headers.common[\"Authorization\"] = `JWT ${jwt}`;\n}  \n\nexport default {\n    get:axios.get,\n    post:axios.post,\n    put:axios.put,\n    patch:axios.patch,\n    delete:axios.delete,\n    setJwt\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,IAAIA,GAAG,IAAIC,SAAS,EAAE;IAClB,OAAOH,KAAK,CAACI,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrD;EACJ;EACAN,KAAK,CAACI,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAI,OAAMJ,GAAI,EAAC;AACjE;AAEA,eAAe;EACXK,GAAG,EAACP,KAAK,CAACO,GAAG;EACbC,IAAI,EAACR,KAAK,CAACQ,IAAI;EACfC,GAAG,EAACT,KAAK,CAACS,GAAG;EACbC,KAAK,EAACV,KAAK,CAACU,KAAK;EACjBC,MAAM,EAACX,KAAK,CAACW,MAAM;EACnBV;AACJ,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}