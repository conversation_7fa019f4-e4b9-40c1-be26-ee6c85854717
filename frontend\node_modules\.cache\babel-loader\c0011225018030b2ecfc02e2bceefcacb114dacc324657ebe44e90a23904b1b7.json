{"ast": null, "code": "var getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\nvar $Object = Object;\nmodule.exports = USE_SYMBOL_AS_UID ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  var $Symbol = getBuiltIn('Symbol');\n  return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, $Object(it));\n};", "map": {"version": 3, "names": ["getBuiltIn", "require", "isCallable", "isPrototypeOf", "USE_SYMBOL_AS_UID", "$Object", "Object", "module", "exports", "it", "$Symbol", "prototype"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/core-js-pure/internals/is-symbol.js"], "sourcesContent": ["var getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar $Object = Object;\n\nmodule.exports = USE_SYMBOL_AS_UID ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  var $Symbol = getBuiltIn('Symbol');\n  return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, $Object(it));\n};\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACrD,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIE,aAAa,GAAGF,OAAO,CAAC,qCAAqC,CAAC;AAClE,IAAIG,iBAAiB,GAAGH,OAAO,CAAC,gCAAgC,CAAC;AAEjE,IAAII,OAAO,GAAGC,MAAM;AAEpBC,MAAM,CAACC,OAAO,GAAGJ,iBAAiB,GAAG,UAAUK,EAAE,EAAE;EACjD,OAAO,OAAOA,EAAE,IAAI,QAAQ;AAC9B,CAAC,GAAG,UAAUA,EAAE,EAAE;EAChB,IAAIC,OAAO,GAAGV,UAAU,CAAC,QAAQ,CAAC;EAClC,OAAOE,UAAU,CAACQ,OAAO,CAAC,IAAIP,aAAa,CAACO,OAAO,CAACC,SAAS,EAAEN,OAAO,CAACI,EAAE,CAAC,CAAC;AAC7E,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}