{"ast": null, "code": "import contains from 'dom-helpers/contains';\nimport * as React from 'react';\nimport { cloneElement, useCallback, useRef } from 'react';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport warning from 'warning';\nimport { useUncontrolledProp } from 'uncontrollable';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport Overlay from './Overlay';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction normalizeDelay(delay) {\n  return delay && typeof delay === 'object' ? delay : {\n    show: delay,\n    hide: delay\n  };\n}\n\n// Simple implementation of mouseEnter and mouseLeave.\n// React's built version is broken: https://github.com/facebook/react/issues/4251\n// for cases when the trigger is disabled and mouseOut/Over can cause flicker\n// moving from one child element to another.\nfunction handleMouseOverOut(\n// eslint-disable-next-line @typescript-eslint/no-shadow\nhandler, args, relatedNative) {\n  const [e] = args;\n  const target = e.currentTarget;\n  const related = e.relatedTarget || e.nativeEvent[relatedNative];\n  if ((!related || related !== target) && !contains(target, related)) {\n    handler(...args);\n  }\n}\nconst defaultProps = {\n  defaultShow: false,\n  trigger: ['hover', 'focus']\n};\nfunction OverlayTrigger(_ref) {\n  let {\n    trigger,\n    overlay,\n    children,\n    popperConfig = {},\n    show: propsShow,\n    defaultShow = false,\n    onToggle,\n    delay: propsDelay,\n    placement,\n    flip = placement && placement.indexOf('auto') !== -1,\n    ...props\n  } = _ref;\n  const triggerNodeRef = useRef(null);\n  const mergedRef = useMergedRefs(triggerNodeRef, children.ref);\n  const timeout = useTimeout();\n  const hoverStateRef = useRef('');\n  const [show, setShow] = useUncontrolledProp(propsShow, defaultShow, onToggle);\n  const delay = normalizeDelay(propsDelay);\n  const {\n    onFocus,\n    onBlur,\n    onClick\n  } = typeof children !== 'function' ? React.Children.only(children).props : {};\n  const attachRef = r => {\n    mergedRef(safeFindDOMNode(r));\n  };\n  const handleShow = useCallback(() => {\n    timeout.clear();\n    hoverStateRef.current = 'show';\n    if (!delay.show) {\n      setShow(true);\n      return;\n    }\n    timeout.set(() => {\n      if (hoverStateRef.current === 'show') setShow(true);\n    }, delay.show);\n  }, [delay.show, setShow, timeout]);\n  const handleHide = useCallback(() => {\n    timeout.clear();\n    hoverStateRef.current = 'hide';\n    if (!delay.hide) {\n      setShow(false);\n      return;\n    }\n    timeout.set(() => {\n      if (hoverStateRef.current === 'hide') setShow(false);\n    }, delay.hide);\n  }, [delay.hide, setShow, timeout]);\n  const handleFocus = useCallback(function () {\n    handleShow();\n    onFocus == null ? void 0 : onFocus(...arguments);\n  }, [handleShow, onFocus]);\n  const handleBlur = useCallback(function () {\n    handleHide();\n    onBlur == null ? void 0 : onBlur(...arguments);\n  }, [handleHide, onBlur]);\n  const handleClick = useCallback(function () {\n    setShow(!show);\n    onClick == null ? void 0 : onClick(...arguments);\n  }, [onClick, setShow, show]);\n  const handleMouseOver = useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    handleMouseOverOut(handleShow, args, 'fromElement');\n  }, [handleShow]);\n  const handleMouseOut = useCallback(function () {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    handleMouseOverOut(handleHide, args, 'toElement');\n  }, [handleHide]);\n  const triggers = trigger == null ? [] : [].concat(trigger);\n  const triggerProps = {\n    ref: attachRef\n  };\n  if (triggers.indexOf('click') !== -1) {\n    triggerProps.onClick = handleClick;\n  }\n  if (triggers.indexOf('focus') !== -1) {\n    triggerProps.onFocus = handleFocus;\n    triggerProps.onBlur = handleBlur;\n  }\n  if (triggers.indexOf('hover') !== -1) {\n    process.env.NODE_ENV !== \"production\" ? warning(triggers.length > 1, '[react-bootstrap] Specifying only the `\"hover\"` trigger limits the visibility of the overlay to just mouse users. Consider also including the `\"focus\"` trigger so that touch and keyboard only users can see the overlay as well.') : void 0;\n    triggerProps.onMouseOver = handleMouseOver;\n    triggerProps.onMouseOut = handleMouseOut;\n  }\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [typeof children === 'function' ? children(triggerProps) : /*#__PURE__*/cloneElement(children, triggerProps), /*#__PURE__*/_jsx(Overlay, {\n      ...props,\n      show: show,\n      onHide: handleHide,\n      flip: flip,\n      placement: placement,\n      popperConfig: popperConfig,\n      target: triggerNodeRef.current,\n      children: overlay\n    })]\n  });\n}\nOverlayTrigger.defaultProps = defaultProps;\nexport default OverlayTrigger;", "map": {"version": 3, "names": ["contains", "React", "cloneElement", "useCallback", "useRef", "useTimeout", "warning", "useUncontrolledProp", "useMergedRefs", "Overlay", "safeFindDOMNode", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "normalizeDelay", "delay", "show", "hide", "handleMouseOverOut", "handler", "args", "relatedNative", "e", "target", "currentTarget", "related", "relatedTarget", "nativeEvent", "defaultProps", "defaultShow", "trigger", "OverlayTrigger", "_ref", "overlay", "children", "popperConfig", "propsShow", "onToggle", "props<PERSON><PERSON><PERSON>", "placement", "flip", "indexOf", "props", "triggerNodeRef", "mergedRef", "ref", "timeout", "hoverStateRef", "setShow", "onFocus", "onBlur", "onClick", "Children", "only", "attachRef", "r", "handleShow", "clear", "current", "set", "handleHide", "handleFocus", "arguments", "handleBlur", "handleClick", "handleMouseOver", "_len", "length", "Array", "_key", "handleMouseOut", "_len2", "_key2", "triggers", "concat", "triggerProps", "process", "env", "NODE_ENV", "onMouseOver", "onMouseOut", "onHide"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/OverlayTrigger.js"], "sourcesContent": ["import contains from 'dom-helpers/contains';\nimport * as React from 'react';\nimport { cloneElement, useCallback, useRef } from 'react';\nimport useTimeout from '@restart/hooks/useTimeout';\nimport warning from 'warning';\nimport { useUncontrolledProp } from 'uncontrollable';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport Overlay from './Overlay';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction normalizeDelay(delay) {\n  return delay && typeof delay === 'object' ? delay : {\n    show: delay,\n    hide: delay\n  };\n}\n\n// Simple implementation of mouseEnter and mouseLeave.\n// React's built version is broken: https://github.com/facebook/react/issues/4251\n// for cases when the trigger is disabled and mouseOut/Over can cause flicker\n// moving from one child element to another.\nfunction handleMouseOverOut(\n// eslint-disable-next-line @typescript-eslint/no-shadow\nhandler, args, relatedNative) {\n  const [e] = args;\n  const target = e.currentTarget;\n  const related = e.relatedTarget || e.nativeEvent[relatedNative];\n  if ((!related || related !== target) && !contains(target, related)) {\n    handler(...args);\n  }\n}\nconst defaultProps = {\n  defaultShow: false,\n  trigger: ['hover', 'focus']\n};\nfunction OverlayTrigger({\n  trigger,\n  overlay,\n  children,\n  popperConfig = {},\n  show: propsShow,\n  defaultShow = false,\n  onToggle,\n  delay: propsDelay,\n  placement,\n  flip = placement && placement.indexOf('auto') !== -1,\n  ...props\n}) {\n  const triggerNodeRef = useRef(null);\n  const mergedRef = useMergedRefs(triggerNodeRef, children.ref);\n  const timeout = useTimeout();\n  const hoverStateRef = useRef('');\n  const [show, setShow] = useUncontrolledProp(propsShow, defaultShow, onToggle);\n  const delay = normalizeDelay(propsDelay);\n  const {\n    onFocus,\n    onBlur,\n    onClick\n  } = typeof children !== 'function' ? React.Children.only(children).props : {};\n  const attachRef = r => {\n    mergedRef(safeFindDOMNode(r));\n  };\n  const handleShow = useCallback(() => {\n    timeout.clear();\n    hoverStateRef.current = 'show';\n    if (!delay.show) {\n      setShow(true);\n      return;\n    }\n    timeout.set(() => {\n      if (hoverStateRef.current === 'show') setShow(true);\n    }, delay.show);\n  }, [delay.show, setShow, timeout]);\n  const handleHide = useCallback(() => {\n    timeout.clear();\n    hoverStateRef.current = 'hide';\n    if (!delay.hide) {\n      setShow(false);\n      return;\n    }\n    timeout.set(() => {\n      if (hoverStateRef.current === 'hide') setShow(false);\n    }, delay.hide);\n  }, [delay.hide, setShow, timeout]);\n  const handleFocus = useCallback((...args) => {\n    handleShow();\n    onFocus == null ? void 0 : onFocus(...args);\n  }, [handleShow, onFocus]);\n  const handleBlur = useCallback((...args) => {\n    handleHide();\n    onBlur == null ? void 0 : onBlur(...args);\n  }, [handleHide, onBlur]);\n  const handleClick = useCallback((...args) => {\n    setShow(!show);\n    onClick == null ? void 0 : onClick(...args);\n  }, [onClick, setShow, show]);\n  const handleMouseOver = useCallback((...args) => {\n    handleMouseOverOut(handleShow, args, 'fromElement');\n  }, [handleShow]);\n  const handleMouseOut = useCallback((...args) => {\n    handleMouseOverOut(handleHide, args, 'toElement');\n  }, [handleHide]);\n  const triggers = trigger == null ? [] : [].concat(trigger);\n  const triggerProps = {\n    ref: attachRef\n  };\n  if (triggers.indexOf('click') !== -1) {\n    triggerProps.onClick = handleClick;\n  }\n  if (triggers.indexOf('focus') !== -1) {\n    triggerProps.onFocus = handleFocus;\n    triggerProps.onBlur = handleBlur;\n  }\n  if (triggers.indexOf('hover') !== -1) {\n    process.env.NODE_ENV !== \"production\" ? warning(triggers.length > 1, '[react-bootstrap] Specifying only the `\"hover\"` trigger limits the visibility of the overlay to just mouse users. Consider also including the `\"focus\"` trigger so that touch and keyboard only users can see the overlay as well.') : void 0;\n    triggerProps.onMouseOver = handleMouseOver;\n    triggerProps.onMouseOut = handleMouseOut;\n  }\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [typeof children === 'function' ? children(triggerProps) : /*#__PURE__*/cloneElement(children, triggerProps), /*#__PURE__*/_jsx(Overlay, {\n      ...props,\n      show: show,\n      onHide: handleHide,\n      flip: flip,\n      placement: placement,\n      popperConfig: popperConfig,\n      target: triggerNodeRef.current,\n      children: overlay\n    })]\n  });\n}\nOverlayTrigger.defaultProps = defaultProps;\nexport default OverlayTrigger;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACzD,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,OAAO,MAAM,SAAS;AAC7B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQ,IAAIC,SAAS,QAAQ,mBAAmB;AACzD,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAOA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG;IAClDC,IAAI,EAAED,KAAK;IACXE,IAAI,EAAEF;EACR,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAASG,kBAAkBA;AAC3B;AACAC,OAAO,EAAEC,IAAI,EAAEC,aAAa,EAAE;EAC5B,MAAM,CAACC,CAAC,CAAC,GAAGF,IAAI;EAChB,MAAMG,MAAM,GAAGD,CAAC,CAACE,aAAa;EAC9B,MAAMC,OAAO,GAAGH,CAAC,CAACI,aAAa,IAAIJ,CAAC,CAACK,WAAW,CAACN,aAAa,CAAC;EAC/D,IAAI,CAAC,CAACI,OAAO,IAAIA,OAAO,KAAKF,MAAM,KAAK,CAAC1B,QAAQ,CAAC0B,MAAM,EAAEE,OAAO,CAAC,EAAE;IAClEN,OAAO,CAAC,GAAGC,IAAI,CAAC;EAClB;AACF;AACA,MAAMQ,YAAY,GAAG;EACnBC,WAAW,EAAE,KAAK;EAClBC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO;AAC5B,CAAC;AACD,SAASC,cAAcA,CAAAC,IAAA,EAYpB;EAAA,IAZqB;IACtBF,OAAO;IACPG,OAAO;IACPC,QAAQ;IACRC,YAAY,GAAG,CAAC,CAAC;IACjBnB,IAAI,EAAEoB,SAAS;IACfP,WAAW,GAAG,KAAK;IACnBQ,QAAQ;IACRtB,KAAK,EAAEuB,UAAU;IACjBC,SAAS;IACTC,IAAI,GAAGD,SAAS,IAAIA,SAAS,CAACE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACpD,GAAGC;EACL,CAAC,GAAAV,IAAA;EACC,MAAMW,cAAc,GAAG1C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM2C,SAAS,GAAGvC,aAAa,CAACsC,cAAc,EAAET,QAAQ,CAACW,GAAG,CAAC;EAC7D,MAAMC,OAAO,GAAG5C,UAAU,EAAE;EAC5B,MAAM6C,aAAa,GAAG9C,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM,CAACe,IAAI,EAAEgC,OAAO,CAAC,GAAG5C,mBAAmB,CAACgC,SAAS,EAAEP,WAAW,EAAEQ,QAAQ,CAAC;EAC7E,MAAMtB,KAAK,GAAGD,cAAc,CAACwB,UAAU,CAAC;EACxC,MAAM;IACJW,OAAO;IACPC,MAAM;IACNC;EACF,CAAC,GAAG,OAAOjB,QAAQ,KAAK,UAAU,GAAGpC,KAAK,CAACsD,QAAQ,CAACC,IAAI,CAACnB,QAAQ,CAAC,CAACQ,KAAK,GAAG,CAAC,CAAC;EAC7E,MAAMY,SAAS,GAAGC,CAAC,IAAI;IACrBX,SAAS,CAACrC,eAAe,CAACgD,CAAC,CAAC,CAAC;EAC/B,CAAC;EACD,MAAMC,UAAU,GAAGxD,WAAW,CAAC,MAAM;IACnC8C,OAAO,CAACW,KAAK,EAAE;IACfV,aAAa,CAACW,OAAO,GAAG,MAAM;IAC9B,IAAI,CAAC3C,KAAK,CAACC,IAAI,EAAE;MACfgC,OAAO,CAAC,IAAI,CAAC;MACb;IACF;IACAF,OAAO,CAACa,GAAG,CAAC,MAAM;MAChB,IAAIZ,aAAa,CAACW,OAAO,KAAK,MAAM,EAAEV,OAAO,CAAC,IAAI,CAAC;IACrD,CAAC,EAAEjC,KAAK,CAACC,IAAI,CAAC;EAChB,CAAC,EAAE,CAACD,KAAK,CAACC,IAAI,EAAEgC,OAAO,EAAEF,OAAO,CAAC,CAAC;EAClC,MAAMc,UAAU,GAAG5D,WAAW,CAAC,MAAM;IACnC8C,OAAO,CAACW,KAAK,EAAE;IACfV,aAAa,CAACW,OAAO,GAAG,MAAM;IAC9B,IAAI,CAAC3C,KAAK,CAACE,IAAI,EAAE;MACf+B,OAAO,CAAC,KAAK,CAAC;MACd;IACF;IACAF,OAAO,CAACa,GAAG,CAAC,MAAM;MAChB,IAAIZ,aAAa,CAACW,OAAO,KAAK,MAAM,EAAEV,OAAO,CAAC,KAAK,CAAC;IACtD,CAAC,EAAEjC,KAAK,CAACE,IAAI,CAAC;EAChB,CAAC,EAAE,CAACF,KAAK,CAACE,IAAI,EAAE+B,OAAO,EAAEF,OAAO,CAAC,CAAC;EAClC,MAAMe,WAAW,GAAG7D,WAAW,CAAC,YAAa;IAC3CwD,UAAU,EAAE;IACZP,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,GAAAa,SAAO,CAAC;EAC7C,CAAC,EAAE,CAACN,UAAU,EAAEP,OAAO,CAAC,CAAC;EACzB,MAAMc,UAAU,GAAG/D,WAAW,CAAC,YAAa;IAC1C4D,UAAU,EAAE;IACZV,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC,GAAAY,SAAO,CAAC;EAC3C,CAAC,EAAE,CAACF,UAAU,EAAEV,MAAM,CAAC,CAAC;EACxB,MAAMc,WAAW,GAAGhE,WAAW,CAAC,YAAa;IAC3CgD,OAAO,CAAC,CAAChC,IAAI,CAAC;IACdmC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,GAAAW,SAAO,CAAC;EAC7C,CAAC,EAAE,CAACX,OAAO,EAAEH,OAAO,EAAEhC,IAAI,CAAC,CAAC;EAC5B,MAAMiD,eAAe,GAAGjE,WAAW,CAAC,YAAa;IAAA,SAAAkE,IAAA,GAAAJ,SAAA,CAAAK,MAAA,EAAT/C,IAAI,OAAAgD,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAJjD,IAAI,CAAAiD,IAAA,IAAAP,SAAA,CAAAO,IAAA;IAAA;IAC1CnD,kBAAkB,CAACsC,UAAU,EAAEpC,IAAI,EAAE,aAAa,CAAC;EACrD,CAAC,EAAE,CAACoC,UAAU,CAAC,CAAC;EAChB,MAAMc,cAAc,GAAGtE,WAAW,CAAC,YAAa;IAAA,SAAAuE,KAAA,GAAAT,SAAA,CAAAK,MAAA,EAAT/C,IAAI,OAAAgD,KAAA,CAAAG,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJpD,IAAI,CAAAoD,KAAA,IAAAV,SAAA,CAAAU,KAAA;IAAA;IACzCtD,kBAAkB,CAAC0C,UAAU,EAAExC,IAAI,EAAE,WAAW,CAAC;EACnD,CAAC,EAAE,CAACwC,UAAU,CAAC,CAAC;EAChB,MAAMa,QAAQ,GAAG3C,OAAO,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC4C,MAAM,CAAC5C,OAAO,CAAC;EAC1D,MAAM6C,YAAY,GAAG;IACnB9B,GAAG,EAAES;EACP,CAAC;EACD,IAAImB,QAAQ,CAAChC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCkC,YAAY,CAACxB,OAAO,GAAGa,WAAW;EACpC;EACA,IAAIS,QAAQ,CAAChC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCkC,YAAY,CAAC1B,OAAO,GAAGY,WAAW;IAClCc,YAAY,CAACzB,MAAM,GAAGa,UAAU;EAClC;EACA,IAAIU,QAAQ,CAAChC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;IACpCmC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3E,OAAO,CAACsE,QAAQ,CAACN,MAAM,GAAG,CAAC,EAAE,oOAAoO,CAAC,GAAG,KAAK,CAAC;IACnTQ,YAAY,CAACI,WAAW,GAAGd,eAAe;IAC1CU,YAAY,CAACK,UAAU,GAAGV,cAAc;EAC1C;EACA,OAAO,aAAazD,KAAK,CAACF,SAAS,EAAE;IACnCuB,QAAQ,EAAE,CAAC,OAAOA,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACyC,YAAY,CAAC,GAAG,aAAa5E,YAAY,CAACmC,QAAQ,EAAEyC,YAAY,CAAC,EAAE,aAAalE,IAAI,CAACH,OAAO,EAAE;MACjJ,GAAGoC,KAAK;MACR1B,IAAI,EAAEA,IAAI;MACViE,MAAM,EAAErB,UAAU;MAClBpB,IAAI,EAAEA,IAAI;MACVD,SAAS,EAAEA,SAAS;MACpBJ,YAAY,EAAEA,YAAY;MAC1BZ,MAAM,EAAEoB,cAAc,CAACe,OAAO;MAC9BxB,QAAQ,EAAED;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACAF,cAAc,CAACH,YAAY,GAAGA,YAAY;AAC1C,eAAeG,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}