{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CarouselItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    bsPrefix,\n    className,\n    ...props\n  } = _ref;\n  const finalClassName = classNames(className, useBootstrapPrefix(bsPrefix, 'carousel-item'));\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: finalClassName\n  });\n});\nCarouselItem.displayName = 'CarouselItem';\nexport default CarouselItem;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "CarouselItem", "forwardRef", "_ref", "ref", "as", "Component", "bsPrefix", "className", "props", "finalClassName", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/CarouselItem.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CarouselItem = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  ...props\n}, ref) => {\n  const finalClassName = classNames(className, useBootstrapPrefix(bsPrefix, 'carousel-item'));\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: finalClassName\n  });\n});\nCarouselItem.displayName = 'CarouselItem';\nexport default CarouselItem;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAAC,IAAA,EAMhDC,GAAG,KAAK;EAAA,IANyC;IAClD;IACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrBC,QAAQ;IACRC,SAAS;IACT,GAAGC;EACL,CAAC,GAAAN,IAAA;EACC,MAAMO,cAAc,GAAGd,UAAU,CAACY,SAAS,EAAEV,kBAAkB,CAACS,QAAQ,EAAE,eAAe,CAAC,CAAC;EAC3F,OAAO,aAAaP,IAAI,CAACM,SAAS,EAAE;IAClCF,GAAG,EAAEA,GAAG;IACR,GAAGK,KAAK;IACRD,SAAS,EAAEE;EACb,CAAC,CAAC;AACJ,CAAC,CAAC;AACFT,YAAY,CAACU,WAAW,GAAG,cAAc;AACzC,eAAeV,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}