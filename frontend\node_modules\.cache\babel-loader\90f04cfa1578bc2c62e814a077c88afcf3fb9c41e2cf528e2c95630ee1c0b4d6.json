{"ast": null, "code": "'use strict';\n\nmodule.exports = function parseProtocol(url) {\n  var match = /^([-+\\w]{1,25})(:?\\/\\/|:)/.exec(url);\n  return match && match[1] || '';\n};", "map": {"version": 3, "names": ["module", "exports", "parseProtocol", "url", "match", "exec"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/axios/lib/helpers/parseProtocol.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = function parseProtocol(url) {\n  var match = /^([-+\\w]{1,25})(:?\\/\\/|:)/.exec(url);\n  return match && match[1] || '';\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,SAASC,aAAaA,CAACC,GAAG,EAAE;EAC3C,IAAIC,KAAK,GAAG,2BAA2B,CAACC,IAAI,CAACF,GAAG,CAAC;EACjD,OAAOC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;AAChC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}