{"ast": null, "code": "import { useMemo, useRef } from 'react';\nimport useMounted from './useMounted';\nimport useWillUnmount from './useWillUnmount';\n/*\n * Browsers including Internet Explorer, Chrome, Safari, and Firefox store the\n * delay as a 32-bit signed integer internally. This causes an integer overflow\n * when using delays larger than 2,147,483,647 ms (about 24.8 days),\n * resulting in the timeout being executed immediately.\n *\n * via: https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/setTimeout\n */\n\nvar MAX_DELAY_MS = Math.pow(2, 31) - 1;\nfunction setChainedTimeout(handleRef, fn, timeoutAtMs) {\n  var delayMs = timeoutAtMs - Date.now();\n  handleRef.current = delayMs <= MAX_DELAY_MS ? setTimeout(fn, delayMs) : setTimeout(function () {\n    return setChainedTimeout(handleRef, fn, timeoutAtMs);\n  }, MAX_DELAY_MS);\n}\n/**\n * Returns a controller object for setting a timeout that is properly cleaned up\n * once the component unmounts. New timeouts cancel and replace existing ones.\n *\n *\n *\n * ```tsx\n * const { set, clear } = useTimeout();\n * const [hello, showHello] = useState(false);\n * //Display hello after 5 seconds\n * set(() => showHello(true), 5000);\n * return (\n *   <div className=\"App\">\n *     {hello ? <h3>Hello</h3> : null}\n *   </div>\n * );\n * ```\n */\n\nexport default function useTimeout() {\n  var isMounted = useMounted(); // types are confused between node and web here IDK\n\n  var handleRef = useRef();\n  useWillUnmount(function () {\n    return clearTimeout(handleRef.current);\n  });\n  return useMemo(function () {\n    var clear = function clear() {\n      return clearTimeout(handleRef.current);\n    };\n    function set(fn, delayMs) {\n      if (delayMs === void 0) {\n        delayMs = 0;\n      }\n      if (!isMounted()) return;\n      clear();\n      if (delayMs <= MAX_DELAY_MS) {\n        // For simplicity, if the timeout is short, just set a normal timeout.\n        handleRef.current = setTimeout(fn, delayMs);\n      } else {\n        setChainedTimeout(handleRef, fn, Date.now() + delayMs);\n      }\n    }\n    return {\n      set: set,\n      clear: clear\n    };\n  }, []);\n}", "map": {"version": 3, "names": ["useMemo", "useRef", "useMounted", "useWillUnmount", "MAX_DELAY_MS", "Math", "pow", "setChainedTimeout", "handleRef", "fn", "timeoutAtMs", "delayMs", "Date", "now", "current", "setTimeout", "useTimeout", "isMounted", "clearTimeout", "clear", "set"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/hooks/esm/useTimeout.js"], "sourcesContent": ["import { useMemo, useRef } from 'react';\nimport useMounted from './useMounted';\nimport useWillUnmount from './useWillUnmount';\n/*\n * Browsers including Internet Explorer, Chrome, Safari, and Firefox store the\n * delay as a 32-bit signed integer internally. This causes an integer overflow\n * when using delays larger than 2,147,483,647 ms (about 24.8 days),\n * resulting in the timeout being executed immediately.\n *\n * via: https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/setTimeout\n */\n\nvar MAX_DELAY_MS = Math.pow(2, 31) - 1;\n\nfunction setChainedTimeout(handleRef, fn, timeoutAtMs) {\n  var delayMs = timeoutAtMs - Date.now();\n  handleRef.current = delayMs <= MAX_DELAY_MS ? setTimeout(fn, delayMs) : setTimeout(function () {\n    return setChainedTimeout(handleRef, fn, timeoutAtMs);\n  }, MAX_DELAY_MS);\n}\n/**\n * Returns a controller object for setting a timeout that is properly cleaned up\n * once the component unmounts. New timeouts cancel and replace existing ones.\n *\n *\n *\n * ```tsx\n * const { set, clear } = useTimeout();\n * const [hello, showHello] = useState(false);\n * //Display hello after 5 seconds\n * set(() => showHello(true), 5000);\n * return (\n *   <div className=\"App\">\n *     {hello ? <h3>Hello</h3> : null}\n *   </div>\n * );\n * ```\n */\n\n\nexport default function useTimeout() {\n  var isMounted = useMounted(); // types are confused between node and web here IDK\n\n  var handleRef = useRef();\n  useWillUnmount(function () {\n    return clearTimeout(handleRef.current);\n  });\n  return useMemo(function () {\n    var clear = function clear() {\n      return clearTimeout(handleRef.current);\n    };\n\n    function set(fn, delayMs) {\n      if (delayMs === void 0) {\n        delayMs = 0;\n      }\n\n      if (!isMounted()) return;\n      clear();\n\n      if (delayMs <= MAX_DELAY_MS) {\n        // For simplicity, if the timeout is short, just set a normal timeout.\n        handleRef.current = setTimeout(fn, delayMs);\n      } else {\n        setChainedTimeout(handleRef, fn, Date.now() + delayMs);\n      }\n    }\n\n    return {\n      set: set,\n      clear: clear\n    };\n  }, []);\n}"], "mappings": "AAAA,SAASA,OAAO,EAAEC,MAAM,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;AAEtC,SAASC,iBAAiBA,CAACC,SAAS,EAAEC,EAAE,EAAEC,WAAW,EAAE;EACrD,IAAIC,OAAO,GAAGD,WAAW,GAAGE,IAAI,CAACC,GAAG,EAAE;EACtCL,SAAS,CAACM,OAAO,GAAGH,OAAO,IAAIP,YAAY,GAAGW,UAAU,CAACN,EAAE,EAAEE,OAAO,CAAC,GAAGI,UAAU,CAAC,YAAY;IAC7F,OAAOR,iBAAiB,CAACC,SAAS,EAAEC,EAAE,EAAEC,WAAW,CAAC;EACtD,CAAC,EAAEN,YAAY,CAAC;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,eAAe,SAASY,UAAUA,CAAA,EAAG;EACnC,IAAIC,SAAS,GAAGf,UAAU,EAAE,CAAC,CAAC;;EAE9B,IAAIM,SAAS,GAAGP,MAAM,EAAE;EACxBE,cAAc,CAAC,YAAY;IACzB,OAAOe,YAAY,CAACV,SAAS,CAACM,OAAO,CAAC;EACxC,CAAC,CAAC;EACF,OAAOd,OAAO,CAAC,YAAY;IACzB,IAAImB,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;MAC3B,OAAOD,YAAY,CAACV,SAAS,CAACM,OAAO,CAAC;IACxC,CAAC;IAED,SAASM,GAAGA,CAACX,EAAE,EAAEE,OAAO,EAAE;MACxB,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;QACtBA,OAAO,GAAG,CAAC;MACb;MAEA,IAAI,CAACM,SAAS,EAAE,EAAE;MAClBE,KAAK,EAAE;MAEP,IAAIR,OAAO,IAAIP,YAAY,EAAE;QAC3B;QACAI,SAAS,CAACM,OAAO,GAAGC,UAAU,CAACN,EAAE,EAAEE,OAAO,CAAC;MAC7C,CAAC,MAAM;QACLJ,iBAAiB,CAACC,SAAS,EAAEC,EAAE,EAAEG,IAAI,CAACC,GAAG,EAAE,GAAGF,OAAO,CAAC;MACxD;IACF;IAEA,OAAO;MACLS,GAAG,EAAEA,GAAG;MACRD,KAAK,EAAEA;IACT,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;AACR"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}