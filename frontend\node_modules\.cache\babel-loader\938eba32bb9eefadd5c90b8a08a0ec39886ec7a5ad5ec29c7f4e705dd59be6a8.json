{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useCallback, useMemo } from 'react';\nimport SelectableContext from '@restart/ui/SelectableContext';\nimport { useUncontrolled } from 'uncontrollable';\nimport createWithBsPrefix from './createWithBsPrefix';\nimport Navbar<PERSON>rand from './NavbarBrand';\nimport Navbar<PERSON>ollapse from './NavbarCollapse';\nimport NavbarToggle from './NavbarToggle';\nimport NavbarOffcanvas from './NavbarOffcanvas';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarText = createWithBsPrefix('navbar-text', {\n  Component: 'span'\n});\nconst defaultProps = {\n  expand: true,\n  variant: 'light',\n  collapseOnSelect: false\n};\nconst Navbar = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    bsPrefix: initialBsPrefix,\n    expand,\n    variant,\n    bg,\n    fixed,\n    sticky,\n    className,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'nav',\n    expanded,\n    onToggle,\n    onSelect,\n    collapseOnSelect,\n    ...controlledProps\n  } = useUncontrolled(props, {\n    expanded: 'onToggle'\n  });\n  const bsPrefix = useBootstrapPrefix(initialBsPrefix, 'navbar');\n  const handleCollapse = useCallback(function () {\n    onSelect == null ? void 0 : onSelect(...arguments);\n    if (collapseOnSelect && expanded) {\n      onToggle == null ? void 0 : onToggle(false);\n    }\n  }, [onSelect, collapseOnSelect, expanded, onToggle]);\n\n  // will result in some false positives but that seems better\n  // than false negatives. strict `undefined` check allows explicit\n  // \"nulling\" of the role if the user really doesn't want one\n  if (controlledProps.role === undefined && Component !== 'nav') {\n    controlledProps.role = 'navigation';\n  }\n  let expandClass = `${bsPrefix}-expand`;\n  if (typeof expand === 'string') expandClass = `${expandClass}-${expand}`;\n  const navbarContext = useMemo(() => ({\n    onToggle: () => onToggle == null ? void 0 : onToggle(!expanded),\n    bsPrefix,\n    expanded: !!expanded,\n    expand\n  }), [bsPrefix, expanded, expand, onToggle]);\n  return /*#__PURE__*/_jsx(NavbarContext.Provider, {\n    value: navbarContext,\n    children: /*#__PURE__*/_jsx(SelectableContext.Provider, {\n      value: handleCollapse,\n      children: /*#__PURE__*/_jsx(Component, {\n        ref: ref,\n        ...controlledProps,\n        className: classNames(className, bsPrefix, expand && expandClass, variant && `${bsPrefix}-${variant}`, bg && `bg-${bg}`, sticky && `sticky-${sticky}`, fixed && `fixed-${fixed}`)\n      })\n    })\n  });\n});\nNavbar.defaultProps = defaultProps;\nNavbar.displayName = 'Navbar';\nexport default Object.assign(Navbar, {\n  Brand: NavbarBrand,\n  Collapse: NavbarCollapse,\n  Offcanvas: NavbarOffcanvas,\n  Text: NavbarText,\n  Toggle: NavbarToggle\n});", "map": {"version": 3, "names": ["classNames", "React", "useCallback", "useMemo", "SelectableContext", "useUncontrolled", "createWithBsPrefix", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NavbarCollapse", "Navbar<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useBootstrapPrefix", "NavbarContext", "jsx", "_jsx", "NavbarText", "Component", "defaultProps", "expand", "variant", "collapseOnSelect", "<PERSON><PERSON><PERSON>", "forwardRef", "props", "ref", "bsPrefix", "initialBsPrefix", "bg", "fixed", "sticky", "className", "as", "expanded", "onToggle", "onSelect", "controlledProps", "handleCollapse", "arguments", "role", "undefined", "expandClass", "navbarContext", "Provider", "value", "children", "displayName", "Object", "assign", "Brand", "Collapse", "<PERSON><PERSON><PERSON>", "Text", "Toggle"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Navbar.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useCallback, useMemo } from 'react';\nimport SelectableContext from '@restart/ui/SelectableContext';\nimport { useUncontrolled } from 'uncontrollable';\nimport createWithBsPrefix from './createWithBsPrefix';\nimport Navbar<PERSON>rand from './NavbarBrand';\nimport Navbar<PERSON>ollapse from './NavbarCollapse';\nimport NavbarToggle from './NavbarToggle';\nimport NavbarOffcanvas from './NavbarOffcanvas';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarText = createWithBsPrefix('navbar-text', {\n  Component: 'span'\n});\nconst defaultProps = {\n  expand: true,\n  variant: 'light',\n  collapseOnSelect: false\n};\nconst Navbar = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    bsPrefix: initialBsPrefix,\n    expand,\n    variant,\n    bg,\n    fixed,\n    sticky,\n    className,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'nav',\n    expanded,\n    onToggle,\n    onSelect,\n    collapseOnSelect,\n    ...controlledProps\n  } = useUncontrolled(props, {\n    expanded: 'onToggle'\n  });\n  const bsPrefix = useBootstrapPrefix(initialBsPrefix, 'navbar');\n  const handleCollapse = useCallback((...args) => {\n    onSelect == null ? void 0 : onSelect(...args);\n    if (collapseOnSelect && expanded) {\n      onToggle == null ? void 0 : onToggle(false);\n    }\n  }, [onSelect, collapseOnSelect, expanded, onToggle]);\n\n  // will result in some false positives but that seems better\n  // than false negatives. strict `undefined` check allows explicit\n  // \"nulling\" of the role if the user really doesn't want one\n  if (controlledProps.role === undefined && Component !== 'nav') {\n    controlledProps.role = 'navigation';\n  }\n  let expandClass = `${bsPrefix}-expand`;\n  if (typeof expand === 'string') expandClass = `${expandClass}-${expand}`;\n  const navbarContext = useMemo(() => ({\n    onToggle: () => onToggle == null ? void 0 : onToggle(!expanded),\n    bsPrefix,\n    expanded: !!expanded,\n    expand\n  }), [bsPrefix, expanded, expand, onToggle]);\n  return /*#__PURE__*/_jsx(NavbarContext.Provider, {\n    value: navbarContext,\n    children: /*#__PURE__*/_jsx(SelectableContext.Provider, {\n      value: handleCollapse,\n      children: /*#__PURE__*/_jsx(Component, {\n        ref: ref,\n        ...controlledProps,\n        className: classNames(className, bsPrefix, expand && expandClass, variant && `${bsPrefix}-${variant}`, bg && `bg-${bg}`, sticky && `sticky-${sticky}`, fixed && `fixed-${fixed}`)\n      })\n    })\n  });\n});\nNavbar.defaultProps = defaultProps;\nNavbar.displayName = 'Navbar';\nexport default Object.assign(Navbar, {\n  Brand: NavbarBrand,\n  Collapse: NavbarCollapse,\n  Offcanvas: NavbarOffcanvas,\n  Text: NavbarText,\n  Toggle: NavbarToggle\n});"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AAC5C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAGT,kBAAkB,CAAC,aAAa,EAAE;EACnDU,SAAS,EAAE;AACb,CAAC,CAAC;AACF,MAAMC,YAAY,GAAG;EACnBC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,OAAO;EAChBC,gBAAgB,EAAE;AACpB,CAAC;AACD,MAAMC,MAAM,GAAG,aAAapB,KAAK,CAACqB,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC3D,MAAM;IACJC,QAAQ,EAAEC,eAAe;IACzBR,MAAM;IACNC,OAAO;IACPQ,EAAE;IACFC,KAAK;IACLC,MAAM;IACNC,SAAS;IACT;IACAC,EAAE,EAAEf,SAAS,GAAG,KAAK;IACrBgB,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRd,gBAAgB;IAChB,GAAGe;EACL,CAAC,GAAG9B,eAAe,CAACkB,KAAK,EAAE;IACzBS,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAMP,QAAQ,GAAGd,kBAAkB,CAACe,eAAe,EAAE,QAAQ,CAAC;EAC9D,MAAMU,cAAc,GAAGlC,WAAW,CAAC,YAAa;IAC9CgC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,GAAAG,SAAO,CAAC;IAC7C,IAAIjB,gBAAgB,IAAIY,QAAQ,EAAE;MAChCC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,KAAK,CAAC;IAC7C;EACF,CAAC,EAAE,CAACC,QAAQ,EAAEd,gBAAgB,EAAEY,QAAQ,EAAEC,QAAQ,CAAC,CAAC;;EAEpD;EACA;EACA;EACA,IAAIE,eAAe,CAACG,IAAI,KAAKC,SAAS,IAAIvB,SAAS,KAAK,KAAK,EAAE;IAC7DmB,eAAe,CAACG,IAAI,GAAG,YAAY;EACrC;EACA,IAAIE,WAAW,GAAI,GAAEf,QAAS,SAAQ;EACtC,IAAI,OAAOP,MAAM,KAAK,QAAQ,EAAEsB,WAAW,GAAI,GAAEA,WAAY,IAAGtB,MAAO,EAAC;EACxE,MAAMuB,aAAa,GAAGtC,OAAO,CAAC,OAAO;IACnC8B,QAAQ,EAAEA,CAAA,KAAMA,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAACD,QAAQ,CAAC;IAC/DP,QAAQ;IACRO,QAAQ,EAAE,CAAC,CAACA,QAAQ;IACpBd;EACF,CAAC,CAAC,EAAE,CAACO,QAAQ,EAAEO,QAAQ,EAAEd,MAAM,EAAEe,QAAQ,CAAC,CAAC;EAC3C,OAAO,aAAanB,IAAI,CAACF,aAAa,CAAC8B,QAAQ,EAAE;IAC/CC,KAAK,EAAEF,aAAa;IACpBG,QAAQ,EAAE,aAAa9B,IAAI,CAACV,iBAAiB,CAACsC,QAAQ,EAAE;MACtDC,KAAK,EAAEP,cAAc;MACrBQ,QAAQ,EAAE,aAAa9B,IAAI,CAACE,SAAS,EAAE;QACrCQ,GAAG,EAAEA,GAAG;QACR,GAAGW,eAAe;QAClBL,SAAS,EAAE9B,UAAU,CAAC8B,SAAS,EAAEL,QAAQ,EAAEP,MAAM,IAAIsB,WAAW,EAAErB,OAAO,IAAK,GAAEM,QAAS,IAAGN,OAAQ,EAAC,EAAEQ,EAAE,IAAK,MAAKA,EAAG,EAAC,EAAEE,MAAM,IAAK,UAASA,MAAO,EAAC,EAAED,KAAK,IAAK,SAAQA,KAAM,EAAC;MAClL,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFP,MAAM,CAACJ,YAAY,GAAGA,YAAY;AAClCI,MAAM,CAACwB,WAAW,GAAG,QAAQ;AAC7B,eAAeC,MAAM,CAACC,MAAM,CAAC1B,MAAM,EAAE;EACnC2B,KAAK,EAAEzC,WAAW;EAClB0C,QAAQ,EAAEzC,cAAc;EACxB0C,SAAS,EAAExC,eAAe;EAC1ByC,IAAI,EAAEpC,UAAU;EAChBqC,MAAM,EAAE3C;AACV,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}