{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\loader.jsx\";\nimport React from \"react\";\nimport { Spinner } from \"react-bootstrap\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Loader(props) {\n  return /*#__PURE__*/_jsxDEV(Spinner, {\n    animation: \"border\",\n    role: \"status\",\n    style: {\n      height: \"100px\",\n      width: \"100px\",\n      margin: \"auto\",\n      display: \"block\"\n    },\n    children: /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"sr-only\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = Loader;\nexport default Loader;\nvar _c;\n$RefreshReg$(_c, \"Loader\");", "map": {"version": 3, "names": ["React", "Spinner", "jsxDEV", "_jsxDEV", "Loader", "props", "animation", "role", "style", "height", "width", "margin", "display", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/loader.jsx"], "sourcesContent": ["import React from \"react\";\nimport { Spinner } from \"react-bootstrap\";\n\nfunction Loader(props) {\n  return (\n    <Spinner\n      animation=\"border\"\n      role=\"status\"\n      style={{\n        height: \"100px\",\n        width: \"100px\",\n        margin: \"auto\",\n        display: \"block\",\n      }}\n    >\n        <span className=\"sr-only\">Loading...</span>\n    </Spinner>\n  );\n}\n\nexport default Loader;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,SAASC,MAAMA,CAACC,KAAK,EAAE;EACrB,oBACEF,OAAA,CAACF,OAAO;IACNK,SAAS,EAAC,QAAQ;IAClBC,IAAI,EAAC,QAAQ;IACbC,KAAK,EAAE;MACLC,MAAM,EAAE,OAAO;MACfC,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,eAEAV,OAAA;MAAMW,SAAS,EAAC,SAAS;MAAAD,QAAA,EAAC;IAAU;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAO;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACrC;AAEd;AAACC,EAAA,GAfQf,MAAM;AAiBf,eAAeA,MAAM;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}