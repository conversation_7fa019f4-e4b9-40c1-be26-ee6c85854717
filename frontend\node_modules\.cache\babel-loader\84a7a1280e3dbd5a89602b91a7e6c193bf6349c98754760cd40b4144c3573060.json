{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useCallback } from 'react';\nimport { ENTERED, ENTERING } from 'react-transition-group/Transition';\nimport transitionEndListener from './transitionEndListener';\nimport triggerBrowserReflow from './triggerBrowserReflow';\nimport TransitionWrapper from './TransitionWrapper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  in: false,\n  timeout: 300,\n  mountOnEnter: false,\n  unmountOnExit: false,\n  appear: false\n};\nconst fadeStyles = {\n  [ENTERING]: 'show',\n  [ENTERED]: 'show'\n};\nconst Fade = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    className,\n    children,\n    transitionClasses = {},\n    ...props\n  } = _ref;\n  const handleEnter = useCallback((node, isAppearing) => {\n    triggerBrowserReflow(node);\n    props.onEnter == null ? void 0 : props.onEnter(node, isAppearing);\n  }, [props]);\n  return /*#__PURE__*/_jsx(TransitionWrapper, {\n    ref: ref,\n    addEndListener: transitionEndListener,\n    ...props,\n    onEnter: handleEnter,\n    childRef: children.ref,\n    children: (status, innerProps) => /*#__PURE__*/React.cloneElement(children, {\n      ...innerProps,\n      className: classNames('fade', className, children.props.className, fadeStyles[status], transitionClasses[status])\n    })\n  });\n});\nFade.defaultProps = defaultProps;\nFade.displayName = 'Fade';\nexport default Fade;", "map": {"version": 3, "names": ["classNames", "React", "useCallback", "ENTERED", "ENTERING", "transitionEndListener", "triggerBrowserReflow", "TransitionWrapper", "jsx", "_jsx", "defaultProps", "in", "timeout", "mountOnEnter", "unmountOnExit", "appear", "fadeStyles", "Fade", "forwardRef", "_ref", "ref", "className", "children", "transitionClasses", "props", "handleEnter", "node", "isAppearing", "onEnter", "addEndListener", "childRef", "status", "innerProps", "cloneElement", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Fade.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useCallback } from 'react';\nimport { ENTERED, ENTERING } from 'react-transition-group/Transition';\nimport transitionEndListener from './transitionEndListener';\nimport triggerBrowserReflow from './triggerBrowserReflow';\nimport TransitionWrapper from './TransitionWrapper';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  in: false,\n  timeout: 300,\n  mountOnEnter: false,\n  unmountOnExit: false,\n  appear: false\n};\nconst fadeStyles = {\n  [ENTERING]: 'show',\n  [ENTERED]: 'show'\n};\nconst Fade = /*#__PURE__*/React.forwardRef(({\n  className,\n  children,\n  transitionClasses = {},\n  ...props\n}, ref) => {\n  const handleEnter = useCallback((node, isAppearing) => {\n    triggerBrowserReflow(node);\n    props.onEnter == null ? void 0 : props.onEnter(node, isAppearing);\n  }, [props]);\n  return /*#__PURE__*/_jsx(TransitionWrapper, {\n    ref: ref,\n    addEndListener: transitionEndListener,\n    ...props,\n    onEnter: handleEnter,\n    childRef: children.ref,\n    children: (status, innerProps) => /*#__PURE__*/React.cloneElement(children, {\n      ...innerProps,\n      className: classNames('fade', className, children.props.className, fadeStyles[status], transitionClasses[status])\n    })\n  });\n});\nFade.defaultProps = defaultProps;\nFade.displayName = 'Fade';\nexport default Fade;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,OAAO;AACnC,SAASC,OAAO,EAAEC,QAAQ,QAAQ,mCAAmC;AACrE,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG;EACnBC,EAAE,EAAE,KAAK;EACTC,OAAO,EAAE,GAAG;EACZC,YAAY,EAAE,KAAK;EACnBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE;AACV,CAAC;AACD,MAAMC,UAAU,GAAG;EACjB,CAACZ,QAAQ,GAAG,MAAM;EAClB,CAACD,OAAO,GAAG;AACb,CAAC;AACD,MAAMc,IAAI,GAAG,aAAahB,KAAK,CAACiB,UAAU,CAAC,CAAAC,IAAA,EAKxCC,GAAG,KAAK;EAAA,IALiC;IAC1CC,SAAS;IACTC,QAAQ;IACRC,iBAAiB,GAAG,CAAC,CAAC;IACtB,GAAGC;EACL,CAAC,GAAAL,IAAA;EACC,MAAMM,WAAW,GAAGvB,WAAW,CAAC,CAACwB,IAAI,EAAEC,WAAW,KAAK;IACrDrB,oBAAoB,CAACoB,IAAI,CAAC;IAC1BF,KAAK,CAACI,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,KAAK,CAACI,OAAO,CAACF,IAAI,EAAEC,WAAW,CAAC;EACnE,CAAC,EAAE,CAACH,KAAK,CAAC,CAAC;EACX,OAAO,aAAaf,IAAI,CAACF,iBAAiB,EAAE;IAC1Ca,GAAG,EAAEA,GAAG;IACRS,cAAc,EAAExB,qBAAqB;IACrC,GAAGmB,KAAK;IACRI,OAAO,EAAEH,WAAW;IACpBK,QAAQ,EAAER,QAAQ,CAACF,GAAG;IACtBE,QAAQ,EAAEA,CAACS,MAAM,EAAEC,UAAU,KAAK,aAAa/B,KAAK,CAACgC,YAAY,CAACX,QAAQ,EAAE;MAC1E,GAAGU,UAAU;MACbX,SAAS,EAAErB,UAAU,CAAC,MAAM,EAAEqB,SAAS,EAAEC,QAAQ,CAACE,KAAK,CAACH,SAAS,EAAEL,UAAU,CAACe,MAAM,CAAC,EAAER,iBAAiB,CAACQ,MAAM,CAAC;IAClH,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFd,IAAI,CAACP,YAAY,GAAGA,YAAY;AAChCO,IAAI,CAACiB,WAAW,GAAG,MAAM;AACzB,eAAejB,IAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}