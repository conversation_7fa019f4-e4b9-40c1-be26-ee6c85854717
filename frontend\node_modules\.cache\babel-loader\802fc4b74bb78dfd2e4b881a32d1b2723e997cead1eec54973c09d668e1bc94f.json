{"ast": null, "code": "import * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport BaseTabs from '@restart/ui/Tabs';\nimport Nav from './Nav';\nimport NavLink from './NavLink';\nimport NavItem from './NavItem';\nimport TabContent from './TabContent';\nimport TabPane from './TabPane';\nimport { forEach, map } from './ElementChildren';\nimport getTabTransitionComponent from './getTabTransitionComponent';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst defaultProps = {\n  variant: 'tabs',\n  mountOnEnter: false,\n  unmountOnExit: false\n};\nfunction getDefaultActiveKey(children) {\n  let defaultActiveKey;\n  forEach(children, child => {\n    if (defaultActiveKey == null) {\n      defaultActiveKey = child.props.eventKey;\n    }\n  });\n  return defaultActiveKey;\n}\nfunction renderTab(child) {\n  const {\n    title,\n    eventKey,\n    disabled,\n    tabClassName,\n    tabAttrs,\n    id\n  } = child.props;\n  if (title == null) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(NavItem, {\n    as: \"li\",\n    role: \"presentation\",\n    children: /*#__PURE__*/_jsx(NavLink, {\n      as: \"button\",\n      type: \"button\",\n      eventKey: eventKey,\n      disabled: disabled,\n      id: id,\n      className: tabClassName,\n      ...tabAttrs,\n      children: title\n    })\n  });\n}\nconst Tabs = props => {\n  const {\n    id,\n    onSelect,\n    transition,\n    mountOnEnter,\n    unmountOnExit,\n    children,\n    activeKey = getDefaultActiveKey(children),\n    ...controlledProps\n  } = useUncontrolled(props, {\n    activeKey: 'onSelect'\n  });\n  return /*#__PURE__*/_jsxs(BaseTabs, {\n    id: id,\n    activeKey: activeKey,\n    onSelect: onSelect,\n    transition: getTabTransitionComponent(transition),\n    mountOnEnter: mountOnEnter,\n    unmountOnExit: unmountOnExit,\n    children: [/*#__PURE__*/_jsx(Nav, {\n      ...controlledProps,\n      role: \"tablist\",\n      as: \"ul\",\n      children: map(children, renderTab)\n    }), /*#__PURE__*/_jsx(TabContent, {\n      children: map(children, child => {\n        const childProps = {\n          ...child.props\n        };\n        delete childProps.title;\n        delete childProps.disabled;\n        delete childProps.tabClassName;\n        delete childProps.tabAttrs;\n        return /*#__PURE__*/_jsx(TabPane, {\n          ...childProps\n        });\n      })\n    })]\n  });\n};\nTabs.defaultProps = defaultProps;\nTabs.displayName = 'Tabs';\nexport default Tabs;", "map": {"version": 3, "names": ["React", "useUncontrolled", "BaseTabs", "Nav", "NavLink", "NavItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabPane", "for<PERSON>ach", "map", "getTabTransitionComponent", "jsx", "_jsx", "jsxs", "_jsxs", "defaultProps", "variant", "mountOnEnter", "unmountOnExit", "getDefaultActiveKey", "children", "defaultActiveKey", "child", "props", "eventKey", "renderTab", "title", "disabled", "tabClassName", "tabAttrs", "id", "as", "role", "type", "className", "Tabs", "onSelect", "transition", "active<PERSON><PERSON>", "controlledProps", "childProps", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Tabs.js"], "sourcesContent": ["import * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport BaseTabs from '@restart/ui/Tabs';\nimport Nav from './Nav';\nimport NavLink from './NavLink';\nimport NavItem from './NavItem';\nimport TabContent from './TabContent';\nimport TabPane from './TabPane';\nimport { forEach, map } from './ElementChildren';\nimport getTabTransitionComponent from './getTabTransitionComponent';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst defaultProps = {\n  variant: 'tabs',\n  mountOnEnter: false,\n  unmountOnExit: false\n};\nfunction getDefaultActiveKey(children) {\n  let defaultActiveKey;\n  forEach(children, child => {\n    if (defaultActiveKey == null) {\n      defaultActiveKey = child.props.eventKey;\n    }\n  });\n  return defaultActiveKey;\n}\nfunction renderTab(child) {\n  const {\n    title,\n    eventKey,\n    disabled,\n    tabClassName,\n    tabAttrs,\n    id\n  } = child.props;\n  if (title == null) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(NavItem, {\n    as: \"li\",\n    role: \"presentation\",\n    children: /*#__PURE__*/_jsx(NavLink, {\n      as: \"button\",\n      type: \"button\",\n      eventKey: eventKey,\n      disabled: disabled,\n      id: id,\n      className: tabClassName,\n      ...tabAttrs,\n      children: title\n    })\n  });\n}\nconst Tabs = props => {\n  const {\n    id,\n    onSelect,\n    transition,\n    mountOnEnter,\n    unmountOnExit,\n    children,\n    activeKey = getDefaultActiveKey(children),\n    ...controlledProps\n  } = useUncontrolled(props, {\n    activeKey: 'onSelect'\n  });\n  return /*#__PURE__*/_jsxs(BaseTabs, {\n    id: id,\n    activeKey: activeKey,\n    onSelect: onSelect,\n    transition: getTabTransitionComponent(transition),\n    mountOnEnter: mountOnEnter,\n    unmountOnExit: unmountOnExit,\n    children: [/*#__PURE__*/_jsx(Nav, {\n      ...controlledProps,\n      role: \"tablist\",\n      as: \"ul\",\n      children: map(children, renderTab)\n    }), /*#__PURE__*/_jsx(TabContent, {\n      children: map(children, child => {\n        const childProps = {\n          ...child.props\n        };\n        delete childProps.title;\n        delete childProps.disabled;\n        delete childProps.tabClassName;\n        delete childProps.tabAttrs;\n        return /*#__PURE__*/_jsx(TabPane, {\n          ...childProps\n        });\n      })\n    })]\n  });\n};\nTabs.defaultProps = defaultProps;\nTabs.displayName = 'Tabs';\nexport default Tabs;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,OAAO,EAAEC,GAAG,QAAQ,mBAAmB;AAChD,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,MAAM;EACfC,YAAY,EAAE,KAAK;EACnBC,aAAa,EAAE;AACjB,CAAC;AACD,SAASC,mBAAmBA,CAACC,QAAQ,EAAE;EACrC,IAAIC,gBAAgB;EACpBb,OAAO,CAACY,QAAQ,EAAEE,KAAK,IAAI;IACzB,IAAID,gBAAgB,IAAI,IAAI,EAAE;MAC5BA,gBAAgB,GAAGC,KAAK,CAACC,KAAK,CAACC,QAAQ;IACzC;EACF,CAAC,CAAC;EACF,OAAOH,gBAAgB;AACzB;AACA,SAASI,SAASA,CAACH,KAAK,EAAE;EACxB,MAAM;IACJI,KAAK;IACLF,QAAQ;IACRG,QAAQ;IACRC,YAAY;IACZC,QAAQ;IACRC;EACF,CAAC,GAAGR,KAAK,CAACC,KAAK;EACf,IAAIG,KAAK,IAAI,IAAI,EAAE;IACjB,OAAO,IAAI;EACb;EACA,OAAO,aAAad,IAAI,CAACP,OAAO,EAAE;IAChC0B,EAAE,EAAE,IAAI;IACRC,IAAI,EAAE,cAAc;IACpBZ,QAAQ,EAAE,aAAaR,IAAI,CAACR,OAAO,EAAE;MACnC2B,EAAE,EAAE,QAAQ;MACZE,IAAI,EAAE,QAAQ;MACdT,QAAQ,EAAEA,QAAQ;MAClBG,QAAQ,EAAEA,QAAQ;MAClBG,EAAE,EAAEA,EAAE;MACNI,SAAS,EAAEN,YAAY;MACvB,GAAGC,QAAQ;MACXT,QAAQ,EAAEM;IACZ,CAAC;EACH,CAAC,CAAC;AACJ;AACA,MAAMS,IAAI,GAAGZ,KAAK,IAAI;EACpB,MAAM;IACJO,EAAE;IACFM,QAAQ;IACRC,UAAU;IACVpB,YAAY;IACZC,aAAa;IACbE,QAAQ;IACRkB,SAAS,GAAGnB,mBAAmB,CAACC,QAAQ,CAAC;IACzC,GAAGmB;EACL,CAAC,GAAGtC,eAAe,CAACsB,KAAK,EAAE;IACzBe,SAAS,EAAE;EACb,CAAC,CAAC;EACF,OAAO,aAAaxB,KAAK,CAACZ,QAAQ,EAAE;IAClC4B,EAAE,EAAEA,EAAE;IACNQ,SAAS,EAAEA,SAAS;IACpBF,QAAQ,EAAEA,QAAQ;IAClBC,UAAU,EAAE3B,yBAAyB,CAAC2B,UAAU,CAAC;IACjDpB,YAAY,EAAEA,YAAY;IAC1BC,aAAa,EAAEA,aAAa;IAC5BE,QAAQ,EAAE,CAAC,aAAaR,IAAI,CAACT,GAAG,EAAE;MAChC,GAAGoC,eAAe;MAClBP,IAAI,EAAE,SAAS;MACfD,EAAE,EAAE,IAAI;MACRX,QAAQ,EAAEX,GAAG,CAACW,QAAQ,EAAEK,SAAS;IACnC,CAAC,CAAC,EAAE,aAAab,IAAI,CAACN,UAAU,EAAE;MAChCc,QAAQ,EAAEX,GAAG,CAACW,QAAQ,EAAEE,KAAK,IAAI;QAC/B,MAAMkB,UAAU,GAAG;UACjB,GAAGlB,KAAK,CAACC;QACX,CAAC;QACD,OAAOiB,UAAU,CAACd,KAAK;QACvB,OAAOc,UAAU,CAACb,QAAQ;QAC1B,OAAOa,UAAU,CAACZ,YAAY;QAC9B,OAAOY,UAAU,CAACX,QAAQ;QAC1B,OAAO,aAAajB,IAAI,CAACL,OAAO,EAAE;UAChC,GAAGiC;QACL,CAAC,CAAC;MACJ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACDL,IAAI,CAACpB,YAAY,GAAGA,YAAY;AAChCoB,IAAI,CAACM,WAAW,GAAG,MAAM;AACzB,eAAeN,IAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}