{"ast": null, "code": "import createWithBsPrefix from './createWithBsPrefix';\nimport divWithClassName from './divWithClassName';\nconst DivStyledAsH4 = divWithClassName('h4');\nexport default createWithBsPrefix('modal-title', {\n  Component: DivStyledAsH4\n});", "map": {"version": 3, "names": ["createWithBsPrefix", "divWithClassName", "DivStyledAsH4", "Component"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/ModalTitle.js"], "sourcesContent": ["import createWithBsPrefix from './createWithBsPrefix';\nimport divWithClassName from './divWithClassName';\nconst DivStyledAsH4 = divWithClassName('h4');\nexport default createWithBsPrefix('modal-title', {\n  Component: DivStyledAsH4\n});"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,MAAMC,aAAa,GAAGD,gBAAgB,CAAC,IAAI,CAAC;AAC5C,eAAeD,kBAAkB,CAAC,aAAa,EAAE;EAC/CG,SAAS,EAAED;AACb,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}