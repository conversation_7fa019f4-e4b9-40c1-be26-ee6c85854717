{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  aspectRatio: '1x1'\n};\nfunction toPercent(num) {\n  if (num <= 0) return '100%';\n  if (num < 1) return `${num * 100}%`;\n  return `${num}%`;\n}\nconst Ratio = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    children,\n    aspectRatio,\n    style,\n    ...props\n  } = _ref;\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'ratio');\n  const isCustomRatio = typeof aspectRatio === 'number';\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: ref,\n    ...props,\n    style: {\n      ...style,\n      ...(isCustomRatio && {\n        '--bs-aspect-ratio': toPercent(aspectRatio)\n      })\n    },\n    className: classNames(bsPrefix, className, !isCustomRatio && `${bsPrefix}-${aspectRatio}`),\n    children: React.Children.only(children)\n  });\n});\nRatio.defaultProps = defaultProps;\nexport default Ratio;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "defaultProps", "aspectRatio", "toPercent", "num", "<PERSON><PERSON>", "forwardRef", "_ref", "ref", "bsPrefix", "className", "children", "style", "props", "isCustomRatio", "Children", "only"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/Ratio.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultProps = {\n  aspectRatio: '1x1'\n};\nfunction toPercent(num) {\n  if (num <= 0) return '100%';\n  if (num < 1) return `${num * 100}%`;\n  return `${num}%`;\n}\nconst Ratio = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  aspectRatio,\n  style,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'ratio');\n  const isCustomRatio = typeof aspectRatio === 'number';\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: ref,\n    ...props,\n    style: {\n      ...style,\n      ...(isCustomRatio && {\n        '--bs-aspect-ratio': toPercent(aspectRatio)\n      })\n    },\n    className: classNames(bsPrefix, className, !isCustomRatio && `${bsPrefix}-${aspectRatio}`),\n    children: React.Children.only(children)\n  });\n});\nRatio.defaultProps = defaultProps;\nexport default Ratio;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE;AACf,CAAC;AACD,SAASC,SAASA,CAACC,GAAG,EAAE;EACtB,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,MAAM;EAC3B,IAAIA,GAAG,GAAG,CAAC,EAAE,OAAQ,GAAEA,GAAG,GAAG,GAAI,GAAE;EACnC,OAAQ,GAAEA,GAAI,GAAE;AAClB;AACA,MAAMC,KAAK,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAAC,IAAA,EAOzCC,GAAG,KAAK;EAAA,IAPkC;IAC3CC,QAAQ;IACRC,SAAS;IACTC,QAAQ;IACRT,WAAW;IACXU,KAAK;IACL,GAAGC;EACL,CAAC,GAAAN,IAAA;EACCE,QAAQ,GAAGX,kBAAkB,CAACW,QAAQ,EAAE,OAAO,CAAC;EAChD,MAAMK,aAAa,GAAG,OAAOZ,WAAW,KAAK,QAAQ;EACrD,OAAO,aAAaF,IAAI,CAAC,KAAK,EAAE;IAC9BQ,GAAG,EAAEA,GAAG;IACR,GAAGK,KAAK;IACRD,KAAK,EAAE;MACL,GAAGA,KAAK;MACR,IAAIE,aAAa,IAAI;QACnB,mBAAmB,EAAEX,SAAS,CAACD,WAAW;MAC5C,CAAC;IACH,CAAC;IACDQ,SAAS,EAAEd,UAAU,CAACa,QAAQ,EAAEC,SAAS,EAAE,CAACI,aAAa,IAAK,GAAEL,QAAS,IAAGP,WAAY,EAAC,CAAC;IAC1FS,QAAQ,EAAEd,KAAK,CAACkB,QAAQ,CAACC,IAAI,CAACL,QAAQ;EACxC,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,KAAK,CAACJ,YAAY,GAAGA,YAAY;AACjC,eAAeI,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}