{"ast": null, "code": "import $89yE2$react, { useContext as $89yE2$useContext, useMemo as $89yE2$useMemo, useState as $89yE2$useState, useLayoutEffect as $89yE2$useLayoutEffect } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ /*\n    * Copyright 2020 Adobe. All rights reserved.\n    * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n    * you may not use this file except in compliance with the License. You may obtain a copy\n    * of the License at http://www.apache.org/licenses/LICENSE-2.0\n    *\n    * Unless required by applicable law or agreed to in writing, software distributed under\n    * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n    * OF ANY KIND, either express or implied. See the License for the specific language\n    * governing permissions and limitations under the License.\n    */ // We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst $704cf1d3b684cc5c$var$defaultContext = {\n  prefix: String(Math.round(Math.random() * 10000000000)),\n  current: 0\n};\nconst $704cf1d3b684cc5c$var$SSRContext = /*#__PURE__*/(0, $89yE2$react).createContext($704cf1d3b684cc5c$var$defaultContext);\nfunction $704cf1d3b684cc5c$export$9f8ac96af4b1b2ae(props) {\n  let cur = (0, $89yE2$useContext)($704cf1d3b684cc5c$var$SSRContext);\n  let value = (0, $89yE2$useMemo)(() => ({\n    // If this is the first SSRProvider, start with an empty string prefix, otherwise\n    // append and increment the counter.\n    prefix: cur === $704cf1d3b684cc5c$var$defaultContext ? \"\" : `${cur.prefix}-${++cur.current}`,\n    current: 0\n  }), [cur]);\n  return /*#__PURE__*/(0, $89yE2$react).createElement($704cf1d3b684cc5c$var$SSRContext.Provider, {\n    value: value\n  }, props.children);\n}\nlet $704cf1d3b684cc5c$var$canUseDOM = Boolean(typeof window !== \"undefined\" && window.document && window.document.createElement);\nfunction $704cf1d3b684cc5c$export$619500959fc48b26(defaultId) {\n  let ctx = (0, $89yE2$useContext)($704cf1d3b684cc5c$var$SSRContext);\n  // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n  // provide a warning to hint to the developer to add one.\n  if (ctx === $704cf1d3b684cc5c$var$defaultContext && !$704cf1d3b684cc5c$var$canUseDOM) console.warn(\"When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.\");\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return (0, $89yE2$useMemo)(() => defaultId || `react-aria${ctx.prefix}-${++ctx.current}`, [defaultId]);\n}\nfunction $704cf1d3b684cc5c$export$535bd6ca7f90a273() {\n  let cur = (0, $89yE2$useContext)($704cf1d3b684cc5c$var$SSRContext);\n  let isInSSRContext = cur !== $704cf1d3b684cc5c$var$defaultContext;\n  let [isSSR, setIsSSR] = (0, $89yE2$useState)(isInSSRContext);\n  // If on the client, and the component was initially server rendered,\n  // then schedule a layout effect to update the component after hydration.\n  if (typeof window !== \"undefined\" && isInSSRContext)\n    // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0, $89yE2$useLayoutEffect)(() => {\n      setIsSSR(false);\n    }, []);\n  return isSSR;\n}\nexport { $704cf1d3b684cc5c$export$9f8ac96af4b1b2ae as SSRProvider, $704cf1d3b684cc5c$export$619500959fc48b26 as useSSRSafeId, $704cf1d3b684cc5c$export$535bd6ca7f90a273 as useIsSSR };", "map": {"version": 3, "names": ["$704cf1d3b684cc5c$var$defaultContext", "prefix", "String", "Math", "round", "random", "current", "$704cf1d3b684cc5c$var$SSRContext", "$89yE2$react", "createContext", "$704cf1d3b684cc5c$export$9f8ac96af4b1b2ae", "props", "cur", "$89yE2$useContext", "value", "$89yE2$useMemo", "createElement", "Provider", "children", "$704cf1d3b684cc5c$var$canUseDOM", "Boolean", "window", "document", "$704cf1d3b684cc5c$export$619500959fc48b26", "defaultId", "ctx", "console", "warn", "$704cf1d3b684cc5c$export$535bd6ca7f90a273", "isInSSRContext", "isSSR", "setIsSSR", "$89yE2$useState", "$89yE2$useLayoutEffect"], "sources": ["D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\node_modules\\@react-aria\\ssr\\dist\\packages\\@react-aria\\ssr\\src\\index.ts", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\node_modules\\@react-aria\\ssr\\dist\\packages\\@react-aria\\ssr\\src\\SSRProvider.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nexport {SSRProvider, useSSRSafeId, useIsSSR} from './SSRProvider';\nexport type {SSRProviderProps} from './SSRProvider';\n", "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\nimport React, {ReactNode, useContext, useLayoutEffect, useMemo, useState} from 'react';\n\n// To support SSR, the auto incrementing id counter is stored in a context. This allows\n// it to be reset on every request to ensure the client and server are consistent.\n// There is also a prefix string that is used to support async loading components\n// Each async boundary must be wrapped in an SSR provider, which appends to the prefix\n// and resets the current id counter. This ensures that async loaded components have\n// consistent ids regardless of the loading order.\ninterface SSRContextValue {\n  prefix: string,\n  current: number\n}\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst defaultContext: SSRContextValue = {\n  prefix: String(Math.round(Math.random() * 10000000000)),\n  current: 0\n};\n\nconst SSRContext = React.createContext<SSRContextValue>(defaultContext);\n\nexport interface SSRProviderProps {\n  /** Your application here. */\n  children: ReactNode\n}\n\n/**\n * When using SSR with React Aria, applications must be wrapped in an SSRProvider.\n * This ensures that auto generated ids are consistent between the client and server.\n */\nexport function SSRProvider(props: SSRProviderProps): JSX.Element {\n  let cur = useContext(SSRContext);\n  let value: SSRContextValue = useMemo(() => ({\n    // If this is the first SSRProvider, start with an empty string prefix, otherwise\n    // append and increment the counter.\n    prefix: cur === defaultContext ? '' : `${cur.prefix}-${++cur.current}`,\n    current: 0\n  }), [cur]);\n\n  return (\n    <SSRContext.Provider value={value}>\n      {props.children}\n    </SSRContext.Provider>\n  );\n}\n\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\n/** @private */\nexport function useSSRSafeId(defaultId?: string): string {\n  let ctx = useContext(SSRContext);\n\n  // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n  // provide a warning to hint to the developer to add one.\n  if (ctx === defaultContext && !canUseDOM) {\n    console.warn('When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.');\n  }\n\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return useMemo(() => defaultId || `react-aria${ctx.prefix}-${++ctx.current}`, [defaultId]);\n}\n\n/**\n * Returns whether the component is currently being server side rendered or\n * hydrated on the client. Can be used to delay browser-specific rendering\n * until after hydration.\n */\nexport function useIsSSR(): boolean {\n  let cur = useContext(SSRContext);\n  let isInSSRContext = cur !== defaultContext;\n  let [isSSR, setIsSSR] = useState(isInSSRContext);\n\n  // If on the client, and the component was initially server rendered,\n  // then schedule a layout effect to update the component after hydration.\n  if (typeof window !== 'undefined' && isInSSRContext) {\n    // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useLayoutEffect(() => {\n      setIsSSR(false);\n    }, []);\n  }\n\n  return isSSR;\n}\n"], "mappings": ";;AAAA;;;;;;;;;;ACAA,GDAA,CCAA;;;;;;;;;;MAAA,CAYA;AACA;AACA;;AAcA;AACA;AACA;AACA;AACA;AACA,MAAMA,oCAAA,GAAkC;EACtCC,MAAA,EAAQC,MAAA,CAAOC,IAAA,CAAKC,KAAK,CAACD,IAAA,CAAKE,MAAM,KAAK;EAC1CC,OAAA,EAAS;AACX;AAEA,MAAMC,gCAAA,gBAAa,IAAAC,YAAK,EAACC,aAAa,CAAkBT,oCAAA;AAWjD,SAASU,0CAAYC,KAAuB,EAAe;EAChE,IAAIC,GAAA,GAAM,IAAAC,iBAAS,EAAEN,gCAAA;EACrB,IAAIO,KAAA,GAAyB,IAAAC,cAAO,EAAC,OAAO;IAC1C;IACA;IACAd,MAAA,EAAQW,GAAA,KAAQZ,oCAAA,GAAiB,KAAM,GAAEY,GAAA,CAAIX,MAAO,IAAG,EAAEW,GAAA,CAAIN,OAAQ,EAAC;IACtEA,OAAA,EAAS;EACX,IAAI,CAACM,GAAA,CAAI;EAET,oBACE,IAAAJ,YAAA,EAAAQ,aAAA,CAACT,gCAAA,CAAWU,QAAQ;IAACH,KAAA,EAAOA;KACzBH,KAAA,CAAMO,QAAQ;AAGrB;AAEA,IAAIC,+BAAA,GAAYC,OAAA,CACd,OAAOC,MAAA,KAAW,eAClBA,MAAA,CAAOC,QAAQ,IACfD,MAAA,CAAOC,QAAQ,CAACN,aAAa;AAIxB,SAASO,0CAAaC,SAAkB,EAAU;EACvD,IAAIC,GAAA,GAAM,IAAAZ,iBAAS,EAAEN,gCAAA;EAErB;EACA;EACA,IAAIkB,GAAA,KAAQzB,oCAAA,IAAkB,CAACmB,+BAAA,EAC7BO,OAAA,CAAQC,IAAI,CAAC;EAGf;EACA,OAAO,IAAAZ,cAAM,EAAE,MAAMS,SAAA,IAAc,aAAYC,GAAA,CAAIxB,MAAO,IAAG,EAAEwB,GAAA,CAAInB,OAAQ,EAAC,EAAE,CAACkB,SAAA,CAAU;AAC3F;AAOO,SAASI,0CAAA,EAAoB;EAClC,IAAIhB,GAAA,GAAM,IAAAC,iBAAS,EAAEN,gCAAA;EACrB,IAAIsB,cAAA,GAAiBjB,GAAA,KAAQZ,oCAAA;EAC7B,IAAI,CAAC8B,KAAA,EAAOC,QAAA,CAAS,GAAG,IAAAC,eAAQ,EAACH,cAAA;EAEjC;EACA;EACA,IAAI,OAAOR,MAAA,KAAW,eAAeQ,cAAA;IACnC;IACA;IACA;IACA,IAAAI,sBAAe,EAAC,MAAM;MACpBF,QAAA,CAAS,KAAK;IAChB,GAAG,EAAE;EAGP,OAAOD,KAAA;AACT"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}