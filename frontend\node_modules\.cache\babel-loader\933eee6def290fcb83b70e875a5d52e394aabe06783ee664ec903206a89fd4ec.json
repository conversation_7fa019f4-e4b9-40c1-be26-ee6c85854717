{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\rating.jsx\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Rating(_ref) {\n  let {\n    color,\n    text,\n    value\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rating\",\n    children: [/*#__PURE__*/_jsxDEV(Star, {\n      color: color,\n      base: 0,\n      value: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(Star, {\n      color: color,\n      base: 1,\n      value: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(Star, {\n      color: color,\n      base: 2,\n      value: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(Star, {\n      color: color,\n      base: 3,\n      value: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(Star, {\n      color: color,\n      base: 4,\n      value: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 4,\n    columnNumber: 10\n  }, this);\n}\n_c = Rating;\nexport default Rating;\nfunction Star(_ref2) {\n  let {\n    color,\n    base,\n    value\n  } = _ref2;\n  return /*#__PURE__*/_jsxDEV(\"span\", {\n    children: /*#__PURE__*/_jsxDEV(\"i\", {\n      style: {\n        color\n      },\n      className: value >= base + 1 ? 'fas fa-star' : value >= base + 0.5 ? 'fas fa-star-half-alt' : 'far fa-star'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 10\n  }, this);\n}\n_c2 = Star;\nvar _c, _c2;\n$RefreshReg$(_c, \"Rating\");\n$RefreshReg$(_c2, \"Star\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Rating", "_ref", "color", "text", "value", "className", "children", "Star", "base", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "_ref2", "style", "_c2", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/rating.jsx"], "sourcesContent": ["import React from \"react\";\n\nfunction Rating({color,text,value}) {\n  return <div className=\"rating\">\n    <Star color={color} base={0} value={value}/>\n    <Star color={color} base={1} value={value}/>\n    <Star color={color} base={2} value={value}/>\n    <Star color={color} base={3} value={value}/>\n    <Star color={color} base={4} value={value}/>\n    <span>{text}</span>\n  </div>;\n}\n\nexport default Rating;\n\nfunction Star({color,base, value}) {\n  return <span>\n    <i style={{color}} className={\n        value >= base + 1 ? 'fas fa-star' :\n        value >= base + 0.5 ? 'fas fa-star-half-alt' :\n        'far fa-star'\n    }></i>\n  </span>;\n}\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,MAAMA,CAAAC,IAAA,EAAqB;EAAA,IAApB;IAACC,KAAK;IAACC,IAAI;IAACC;EAAK,CAAC,GAAAH,IAAA;EAChC,oBAAOF,OAAA;IAAKM,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBAC5BP,OAAA,CAACQ,IAAI;MAACL,KAAK,EAAEA,KAAM;MAACM,IAAI,EAAE,CAAE;MAACJ,KAAK,EAAEA;IAAM;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAE,eAC5Cb,OAAA,CAACQ,IAAI;MAACL,KAAK,EAAEA,KAAM;MAACM,IAAI,EAAE,CAAE;MAACJ,KAAK,EAAEA;IAAM;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAE,eAC5Cb,OAAA,CAACQ,IAAI;MAACL,KAAK,EAAEA,KAAM;MAACM,IAAI,EAAE,CAAE;MAACJ,KAAK,EAAEA;IAAM;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAE,eAC5Cb,OAAA,CAACQ,IAAI;MAACL,KAAK,EAAEA,KAAM;MAACM,IAAI,EAAE,CAAE;MAACJ,KAAK,EAAEA;IAAM;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAE,eAC5Cb,OAAA,CAACQ,IAAI;MAACL,KAAK,EAAEA,KAAM;MAACM,IAAI,EAAE,CAAE;MAACJ,KAAK,EAAEA;IAAM;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAE,eAC5Cb,OAAA;MAAAO,QAAA,EAAOH;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAQ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACf;AACR;AAACC,EAAA,GATQb,MAAM;AAWf,eAAeA,MAAM;AAErB,SAASO,IAAIA,CAAAO,KAAA,EAAsB;EAAA,IAArB;IAACZ,KAAK;IAACM,IAAI;IAAEJ;EAAK,CAAC,GAAAU,KAAA;EAC/B,oBAAOf,OAAA;IAAAO,QAAA,eACLP,OAAA;MAAGgB,KAAK,EAAE;QAACb;MAAK,CAAE;MAACG,SAAS,EACxBD,KAAK,IAAII,IAAI,GAAG,CAAC,GAAG,aAAa,GACjCJ,KAAK,IAAII,IAAI,GAAG,GAAG,GAAG,sBAAsB,GAC5C;IACH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAK;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACD;AACT;AAACI,GAAA,GARQT,IAAI;AAAA,IAAAM,EAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAJ,EAAA;AAAAI,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}