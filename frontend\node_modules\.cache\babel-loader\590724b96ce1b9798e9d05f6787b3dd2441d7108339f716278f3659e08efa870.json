{"ast": null, "code": "import * as React from 'react';\n\n// TODO\n\nconst FormContext = /*#__PURE__*/React.createContext({});\nexport default FormContext;", "map": {"version": 3, "names": ["React", "FormContext", "createContext"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/FormContext.js"], "sourcesContent": ["import * as React from 'react';\n\n// TODO\n\nconst FormContext = /*#__PURE__*/React.createContext({});\nexport default FormContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;;AAEA,MAAMC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AACxD,eAAeD,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}