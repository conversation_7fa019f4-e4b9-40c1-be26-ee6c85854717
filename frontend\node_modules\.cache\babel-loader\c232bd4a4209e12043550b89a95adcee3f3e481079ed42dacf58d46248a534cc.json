{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    ...props\n  } = _ref;\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "map": {"version": 3, "names": ["classNames", "React", "useMemo", "useBootstrapPrefix", "CardHeaderContext", "jsx", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "forwardRef", "_ref", "ref", "bsPrefix", "className", "as", "Component", "props", "prefix", "contextValue", "cardHeaderBsPrefix", "Provider", "value", "children", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/CardHeader.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAAC,IAAA,EAM9CC,GAAG,KAAK;EAAA,IANuC;IAChDC,QAAQ;IACRC,SAAS;IACT;IACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrB,GAAGC;EACL,CAAC,GAAAN,IAAA;EACC,MAAMO,MAAM,GAAGb,kBAAkB,CAACQ,QAAQ,EAAE,aAAa,CAAC;EAC1D,MAAMM,YAAY,GAAGf,OAAO,CAAC,OAAO;IAClCgB,kBAAkB,EAAEF;EACtB,CAAC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACb,OAAO,aAAaV,IAAI,CAACF,iBAAiB,CAACe,QAAQ,EAAE;IACnDC,KAAK,EAAEH,YAAY;IACnBI,QAAQ,EAAE,aAAaf,IAAI,CAACQ,SAAS,EAAE;MACrCJ,GAAG,EAAEA,GAAG;MACR,GAAGK,KAAK;MACRH,SAAS,EAAEZ,UAAU,CAACY,SAAS,EAAEI,MAAM;IACzC,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFT,UAAU,CAACe,WAAW,GAAG,YAAY;AACrC,eAAef,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}