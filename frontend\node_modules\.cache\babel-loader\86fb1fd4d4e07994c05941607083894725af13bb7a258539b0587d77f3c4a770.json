{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormContext from './FormContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormRange = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    bsPrefix,\n    className,\n    id,\n    ...props\n  } = _ref;\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-range');\n  return /*#__PURE__*/_jsx(\"input\", {\n    ...props,\n    type: \"range\",\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    id: id || controlId\n  });\n});\nFormRange.displayName = 'FormRange';\nexport default FormRange;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useBootstrapPrefix", "FormContext", "jsx", "_jsx", "FormRange", "forwardRef", "_ref", "ref", "bsPrefix", "className", "id", "props", "controlId", "type", "displayName"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/react-bootstrap/esm/FormRange.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormContext from './FormContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormRange = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  id,\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-range');\n  return /*#__PURE__*/_jsx(\"input\", {\n    ...props,\n    type: \"range\",\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    id: id || controlId\n  });\n});\nFormRange.displayName = 'FormRange';\nexport default FormRange;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAAC,IAAA,EAK7CC,GAAG,KAAK;EAAA,IALsC;IAC/CC,QAAQ;IACRC,SAAS;IACTC,EAAE;IACF,GAAGC;EACL,CAAC,GAAAL,IAAA;EACC,MAAM;IACJM;EACF,CAAC,GAAGb,UAAU,CAACE,WAAW,CAAC;EAC3BO,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,YAAY,CAAC;EACrD,OAAO,aAAaL,IAAI,CAAC,OAAO,EAAE;IAChC,GAAGQ,KAAK;IACRE,IAAI,EAAE,OAAO;IACbN,GAAG,EAAEA,GAAG;IACRE,SAAS,EAAEZ,UAAU,CAACY,SAAS,EAAED,QAAQ,CAAC;IAC1CE,EAAE,EAAEA,EAAE,IAAIE;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,SAAS,CAACU,WAAW,GAAG,WAAW;AACnC,eAAeV,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}