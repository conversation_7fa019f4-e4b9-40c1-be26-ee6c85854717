[{"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "2", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js": "3", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "4", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "5", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "6", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "7", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "8", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "9", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "10", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "11", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "12", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "13", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "14", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "15", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "16", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "17", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "18", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "19", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "20", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "21", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "22", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx": "23", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "24", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "25", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "26", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "27", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx": "28", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx": "29", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "30", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "31", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "32", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "33", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "34", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "35", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "36"}, {"size": 649, "mtime": 1750943544495, "results": "37", "hashOfConfig": "38"}, {"size": 362, "mtime": 1750943544502, "results": "39", "hashOfConfig": "38"}, {"size": 2764, "mtime": 1750943544485, "results": "40", "hashOfConfig": "38"}, {"size": 1585, "mtime": 1750943544494, "results": "41", "hashOfConfig": "38"}, {"size": 4116, "mtime": 1750943544494, "results": "42", "hashOfConfig": "38"}, {"size": 4250, "mtime": 1750943544495, "results": "43", "hashOfConfig": "38"}, {"size": 1795, "mtime": 1750943544489, "results": "44", "hashOfConfig": "38"}, {"size": 2707, "mtime": 1750943544498, "results": "45", "hashOfConfig": "38"}, {"size": 562, "mtime": 1750943544488, "results": "46", "hashOfConfig": "38"}, {"size": 1880, "mtime": 1750943544498, "results": "47", "hashOfConfig": "38"}, {"size": 4041, "mtime": 1750943544496, "results": "48", "hashOfConfig": "38"}, {"size": 4556, "mtime": 1750943544500, "results": "49", "hashOfConfig": "38"}, {"size": 3166, "mtime": 1750943544501, "results": "50", "hashOfConfig": "38"}, {"size": 3374, "mtime": 1750943544501, "results": "51", "hashOfConfig": "38"}, {"size": 2746, "mtime": 1750943544500, "results": "52", "hashOfConfig": "38"}, {"size": 557, "mtime": 1750943544498, "results": "53", "hashOfConfig": "38"}, {"size": 1812, "mtime": 1750943544497, "results": "54", "hashOfConfig": "38"}, {"size": 5003, "mtime": 1750943544499, "results": "55", "hashOfConfig": "38"}, {"size": 1876, "mtime": 1750943544499, "results": "56", "hashOfConfig": "38"}, {"size": 6146, "mtime": 1750943544499, "results": "57", "hashOfConfig": "38"}, {"size": 4506, "mtime": 1750943544501, "results": "58", "hashOfConfig": "38"}, {"size": 392, "mtime": 1750943544489, "results": "59", "hashOfConfig": "38"}, {"size": 988, "mtime": 1750943544487, "results": "60", "hashOfConfig": "38"}, {"size": 373, "mtime": 1750943544502, "results": "61", "hashOfConfig": "38"}, {"size": 1221, "mtime": 1750943544493, "results": "62", "hashOfConfig": "38"}, {"size": 228, "mtime": 1750943544490, "results": "63", "hashOfConfig": "38"}, {"size": 1101, "mtime": 1750943544491, "results": "64", "hashOfConfig": "38"}, {"size": 737, "mtime": 1750943544487, "results": "65", "hashOfConfig": "38"}, {"size": 901, "mtime": 1750943544491, "results": "66", "hashOfConfig": "38"}, {"size": 3665, "mtime": 1750943544493, "results": "67", "hashOfConfig": "38"}, {"size": 372, "mtime": 1750943544489, "results": "68", "hashOfConfig": "38"}, {"size": 2548, "mtime": 1750943544493, "results": "69", "hashOfConfig": "38"}, {"size": 2420, "mtime": 1750943544490, "results": "70", "hashOfConfig": "38"}, {"size": 1519, "mtime": 1750943544488, "results": "71", "hashOfConfig": "38"}, {"size": 639, "mtime": 1750943544492, "results": "72", "hashOfConfig": "38"}, {"size": 1826, "mtime": 1750943544491, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xy8mk5", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\index.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\App.js", ["182"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["183"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["184", "185", "186"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["187", "188", "189", "190", "191", "192", "193", "194", "195"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["196", "197"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["198"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["199", "200"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", ["201", "202"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["203", "204"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", ["205"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", ["206", "207"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["208"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["209"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", ["210"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", ["211", "212", "213"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", ["214"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["215", "216", "217", "218"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["219", "220", "221", "222", "223", "224", "225", "226", "227"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\categoryCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["228", "229"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["230"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\brandCard.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\productsCarousel.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["231", "232"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["233"], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["234", "235", "236"], [], {"ruleId": "237", "severity": 1, "message": "238", "line": 31, "column": 3, "nodeType": "239", "endLine": 31, "endColumn": 12, "suggestions": "240"}, {"ruleId": "241", "severity": 1, "message": "242", "line": 35, "column": 49, "nodeType": "243", "messageId": "244", "endLine": 35, "endColumn": 51}, {"ruleId": "241", "severity": 1, "message": "242", "line": 62, "column": 18, "nodeType": "243", "messageId": "244", "endLine": 62, "endColumn": 20}, {"ruleId": "241", "severity": 1, "message": "242", "line": 68, "column": 15, "nodeType": "243", "messageId": "244", "endLine": 68, "endColumn": 17}, {"ruleId": "241", "severity": 1, "message": "242", "line": 125, "column": 45, "nodeType": "243", "messageId": "244", "endLine": 125, "endColumn": 47}, {"ruleId": "245", "severity": 1, "message": "246", "line": 58, "column": 15, "nodeType": "239", "messageId": "247", "endLine": 58, "endColumn": 19}, {"ruleId": "237", "severity": 1, "message": "248", "line": 100, "column": 6, "nodeType": "249", "endLine": 100, "endColumn": 8, "suggestions": "250"}, {"ruleId": "237", "severity": 1, "message": "251", "line": 112, "column": 6, "nodeType": "249", "endLine": 112, "endColumn": 18, "suggestions": "252"}, {"ruleId": "241", "severity": 1, "message": "253", "line": 117, "column": 20, "nodeType": "243", "messageId": "244", "endLine": 117, "endColumn": 22}, {"ruleId": "241", "severity": 1, "message": "253", "line": 117, "column": 53, "nodeType": "243", "messageId": "244", "endLine": 117, "endColumn": 55}, {"ruleId": "241", "severity": 1, "message": "253", "line": 121, "column": 17, "nodeType": "243", "messageId": "244", "endLine": 121, "endColumn": 19}, {"ruleId": "241", "severity": 1, "message": "253", "line": 121, "column": 44, "nodeType": "243", "messageId": "244", "endLine": 121, "endColumn": 46}, {"ruleId": "241", "severity": 1, "message": "253", "line": 125, "column": 20, "nodeType": "243", "messageId": "244", "endLine": 125, "endColumn": 22}, {"ruleId": "241", "severity": 1, "message": "242", "line": 129, "column": 16, "nodeType": "243", "messageId": "244", "endLine": 129, "endColumn": 18}, {"ruleId": "245", "severity": 1, "message": "254", "line": 13, "column": 23, "nodeType": "239", "messageId": "247", "endLine": 13, "endColumn": 38}, {"ruleId": "237", "severity": 1, "message": "255", "line": 20, "column": 6, "nodeType": "249", "endLine": 20, "endColumn": 8, "suggestions": "256"}, {"ruleId": "257", "severity": 1, "message": "258", "line": 14, "column": 13, "nodeType": "259", "messageId": "260", "endLine": 14, "endColumn": 107, "fix": "261"}, {"ruleId": "237", "severity": 1, "message": "262", "line": 21, "column": 6, "nodeType": "249", "endLine": 21, "endColumn": 8, "suggestions": "263"}, {"ruleId": "241", "severity": 1, "message": "253", "line": 25, "column": 13, "nodeType": "243", "messageId": "244", "endLine": 25, "endColumn": 15}, {"ruleId": "245", "severity": 1, "message": "264", "line": 1, "column": 29, "nodeType": "239", "messageId": "247", "endLine": 1, "endColumn": 38}, {"ruleId": "241", "severity": 1, "message": "253", "line": 25, "column": 13, "nodeType": "243", "messageId": "244", "endLine": 25, "endColumn": 15}, {"ruleId": "237", "severity": 1, "message": "265", "line": 34, "column": 6, "nodeType": "249", "endLine": 34, "endColumn": 8, "suggestions": "266"}, {"ruleId": "241", "severity": 1, "message": "253", "line": 43, "column": 13, "nodeType": "243", "messageId": "244", "endLine": 43, "endColumn": 15}, {"ruleId": "237", "severity": 1, "message": "267", "line": 31, "column": 6, "nodeType": "249", "endLine": 31, "endColumn": 8, "suggestions": "268"}, {"ruleId": "269", "severity": 1, "message": "270", "line": 18, "column": 28, "nodeType": "239", "messageId": "271", "endLine": 18, "endColumn": 36}, {"ruleId": "237", "severity": 1, "message": "255", "line": 22, "column": 6, "nodeType": "249", "endLine": 22, "endColumn": 8, "suggestions": "272"}, {"ruleId": "237", "severity": 1, "message": "267", "line": 22, "column": 6, "nodeType": "249", "endLine": 22, "endColumn": 8, "suggestions": "273"}, {"ruleId": "237", "severity": 1, "message": "274", "line": 15, "column": 6, "nodeType": "249", "endLine": 15, "endColumn": 8, "suggestions": "275"}, {"ruleId": "237", "severity": 1, "message": "276", "line": 19, "column": 3, "nodeType": "239", "endLine": 19, "endColumn": 12, "suggestions": "277"}, {"ruleId": "245", "severity": 1, "message": "278", "line": 29, "column": 11, "nodeType": "239", "messageId": "247", "endLine": 29, "endColumn": 13}, {"ruleId": "241", "severity": 1, "message": "242", "line": 59, "column": 38, "nodeType": "243", "messageId": "244", "endLine": 59, "endColumn": 40}, {"ruleId": "241", "severity": 1, "message": "242", "line": 129, "column": 53, "nodeType": "243", "messageId": "244", "endLine": 129, "endColumn": 55}, {"ruleId": "241", "severity": 1, "message": "242", "line": 37, "column": 33, "nodeType": "243", "messageId": "244", "endLine": 37, "endColumn": 35}, {"ruleId": "241", "severity": 1, "message": "242", "line": 26, "column": 47, "nodeType": "243", "messageId": "244", "endLine": 26, "endColumn": 49}, {"ruleId": "241", "severity": 1, "message": "242", "line": 27, "column": 47, "nodeType": "243", "messageId": "244", "endLine": 27, "endColumn": 49}, {"ruleId": "237", "severity": 1, "message": "279", "line": 34, "column": 6, "nodeType": "249", "endLine": 34, "endColumn": 8, "suggestions": "280"}, {"ruleId": "241", "severity": 1, "message": "253", "line": 40, "column": 17, "nodeType": "243", "messageId": "244", "endLine": 40, "endColumn": 19}, {"ruleId": "245", "severity": 1, "message": "254", "line": 17, "column": 24, "nodeType": "239", "messageId": "247", "endLine": 17, "endColumn": 39}, {"ruleId": "237", "severity": 1, "message": "281", "line": 42, "column": 6, "nodeType": "249", "endLine": 42, "endColumn": 8, "suggestions": "282"}, {"ruleId": "241", "severity": 1, "message": "253", "line": 48, "column": 21, "nodeType": "243", "messageId": "244", "endLine": 48, "endColumn": 23}, {"ruleId": "241", "severity": 1, "message": "242", "line": 50, "column": 34, "nodeType": "243", "messageId": "244", "endLine": 50, "endColumn": 36}, {"ruleId": "241", "severity": 1, "message": "253", "line": 54, "column": 24, "nodeType": "243", "messageId": "244", "endLine": 54, "endColumn": 26}, {"ruleId": "241", "severity": 1, "message": "242", "line": 56, "column": 37, "nodeType": "243", "messageId": "244", "endLine": 56, "endColumn": 39}, {"ruleId": "241", "severity": 1, "message": "253", "line": 62, "column": 13, "nodeType": "243", "messageId": "244", "endLine": 62, "endColumn": 15}, {"ruleId": "241", "severity": 1, "message": "253", "line": 62, "column": 35, "nodeType": "243", "messageId": "244", "endLine": 62, "endColumn": 37}, {"ruleId": "241", "severity": 1, "message": "253", "line": 65, "column": 20, "nodeType": "243", "messageId": "244", "endLine": 65, "endColumn": 22}, {"ruleId": "241", "severity": 1, "message": "242", "line": 4, "column": 13, "nodeType": "243", "messageId": "244", "endLine": 4, "endColumn": 15}, {"ruleId": "283", "severity": 1, "message": "284", "line": 11, "column": 1, "nodeType": "285", "endLine": 18, "endColumn": 3}, {"ruleId": "245", "severity": 1, "message": "264", "line": 1, "column": 17, "nodeType": "239", "messageId": "247", "endLine": 1, "endColumn": 26}, {"ruleId": "241", "severity": 1, "message": "242", "line": 90, "column": 34, "nodeType": "243", "messageId": "244", "endLine": 90, "endColumn": 36}, {"ruleId": "241", "severity": 1, "message": "242", "line": 90, "column": 51, "nodeType": "243", "messageId": "244", "endLine": 90, "endColumn": 53}, {"ruleId": "241", "severity": 1, "message": "242", "line": 21, "column": 47, "nodeType": "243", "messageId": "244", "endLine": 21, "endColumn": 49}, {"ruleId": "245", "severity": 1, "message": "264", "line": 1, "column": 17, "nodeType": "239", "messageId": "247", "endLine": 1, "endColumn": 26}, {"ruleId": "245", "severity": 1, "message": "286", "line": 8, "column": 8, "nodeType": "239", "messageId": "247", "endLine": 8, "endColumn": 19}, {"ruleId": "245", "severity": 1, "message": "287", "line": 14, "column": 10, "nodeType": "239", "messageId": "247", "endLine": 14, "endColumn": 15}, "react-hooks/exhaustive-deps", "React Hook useEffect contains a call to 'setKeyword'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [keywordParam] as a second argument to the useEffect Hook.", "Identifier", ["288"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "unusedVar", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["289"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["290"], "Expected '!==' and instead saw '!='.", "'setSearchParams' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate', 'redirect', and 'userInfo'. Either include them or remove the dependency array.", ["291"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "292", "text": "293"}, "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["294"], "'useEffect' is defined but never used.", "React Hook useEffect has missing dependencies: 'id' and 'loadProduct'. Either include them or remove the dependency array.", ["295"], "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["296"], "no-const-assign", "'redirect' is constant.", "const", ["297"], ["298"], "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["299"], "React Hook useEffect contains a call to 'setError'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [success, id, payment_intent] as a second argument to the useEffect Hook.", ["300"], "'id' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'id' and 'logout'. Either include them or remove the dependency array.", ["301"], "React Hook useEffect has missing dependencies: 'brandParam', 'categoryParam', and 'loadProducts'. Either include them or remove the dependency array.", ["302"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", {"desc": "303", "fix": "304"}, {"desc": "305", "fix": "306"}, {"desc": "305", "fix": "307"}, {"desc": "308", "fix": "309"}, [447, 447], " rel=\"noreferrer\"", {"desc": "310", "fix": "311"}, {"desc": "312", "fix": "313"}, {"desc": "314", "fix": "315"}, {"desc": "308", "fix": "316"}, {"desc": "314", "fix": "317"}, {"desc": "318", "fix": "319"}, {"desc": "320", "fix": "321"}, {"desc": "322", "fix": "323"}, {"desc": "324", "fix": "325"}, "Add dependencies array: [keywordParam]", {"range": "326", "text": "327"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "328", "text": "329"}, {"range": "330", "text": "329"}, "Update the dependencies array to be: [navigate, redirect, userInfo]", {"range": "331", "text": "332"}, "Update the dependencies array to be: [loadProducts]", {"range": "333", "text": "334"}, "Update the dependencies array to be: [id, loadProduct]", {"range": "335", "text": "336"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "337", "text": "338"}, {"range": "339", "text": "332"}, {"range": "340", "text": "338"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "341", "text": "342"}, "Add dependencies array: [success, id, payment_intent]", {"range": "343", "text": "344"}, "Update the dependencies array to be: [id, logout]", {"range": "345", "text": "346"}, "Update the dependencies array to be: [brandParam, categoryParam, loadProducts]", {"range": "347", "text": "348"}, [1313, 1313], ", [keywordParam]", [2620, 2622], "[authTokens, refresh]", [2996, 3008], [810, 812], "[navigate, redirect, userInfo]", [782, 784], "[loadProducts]", [1011, 1013], "[id, loadProduct]", [1168, 1170], "[navigate, userInfo]", [833, 835], [858, 860], [491, 493], "[logout, navigate, userInfo]", [1352, 1352], ", [success, id, payment_intent]", [1286, 1288], "[id, logout]", [1431, 1433], "[brandParam, categoryParam, loadProducts]"]