{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\pages\\\\registerPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useEffect } from \"react\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { Form, Button, Row, Col } from \"react-bootstrap\";\nimport Message from \"../components/message\";\nimport UserContext from \"../context/userContext\";\nimport FormContainer from \"../components/formContainer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RegisterPage(props) {\n  _s();\n  const [username, setUsername] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const {\n    userInfo,\n    register,\n    error\n  } = useContext(UserContext);\n  const navigate = useNavigate();\n  const redirect = window.location.search ? window.location.search.split(\"=\")[1] : \"/\";\n  if (redirect[0] !== \"/\") redirect = `/${redirect}`;\n  useEffect(() => {\n    if (userInfo && userInfo.username) navigate(redirect);\n  }, []);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const status = await register(username, email, password);\n    if (status) navigate(redirect);\n  };\n  return /*#__PURE__*/_jsxDEV(FormContainer, {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Register\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), error.register && error.register.detail && /*#__PURE__*/_jsxDEV(Message, {\n      variant: \"danger\",\n      children: /*#__PURE__*/_jsxDEV(\"h4\", {\n        children: error.register.detail\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n        controlId: \"username\",\n        className: \"my-2\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n          required: true,\n          type: \"text\",\n          placeholder: \"Enter Username\",\n          value: username,\n          onChange: e => {\n            setUsername(e.currentTarget.value);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n          children: error.register && error.register.username && /*#__PURE__*/_jsxDEV(Message, {\n            variant: \"danger\",\n            children: error.register.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n        controlId: \"email\",\n        className: \"my-2\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n          required: true,\n          type: \"email\",\n          placeholder: \"Enter Email\",\n          value: email,\n          onChange: e => {\n            setEmail(e.currentTarget.value);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n          children: error.register && error.register.email && /*#__PURE__*/_jsxDEV(Message, {\n            variant: \"danger\",\n            children: error.register.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n        controlId: \"password\",\n        className: \"my-2\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n          required: true,\n          type: \"password\",\n          placeholder: \"Enter Password\",\n          value: password,\n          onChange: e => {\n            setPassword(e.currentTarget.value);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n          children: error.register && error.register.password && /*#__PURE__*/_jsxDEV(Message, {\n            variant: \"danger\",\n            children: error.register.password\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"submit\",\n        variant: \"primary\",\n        className: \"my-2\",\n        children: \"Register\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"py-3\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [\"Already Registered?\", /*#__PURE__*/_jsxDEV(Link, {\n          to: redirect ? `/login?redirect=${redirect}` : \"/login\",\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n}\n_s(RegisterPage, \"KSzUbNJSIc7ruVSZtkrwY+6sqBQ=\", false, function () {\n  return [useNavigate];\n});\n_c = RegisterPage;\nexport default RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useEffect", "Link", "useNavigate", "Form", "<PERSON><PERSON>", "Row", "Col", "Message", "UserContext", "FormContainer", "jsxDEV", "_jsxDEV", "RegisterPage", "props", "_s", "username", "setUsername", "email", "setEmail", "password", "setPassword", "userInfo", "register", "error", "navigate", "redirect", "window", "location", "search", "split", "handleSubmit", "e", "preventDefault", "status", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "detail", "variant", "onSubmit", "Group", "controlId", "className", "Label", "Control", "required", "type", "placeholder", "value", "onChange", "currentTarget", "Text", "to", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/pages/registerPage.jsx"], "sourcesContent": ["import React, { useState, useContext, useEffect } from \"react\";\nimport { Link, useNavigate } from \"react-router-dom\";\nimport { Form, Button, Row, Col } from \"react-bootstrap\";\nimport Message from \"../components/message\";\nimport UserContext from \"../context/userContext\";\nimport FormContainer from \"../components/formContainer\";\n\nfunction RegisterPage(props) {\n  const [username, setUsername] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const { userInfo, register, error } = useContext(UserContext);\n  const navigate = useNavigate();\n  const redirect = window.location.search\n    ? window.location.search.split(\"=\")[1]\n    : \"/\";\n\n  if (redirect[0] !== \"/\") redirect = `/${redirect}`;\n\n  useEffect(() => {\n    if (userInfo && userInfo.username) navigate(redirect);\n  }, []);\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    const status = await register(username, email, password);\n    if (status) navigate(redirect);\n  };\n\n  return (\n    <FormContainer>\n      <h1>Register</h1>\n      {error.register && error.register.detail && (\n        <Message variant=\"danger\">\n          <h4>{error.register.detail}</h4>\n        </Message>\n      )}\n      <Form onSubmit={handleSubmit}>\n        <Form.Group controlId=\"username\" className=\"my-2\">\n          <Form.Label>Username</Form.Label>\n          <Form.Control\n            required\n            type=\"text\"\n            placeholder=\"Enter Username\"\n            value={username}\n            onChange={(e) => {\n              setUsername(e.currentTarget.value);\n            }}\n          ></Form.Control>\n          <Form.Text>\n            {error.register && error.register.username && (\n              <Message variant=\"danger\">{error.register.username}</Message>\n            )}\n          </Form.Text>\n        </Form.Group>\n        <Form.Group controlId=\"email\" className=\"my-2\">\n          <Form.Label>Email</Form.Label>\n          <Form.Control\n            required\n            type=\"email\"\n            placeholder=\"Enter Email\"\n            value={email}\n            onChange={(e) => {\n              setEmail(e.currentTarget.value);\n            }}\n          ></Form.Control>\n          <Form.Text>\n            {error.register && error.register.email && (\n              <Message variant=\"danger\">{error.register.email}</Message>\n            )}\n          </Form.Text>\n        </Form.Group>\n        <Form.Group controlId=\"password\" className=\"my-2\">\n          <Form.Label>Password</Form.Label>\n          <Form.Control\n            required\n            type=\"password\"\n            placeholder=\"Enter Password\"\n            value={password}\n            onChange={(e) => {\n              setPassword(e.currentTarget.value);\n            }}\n          ></Form.Control>\n          <Form.Text>\n            {error.register && error.register.password && (\n              <Message variant=\"danger\">{error.register.password}</Message>\n            )}\n          </Form.Text>\n        </Form.Group>\n        <Button type=\"submit\" variant=\"primary\" className=\"my-2\">\n          Register\n        </Button>\n      </Form>\n      <Row className=\"py-3\">\n        <Col>\n          Already Registered?\n          <Link to={redirect ? `/login?redirect=${redirect}` : \"/login\"}>\n            Login\n          </Link>\n        </Col>\n      </Row>\n    </FormContainer>\n  );\n}\n\nexport default RegisterPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC9D,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AACxD,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,aAAa,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,SAASC,YAAYA,CAACC,KAAK,EAAE;EAAAC,EAAA;EAC3B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM;IAAEuB,QAAQ;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAGxB,UAAU,CAACS,WAAW,CAAC;EAC7D,MAAMgB,QAAQ,GAAGtB,WAAW,EAAE;EAC9B,MAAMuB,QAAQ,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,GACnCF,MAAM,CAACC,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACpC,GAAG;EAEP,IAAIJ,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAEA,QAAQ,GAAI,IAAGA,QAAS,EAAC;EAElDzB,SAAS,CAAC,MAAM;IACd,IAAIqB,QAAQ,IAAIA,QAAQ,CAACN,QAAQ,EAAES,QAAQ,CAACC,QAAQ,CAAC;EACvD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,EAAE;IAClB,MAAMC,MAAM,GAAG,MAAMX,QAAQ,CAACP,QAAQ,EAAEE,KAAK,EAAEE,QAAQ,CAAC;IACxD,IAAIc,MAAM,EAAET,QAAQ,CAACC,QAAQ,CAAC;EAChC,CAAC;EAED,oBACEd,OAAA,CAACF,aAAa;IAAAyB,QAAA,gBACZvB,OAAA;MAAAuB,QAAA,EAAI;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAK,EAChBf,KAAK,CAACD,QAAQ,IAAIC,KAAK,CAACD,QAAQ,CAACiB,MAAM,iBACtC5B,OAAA,CAACJ,OAAO;MAACiC,OAAO,EAAC,QAAQ;MAAAN,QAAA,eACvBvB,OAAA;QAAAuB,QAAA,EAAKX,KAAK,CAACD,QAAQ,CAACiB;MAAM;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAM;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAEnC,eACD3B,OAAA,CAACR,IAAI;MAACsC,QAAQ,EAAEX,YAAa;MAAAI,QAAA,gBAC3BvB,OAAA,CAACR,IAAI,CAACuC,KAAK;QAACC,SAAS,EAAC,UAAU;QAACC,SAAS,EAAC,MAAM;QAAAV,QAAA,gBAC/CvB,OAAA,CAACR,IAAI,CAAC0C,KAAK;UAAAX,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACjC3B,OAAA,CAACR,IAAI,CAAC2C,OAAO;UACXC,QAAQ;UACRC,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,gBAAgB;UAC5BC,KAAK,EAAEnC,QAAS;UAChBoC,QAAQ,EAAGpB,CAAC,IAAK;YACff,WAAW,CAACe,CAAC,CAACqB,aAAa,CAACF,KAAK,CAAC;UACpC;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACY,eAChB3B,OAAA,CAACR,IAAI,CAACkD,IAAI;UAAAnB,QAAA,EACPX,KAAK,CAACD,QAAQ,IAAIC,KAAK,CAACD,QAAQ,CAACP,QAAQ,iBACxCJ,OAAA,CAACJ,OAAO;YAACiC,OAAO,EAAC,QAAQ;YAAAN,QAAA,EAAEX,KAAK,CAACD,QAAQ,CAACP;UAAQ;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACnD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACS;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD,eACb3B,OAAA,CAACR,IAAI,CAACuC,KAAK;QAACC,SAAS,EAAC,OAAO;QAACC,SAAS,EAAC,MAAM;QAAAV,QAAA,gBAC5CvB,OAAA,CAACR,IAAI,CAAC0C,KAAK;UAAAX,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eAC9B3B,OAAA,CAACR,IAAI,CAAC2C,OAAO;UACXC,QAAQ;UACRC,IAAI,EAAC,OAAO;UACZC,WAAW,EAAC,aAAa;UACzBC,KAAK,EAAEjC,KAAM;UACbkC,QAAQ,EAAGpB,CAAC,IAAK;YACfb,QAAQ,CAACa,CAAC,CAACqB,aAAa,CAACF,KAAK,CAAC;UACjC;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACY,eAChB3B,OAAA,CAACR,IAAI,CAACkD,IAAI;UAAAnB,QAAA,EACPX,KAAK,CAACD,QAAQ,IAAIC,KAAK,CAACD,QAAQ,CAACL,KAAK,iBACrCN,OAAA,CAACJ,OAAO;YAACiC,OAAO,EAAC,QAAQ;YAAAN,QAAA,EAAEX,KAAK,CAACD,QAAQ,CAACL;UAAK;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAChD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACS;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD,eACb3B,OAAA,CAACR,IAAI,CAACuC,KAAK;QAACC,SAAS,EAAC,UAAU;QAACC,SAAS,EAAC,MAAM;QAAAV,QAAA,gBAC/CvB,OAAA,CAACR,IAAI,CAAC0C,KAAK;UAAAX,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAa,eACjC3B,OAAA,CAACR,IAAI,CAAC2C,OAAO;UACXC,QAAQ;UACRC,IAAI,EAAC,UAAU;UACfC,WAAW,EAAC,gBAAgB;UAC5BC,KAAK,EAAE/B,QAAS;UAChBgC,QAAQ,EAAGpB,CAAC,IAAK;YACfX,WAAW,CAACW,CAAC,CAACqB,aAAa,CAACF,KAAK,CAAC;UACpC;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACY,eAChB3B,OAAA,CAACR,IAAI,CAACkD,IAAI;UAAAnB,QAAA,EACPX,KAAK,CAACD,QAAQ,IAAIC,KAAK,CAACD,QAAQ,CAACH,QAAQ,iBACxCR,OAAA,CAACJ,OAAO;YAACiC,OAAO,EAAC,QAAQ;YAAAN,QAAA,EAAEX,KAAK,CAACD,QAAQ,CAACH;UAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACnD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACS;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD,eACb3B,OAAA,CAACP,MAAM;QAAC4C,IAAI,EAAC,QAAQ;QAACR,OAAO,EAAC,SAAS;QAACI,SAAS,EAAC,MAAM;QAAAV,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAS;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACJ,eACP3B,OAAA,CAACN,GAAG;MAACuC,SAAS,EAAC,MAAM;MAAAV,QAAA,eACnBvB,OAAA,CAACL,GAAG;QAAA4B,QAAA,GAAC,qBAEH,eAAAvB,OAAA,CAACV,IAAI;UAACqD,EAAE,EAAE7B,QAAQ,GAAI,mBAAkBA,QAAS,EAAC,GAAG,QAAS;UAAAS,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAO;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACQ;AAEpB;AAACxB,EAAA,CAhGQF,YAAY;EAAA,QAKFV,WAAW;AAAA;AAAAqD,EAAA,GALrB3C,YAAY;AAkGrB,eAAeA,YAAY;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}