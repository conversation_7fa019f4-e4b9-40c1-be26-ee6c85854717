{"ast": null, "code": "import * as React from 'react';\nconst SelectableContext = /*#__PURE__*/React.createContext(null);\nexport const makeEventKey = function (eventKey) {\n  let href = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  if (eventKey != null) return String(eventKey);\n  return href || null;\n};\nexport default SelectableContext;", "map": {"version": 3, "names": ["React", "SelectableContext", "createContext", "makeEventKey", "eventKey", "href", "arguments", "length", "undefined", "String"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/node_modules/@restart/ui/esm/SelectableContext.js"], "sourcesContent": ["import * as React from 'react';\nconst SelectableContext = /*#__PURE__*/React.createContext(null);\nexport const makeEventKey = (eventKey, href = null) => {\n  if (eventKey != null) return String(eventKey);\n  return href || null;\n};\nexport default SelectableContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAChE,OAAO,MAAMC,YAAY,GAAG,SAAAA,CAACC,QAAQ,EAAkB;EAAA,IAAhBC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAChD,IAAIF,QAAQ,IAAI,IAAI,EAAE,OAAOK,MAAM,CAACL,QAAQ,CAAC;EAC7C,OAAOC,IAAI,IAAI,IAAI;AACrB,CAAC;AACD,eAAeJ,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}